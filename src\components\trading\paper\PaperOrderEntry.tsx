
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, Target, Calendar, DollarSign } from "lucide-react";
import { paperTradingService } from '@/services/PaperTradingService';
import { useToast } from "@/hooks/use-toast";

export const PaperOrderEntry: React.FC = () => {
  const [orderData, setOrderData] = useState({
    symbol: '',
    type: 'BUY' as 'BUY' | 'SELL',
    orderType: 'MARKET' as 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT',
    quantity: '',
    price: '',
    stopPrice: '',
    strategy: 'INTRADAY' as 'INTRADAY' | 'SWING',
    instrumentType: 'EQUITY' as 'EQUITY' | 'OPTION'
  });

  const [optionData, setOptionData] = useState({
    strike: '',
    expiry: '',
    optionType: 'CALL' as 'CALL' | 'PUT',
    premium: ''
  });

  const { toast } = useToast();

  const handlePlaceOrder = () => {
    if (!orderData.symbol || !orderData.quantity || !orderData.price) {
      toast({
        title: "Validation Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    const order = {
      symbol: orderData.symbol,
      type: orderData.type,
      orderType: orderData.orderType,
      quantity: parseInt(orderData.quantity),
      price: parseFloat(orderData.price),
      stopPrice: orderData.stopPrice ? parseFloat(orderData.stopPrice) : undefined,
      instrumentType: orderData.instrumentType,
      strategy: orderData.strategy,
      optionDetails: orderData.instrumentType === 'OPTION' ? {
        strike: parseFloat(optionData.strike),
        expiry: new Date(optionData.expiry),
        optionType: optionData.optionType,
        premium: parseFloat(optionData.premium)
      } : undefined
    };

    const orderId = paperTradingService.placeOrder(order);
    
    toast({
      title: "Order Placed",
      description: `${orderData.type} order for ${orderData.quantity} ${orderData.symbol} placed successfully`,
    });

    // Reset form
    setOrderData({
      symbol: '',
      type: 'BUY',
      orderType: 'MARKET',
      quantity: '',
      price: '',
      stopPrice: '',
      strategy: 'INTRADAY',
      instrumentType: 'EQUITY'
    });
    setOptionData({
      strike: '',
      expiry: '',
      optionType: 'CALL',
      premium: ''
    });
  };

  return (
    <div className="space-y-6">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Paper Trading Order Entry
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={orderData.instrumentType} onValueChange={(value) => setOrderData(prev => ({ ...prev, instrumentType: value as 'EQUITY' | 'OPTION' }))}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="EQUITY">Equity Trading</TabsTrigger>
              <TabsTrigger value="OPTION">Options Trading</TabsTrigger>
            </TabsList>

            <TabsContent value="EQUITY" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="symbol">Symbol</Label>
                  <Input
                    id="symbol"
                    placeholder="e.g., RELIANCE, TCS, NIFTY"
                    value={orderData.symbol}
                    onChange={(e) => setOrderData(prev => ({ ...prev, symbol: e.target.value.toUpperCase() }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="strategy">Strategy</Label>
                  <Select value={orderData.strategy} onValueChange={(value) => setOrderData(prev => ({ ...prev, strategy: value as 'INTRADAY' | 'SWING' }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INTRADAY">
                        <div className="flex items-center">
                          <Target className="h-4 w-4 mr-2 text-yellow-400" />
                          Intraday
                        </div>
                      </SelectItem>
                      <SelectItem value="SWING">
                        <div className="flex items-center">
                          <TrendingUp className="h-4 w-4 mr-2 text-green-400" />
                          Swing
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Order Side</Label>
                  <Select value={orderData.type} onValueChange={(value) => setOrderData(prev => ({ ...prev, type: value as 'BUY' | 'SELL' }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BUY">
                        <Badge className="bg-green-600">BUY</Badge>
                      </SelectItem>
                      <SelectItem value="SELL">
                        <Badge className="bg-red-600">SELL</Badge>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="orderType">Order Type</Label>
                  <Select value={orderData.orderType} onValueChange={(value) => setOrderData(prev => ({ ...prev, orderType: value as any }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MARKET">Market</SelectItem>
                      <SelectItem value="LIMIT">Limit</SelectItem>
                      <SelectItem value="STOP">Stop</SelectItem>
                      <SelectItem value="STOP_LIMIT">Stop Limit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    placeholder="Enter quantity"
                    value={orderData.quantity}
                    onChange={(e) => setOrderData(prev => ({ ...prev, quantity: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">Price (₹)</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.05"
                    placeholder="Enter price"
                    value={orderData.price}
                    onChange={(e) => setOrderData(prev => ({ ...prev, price: e.target.value }))}
                  />
                </div>
              </div>

              {(orderData.orderType === 'STOP' || orderData.orderType === 'STOP_LIMIT') && (
                <div className="space-y-2">
                  <Label htmlFor="stopPrice">Stop Price (₹)</Label>
                  <Input
                    id="stopPrice"
                    type="number"
                    step="0.05"
                    placeholder="Enter stop price"
                    value={orderData.stopPrice}
                    onChange={(e) => setOrderData(prev => ({ ...prev, stopPrice: e.target.value }))}
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="OPTION" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="optionSymbol">Underlying Symbol</Label>
                  <Input
                    id="optionSymbol"
                    placeholder="e.g., NIFTY, BANKNIFTY"
                    value={orderData.symbol}
                    onChange={(e) => setOrderData(prev => ({ ...prev, symbol: e.target.value.toUpperCase() }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="optionStrategy">Strategy</Label>
                  <Select value={orderData.strategy} onValueChange={(value) => setOrderData(prev => ({ ...prev, strategy: value as 'INTRADAY' | 'SWING' }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INTRADAY">Intraday Options</SelectItem>
                      <SelectItem value="SWING">Swing Options</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="optionType">Option Type</Label>
                  <Select value={optionData.optionType} onValueChange={(value) => setOptionData(prev => ({ ...prev, optionType: value as 'CALL' | 'PUT' }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CALL">
                        <Badge className="bg-green-600">CALL</Badge>
                      </SelectItem>
                      <SelectItem value="PUT">
                        <Badge className="bg-red-600">PUT</Badge>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="strike">Strike Price</Label>
                  <Input
                    id="strike"
                    type="number"
                    placeholder="Enter strike price"
                    value={optionData.strike}
                    onChange={(e) => setOptionData(prev => ({ ...prev, strike: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input
                    id="expiry"
                    type="date"
                    value={optionData.expiry}
                    onChange={(e) => setOptionData(prev => ({ ...prev, expiry: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="premium">Premium (₹)</Label>
                  <Input
                    id="premium"
                    type="number"
                    step="0.05"
                    placeholder="Enter premium"
                    value={optionData.premium}
                    onChange={(e) => setOptionData(prev => ({ ...prev, premium: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lots">Lots</Label>
                  <Input
                    id="lots"
                    type="number"
                    placeholder="Enter number of lots"
                    value={orderData.quantity}
                    onChange={(e) => setOrderData(prev => ({ ...prev, quantity: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="orderSide">Order Side</Label>
                  <Select value={orderData.type} onValueChange={(value) => setOrderData(prev => ({ ...prev, type: value as 'BUY' | 'SELL' }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="BUY">Buy to Open</SelectItem>
                      <SelectItem value="SELL">Sell to Open</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-between items-center pt-6 border-t border-trading-border">
            <div className="text-sm text-trading-muted">
              This is paper trading - no real money will be used
            </div>
            <Button onClick={handlePlaceOrder} className="bg-blue-600 hover:bg-blue-700">
              Place Paper Order
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
