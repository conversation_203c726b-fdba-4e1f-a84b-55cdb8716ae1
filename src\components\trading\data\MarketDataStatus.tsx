
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Wifi, WifiOff, AlertCircle, RefreshCw, Zap } from "lucide-react";
import { useMarketData } from "./MarketDataProvider";
import { useMarketStore } from "../../../store/useMarketStore";
import { realTimeDataService } from "../../../services/RealTimeDataService";

export const MarketDataStatus = () => {
  const { isConnected, connectionStatus, latency } = useMarketData();
  const subscribedSymbols = useMarketStore(state => state.subscribedSymbols);

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="h-4 w-4 text-green-400" />;
      case 'connecting':
        return <RefreshCw className="h-4 w-4 text-yellow-400 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-400 border-green-400';
      case 'connecting':
        return 'text-yellow-400 border-yellow-400';
      case 'error':
        return 'text-red-400 border-red-400';
      default:
        return 'text-gray-400 border-gray-400';
    }
  };

  const getLatencyColor = () => {
    if (latency < 50) return 'text-green-400';
    if (latency < 100) return 'text-yellow-400';
    return 'text-red-400';
  };

  const handleReconnect = async () => {
    try {
      await realTimeDataService.connect();
    } catch (error) {
      console.error('Reconnection failed:', error);
    }
  };

  return (
    <Card className="glass-trading-panel">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-trading-light flex items-center">
          {getStatusIcon()}
          <span className="ml-2">Market Data Feed</span>
          {isConnected && <Zap className="h-3 w-3 ml-2 text-green-400 animate-pulse" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-trading-muted">Status</span>
          <Badge variant="outline" className={`text-xs ${getStatusColor()}`}>
            {connectionStatus.toUpperCase()}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-xs text-trading-muted">Connection</span>
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="text-center">
            <div className={`text-trading-light font-medium ${getLatencyColor()}`}>
              {latency > 0 ? `${latency}ms` : '--'}
            </div>
            <div className="text-trading-muted">Latency</div>
          </div>
          <div className="text-center">
            <div className="text-trading-light font-medium">{subscribedSymbols.size}</div>
            <div className="text-trading-muted">Symbols</div>
          </div>
        </div>

        <div className="space-y-1">
          <div className="text-xs text-trading-muted">Data Quality</div>
          <div className="flex space-x-1">
            <div className={`h-1 flex-1 rounded ${isConnected ? 'bg-green-400' : 'bg-gray-600'}`}></div>
            <div className={`h-1 flex-1 rounded ${latency < 100 ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
            <div className={`h-1 flex-1 rounded ${subscribedSymbols.size > 0 ? 'bg-green-400' : 'bg-gray-600'}`}></div>
          </div>
        </div>

        {(connectionStatus === 'error' || connectionStatus === 'disconnected') && (
          <Button 
            size="sm" 
            variant="outline" 
            className="w-full text-xs glass-button"
            onClick={handleReconnect}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Reconnect
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
