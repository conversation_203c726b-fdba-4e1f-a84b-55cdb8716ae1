
export interface TradingStrategy {
  id: string;
  name: string;
  category: 'Price Action' | 'Options' | 'Swing' | 'Intraday' | 'Scalping';
  timeframe: '1m' | '5m' | '15m' | '30m' | '1h' | 'Daily';
  indicators: string[];
  criteria: string[];
  riskLevel: 'Low' | 'Medium' | 'High';
  winRate?: number; // Now optional, comes from real data
  avgReturn?: number; // Now optional, comes from real data
  isActive: boolean;
  confidence?: number; // Now optional, comes from real data
  lastSignal?: Date;
  totalSignals?: number; // Now optional, comes from real data
}

export interface StrategyPerformance {
  winRate: number;
  avgReturn: number;
  confidence: number;
  totalSignals: number;
  totalTrades: number;
  pnl: number;
  successRate: number;
  lastUpdated: Date;
}

export interface StrategySignal {
  strategyId: string;
  symbol: string;
  timestamp: Date;
  price: number;
  confidence: number;
  direction: 'Long' | 'Short';
  targetPrice?: number;
  stopLoss?: number;
  volume: number;
}

export interface ScannerFilter {
  category?: string[];
  timeframe?: string[];
  minConfidence?: number;
  minWinRate?: number;
  riskLevel?: string[];
  indicators?: string[];
}

export interface StrategyConfig {
  strategy: TradingStrategy;
  performance?: StrategyPerformance;
  isActive: boolean;
  configuration?: Record<string, any>;
}
