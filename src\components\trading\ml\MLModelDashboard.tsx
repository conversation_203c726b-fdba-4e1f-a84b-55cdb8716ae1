
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Brain, Zap, Target, Activity, Play } from "lucide-react";

interface MLModel {
  name: string;
  type: string;
  accuracy: number;
  status: 'active' | 'training' | 'stopped';
  trades: number;
  profit: number;
  lastTrained: string;
}

interface TrainingMetric {
  epoch: number;
  loss: number;
  accuracy: number;
  valLoss: number;
}

interface MLModelDashboardProps {
  models?: MLModel[];
  trainingMetrics?: TrainingMetric[];
  activeModels?: number;
  avgAccuracy?: number;
  totalTrades?: number;
  combinedProfit?: number;
}

export const MLModelDashboard = ({
  models = [],
  trainingMetrics = [],
  activeModels = 0,
  avgAccuracy = 0,
  totalTrades = 0,
  combinedProfit = 0
}: MLModelDashboardProps) => {

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            ML Model Management System
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Active Models</div>
              <div className="text-2xl font-bold text-blue-400">{activeModels}</div>
              <div className="text-xs text-green-400">Training: {models.filter(m => m.status === 'training').length}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Avg Accuracy</div>
              <div className="text-2xl font-bold text-green-400">{avgAccuracy.toFixed(1)}%</div>
              <div className="text-xs text-green-400">Weekly trend</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Total Trades</div>
              <div className="text-2xl font-bold text-trading-light">{totalTrades.toLocaleString()}</div>
              <div className="text-xs text-trading-muted">Across all models</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Combined Profit</div>
              <div className="text-2xl font-bold text-green-400">+{combinedProfit.toFixed(1)}%</div>
              <div className="text-xs text-green-400">This quarter</div>
            </div>
          </div>

          <div className="flex space-x-2 mb-4">
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Play className="h-4 w-4 mr-2" />
              Train New Model
            </Button>
            <Button variant="outline" size="sm">
              Import Model
            </Button>
            <Button variant="outline" size="sm">
              Export Weights
            </Button>
            <Button variant="outline" size="sm">
              Model Comparison
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="models" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="models" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            Models
          </TabsTrigger>
          <TabsTrigger value="training" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Training
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="models">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Model Repository</CardTitle>
            </CardHeader>
            <CardContent>
              {models.length > 0 ? (
                <div className="space-y-4">
                  {models.map((model, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded-lg border border-trading-border">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h4 className="text-lg font-medium text-trading-light">{model.name}</h4>
                          <Badge variant="outline" className="text-blue-400 border-blue-400">
                            {model.type}
                          </Badge>
                          <Badge variant="outline" className={
                            model.status === "active" ? "text-green-400 border-green-400" : 
                            model.status === "training" ? "text-yellow-400 border-yellow-400" : 
                            "text-red-400 border-red-400"
                          }>
                            {model.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">View Details</Button>
                          <Button size="sm">
                            {model.status === "training" ? "Monitor" : "Deploy"}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Accuracy</div>
                          <div className="text-lg font-bold text-green-400">{model.accuracy}%</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Total Trades</div>
                          <div className="text-lg font-bold text-trading-light">{model.trades}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Profit</div>
                          <div className="text-lg font-bold text-green-400">+{model.profit}%</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Last Trained</div>
                          <div className="text-xs text-trading-muted">{model.lastTrained}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Status</div>
                          <div className="w-full bg-trading-darker rounded-full h-1 mt-1">
                            <div 
                              className={`h-1 rounded-full ${
                                model.status === "active" ? "bg-green-400" : 
                                model.status === "training" ? "bg-yellow-400" : "bg-red-400"
                              }`}
                              style={{ width: `${model.accuracy}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-trading-muted">
                  <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No ML models available</p>
                  <p className="text-sm mt-1">Train your first model to get started</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Training Progress</CardTitle>
            </CardHeader>
            <CardContent>
              {trainingMetrics.length > 0 ? (
                <div className="space-y-4">
                  <div className="p-4 bg-trading-dark rounded border border-trading-border">
                    <h4 className="text-lg font-medium text-trading-light mb-3">Model Training Progress</h4>
                    <div className="space-y-3">
                      {trainingMetrics.map((metric, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-trading-darker rounded">
                          <span className="text-sm text-trading-light">Epoch {metric.epoch}</span>
                          <div className="flex space-x-4 text-xs">
                            <span className="text-trading-muted">Loss: <span className="text-red-400">{metric.loss}</span></span>
                            <span className="text-trading-muted">Accuracy: <span className="text-green-400">{metric.accuracy}%</span></span>
                            <span className="text-trading-muted">Val Loss: <span className="text-yellow-400">{metric.valLoss}</span></span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4">
                      <div className="text-xs text-trading-muted mb-1">Training Progress: {Math.min(trainingMetrics.length * 20, 100)}%</div>
                      <div className="w-full bg-trading-darker rounded-full h-2">
                        <div className="bg-blue-400 h-2 rounded-full" style={{ width: `${Math.min(trainingMetrics.length * 20, 100)}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-trading-muted">
                  <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No training in progress</p>
                  <p className="text-sm mt-1">Start training a model to see progress here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Model Performance Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-trading-dark border border-trading-border rounded-lg flex items-center justify-center">
                <div className="text-center text-trading-muted">
                  <Activity className="h-12 w-12 mx-auto mb-2" />
                  <div className="text-lg">Performance Charts</div>
                  <div className="text-sm">Connect models to view accuracy trends and risk metrics</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
