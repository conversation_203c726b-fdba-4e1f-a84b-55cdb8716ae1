
# Advanced Trading Platform Frontend

A comprehensive trading platform built with React, TypeScript, and modern UI components. This platform provides real-time market data, advanced order management, position tracking, and AI-powered trading insights.

## 🚀 Features

### Core Trading Features
- **Real-time Market Data**: Live price feeds via WebSocket connections
- **Advanced Order Management**: Market, Limit, Stop Loss, and Bracket orders
- **Position Management**: Real-time P&L tracking and position monitoring
- **Quick Trade Widgets**: Fast order placement with one-click trading
- **Market Heatmap**: Visual sector performance overview
- **Trading Alerts**: Real-time notifications and signal alerts

### Market Data & Analytics
- **Live Price Widgets**: Real-time price updates for multiple symbols
- **Market Status Dashboard**: Index tracking and sector performance
- **Trading Alerts System**: Customizable notifications with filtering
- **Market Heatmap**: Visual representation of sector movements
- **Connection Status Monitoring**: Real-time feed health tracking

### AI & Strategy Features
- **Strategy Scanner**: Automated pattern detection and signal generation
- **AI Agents Dashboard**: Multiple specialized trading agents
- **Conversational AI**: Natural language trading assistance
- **Backtesting Engine**: Historical strategy performance testing
- **Risk Management**: Position sizing and risk assessment tools

### Technical Analysis
- **Advanced Charting**: Interactive charts with technical indicators
- **Scanner Dashboard**: Multi-timeframe market scanning
- **Strategy Builder**: Visual strategy creation interface
- **Performance Analytics**: Detailed trading performance metrics

## 📊 API Documentation

### REST API Endpoints

#### Market Data
```
GET /api/market/quote/{symbol}
- Description: Get current quote for a symbol
- Response: Current price, change, volume, high, low, open

GET /api/market/history/{symbol}
- Description: Get historical data for a symbol
- Parameters: symbol, timeframe, from, to
- Response: OHLCV data array

GET /api/market/instruments
- Description: Get list of available trading instruments
- Response: Array of instrument details

POST /api/market/subscribe
- Description: Subscribe to real-time market data
- Body: { symbols: string[], types: string[] }
- Response: Subscription confirmation
```

#### Trading Operations
```
POST /api/orders/place
- Description: Place a new trading order
- Body: { symbol, type, orderType, quantity, price?, stopLoss?, target? }
- Response: Order confirmation with order ID

GET /api/orders/active
- Description: Get all active orders
- Response: Array of pending/partial orders

PUT /api/orders/{orderId}/cancel
- Description: Cancel an existing order
- Response: Cancellation confirmation

GET /api/positions
- Description: Get current positions
- Response: Array of open positions with P&L

GET /api/portfolio/summary
- Description: Get portfolio overview
- Response: Total value, P&L, day change, positions count
```

#### Strategy & Scanning
```
POST /api/scanner/run
- Description: Execute market scan with filters
- Body: { filters: ScannerFilter, strategy?: string }
- Response: Array of matching symbols with signals

GET /api/strategies
- Description: Get available trading strategies
- Response: Array of strategy definitions

POST /api/backtest/run
- Description: Run strategy backtest
- Body: { strategy, symbol, timeframe, period }
- Response: Backtest results with metrics
```

### WebSocket API

#### Connection
```
WebSocket URL: ws://localhost:8080/ws
Protocol: JSON-based messaging
Authentication: API key via connection params
```

#### Message Format
```json
{
  "action": "subscribe|unsubscribe|ping",
  "symbol": "NIFTY",
  "type": "live|historical|orders|positions",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Real-time Data Streams
```javascript
// Market Data Subscription
{
  "action": "subscribe",
  "symbols": ["NIFTY", "SENSEX", "RELIANCE"],
  "types": ["price", "volume", "trades"]
}

// Order Updates
{
  "action": "subscribe",
  "type": "orders",
  "userId": "user123"
}

// Position Updates
{
  "action": "subscribe",
  "type": "positions",
  "userId": "user123"
}
```

#### Market Data Response
```json
{
  "symbol": "NIFTY",
  "price": 24157.81,
  "change": 165.42,
  "changePercent": 0.69,
  "volume": **********,
  "high": 24200.50,
  "low": 23950.25,
  "open": 24000.00,
  "timestamp": "2024-01-01T09:30:00Z"
}
```

#### Order Update Response
```json
{
  "orderId": "ORD123456",
  "symbol": "RELIANCE",
  "status": "FILLED",
  "fillPrice": 2465.50,
  "filledQuantity": 100,
  "timestamp": "2024-01-01T09:30:00Z"
}
```

## 🔧 Environment Variables

The platform uses Supabase for secure environment variable management. Configure the following variables:

### Market Data Provider
```
VITE_MARKET_DATA_API_URL=https://api.marketdata.com
VITE_MARKET_DATA_WS_URL=wss://ws.marketdata.com
VITE_MARKET_DATA_API_KEY=your_api_key_here
```

### Trading Broker Integration
```
VITE_BROKER_API_URL=https://api.broker.com
VITE_BROKER_API_KEY=your_broker_api_key
VITE_BROKER_SECRET=your_broker_secret
```

### AI & Analytics
```
VITE_AI_API_URL=https://ai.tradingplatform.com
VITE_AI_API_KEY=your_ai_api_key
VITE_ANALYTICS_ENDPOINT=https://analytics.platform.com
```

### WebSocket Configuration
```
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_MAX_RECONNECT_ATTEMPTS=10
VITE_WS_HEARTBEAT_INTERVAL=30000
```

## 🏗️ Architecture

### Component Structure
```
src/
├── components/
│   ├── trading/
│   │   ├── data/          # Market data providers & widgets
│   │   ├── orders/        # Order management components
│   │   ├── positions/     # Position tracking components
│   │   ├── widgets/       # Quick trade & utility widgets
│   │   ├── market/        # Market overview & heatmap
│   │   ├── scanners/      # Strategy scanners & filters
│   │   ├── ai/            # AI agents & conversational interface
│   │   ├── analytics/     # Performance analytics
│   │   ├── charting/      # Chart components
│   │   └── layout/        # Layout components
│   └── ui/               # Reusable UI components
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── lib/                  # Utility functions
```

### Data Flow
1. **Market Data**: WebSocket connection provides real-time price feeds
2. **Order Management**: REST API handles order placement and management
3. **Position Tracking**: Real-time position updates via WebSocket
4. **Strategy Execution**: AI agents process market data and generate signals
5. **Risk Management**: Continuous monitoring of positions and exposure

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (for environment variables)

### Installation
```bash
# Clone the repository
git clone https://github.com/your-org/trading-platform

# Install dependencies
npm install

# Start development server
npm run dev
```

### Configuration
1. Set up Supabase project
2. Configure environment variables in Supabase dashboard
3. Update API endpoints in the platform
4. Test WebSocket connections

## 📱 Usage Examples

### Real-time Market Data
```typescript
import { useMarketData } from './components/trading/data/MarketDataProvider';

const { data, subscribe, unsubscribe } = useMarketData();

// Subscribe to real-time data
useEffect(() => {
  subscribe('NIFTY');
  return () => unsubscribe('NIFTY');
}, []);

// Access real-time price
const niftyPrice = data['NIFTY']?.price;
```

### Place Orders
```typescript
import { OrderManager } from './components/trading/orders/OrderManager';

// Market order
const marketOrder = {
  symbol: 'RELIANCE',
  type: 'BUY',
  orderType: 'MARKET',
  quantity: 100
};

// Limit order with stop loss
const limitOrder = {
  symbol: 'TCS',
  type: 'BUY',
  orderType: 'BRACKET',
  quantity: 50,
  price: 3800,
  stopLoss: 3750,
  target: 3900
};
```

### Track Positions
```typescript
import { PositionManager } from './components/trading/positions/PositionManager';

// Real-time P&L tracking
const positions = usePositions();
const totalPnL = positions.reduce((sum, pos) => sum + pos.pnl, 0);
```

## 🔒 Security

- API keys stored securely in Supabase
- WebSocket connections with authentication
- Order validation and risk checks
- Position limits and margin requirements
- Audit logging for all transactions

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/new-feature`)
3. Commit changes (`git commit -am 'Add new feature'`)
4. Push to branch (`git push origin feature/new-feature`)
5. Create Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.tradingplatform.com
- Community: https://community.tradingplatform.com

---

*Built with ❤️ using React, TypeScript, and modern web technologies*
