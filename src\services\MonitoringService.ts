export interface SystemMetrics {
  cpu: number;
  memory: number;
  network: number;
  disk: number;
  latency: number;
  throughput: number;
  errorRate: number;
  timestamp: Date;
}

export interface AlertRule {
  id: string;
  name: string;
  metric: string;
  threshold: number;
  condition: 'GREATER_THAN' | 'LESS_THAN' | 'EQUALS';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  isActive: boolean;
}

export interface PerformanceMetrics {
  component: string;
  responseTime: number;
  successRate: number;
  errorCount: number;
  requestCount: number;
  timestamp: Date;
}

export class MonitoringService {
  private metrics: SystemMetrics[] = [];
  private alerts: AlertRule[] = [];
  private performance: Map<string, PerformanceMetrics[]> = new Map();
  private isMonitoring = false;

  constructor() {
    this.initializeAlertRules();
    this.startMonitoring();
  }

  private initializeAlertRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'cpu-high',
        name: 'High CPU Usage',
        metric: 'cpu',
        threshold: 80,
        condition: 'GREATER_THAN',
        severity: 'HIGH',
        isActive: true
      },
      {
        id: 'memory-critical',
        name: 'Critical Memory Usage',
        metric: 'memory',
        threshold: 90,
        condition: 'GREATER_THAN',
        severity: 'CRITICAL',
        isActive: true
      },
      {
        id: 'latency-high',
        name: 'High Latency',
        metric: 'latency',
        threshold: 1000,
        condition: 'GREATER_THAN',
        severity: 'MEDIUM',
        isActive: true
      },
      {
        id: 'error-rate-high',
        name: 'High Error Rate',
        metric: 'errorRate',
        threshold: 5,
        condition: 'GREATER_THAN',
        severity: 'HIGH',
        isActive: true
      }
    ];

    this.alerts = defaultRules;
  }

  private startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    setInterval(() => {
      this.collectSystemMetrics();
    }, 5000); // Every 5 seconds

    setInterval(() => {
      this.checkAlerts();
    }, 10000); // Every 10 seconds
  }

  private collectSystemMetrics(): void {
    const metrics: SystemMetrics = {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      network: Math.random() * 100,
      disk: Math.random() * 100,
      latency: Math.random() * 500 + 50,
      throughput: Math.random() * 10000 + 1000,
      errorRate: Math.random() * 10,
      timestamp: new Date()
    };

    this.metrics.push(metrics);
    
    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  private checkAlerts(): void {
    if (this.metrics.length === 0) return;

    const latestMetrics = this.metrics[this.metrics.length - 1];
    
    this.alerts.forEach(rule => {
      if (!rule.isActive) return;

      const metricValue = (latestMetrics as any)[rule.metric];
      let triggered = false;

      switch (rule.condition) {
        case 'GREATER_THAN':
          triggered = metricValue > rule.threshold;
          break;
        case 'LESS_THAN':
          triggered = metricValue < rule.threshold;
          break;
        case 'EQUALS':
          triggered = metricValue === rule.threshold;
          break;
      }

      if (triggered) {
        this.triggerAlert(rule, metricValue);
      }
    });
  }

  private triggerAlert(rule: AlertRule, value: number): void {
    console.warn(`ALERT: ${rule.name} - ${rule.metric}: ${value} (threshold: ${rule.threshold})`);
  }

  recordPerformance(component: string, responseTime: number, success: boolean): void {
    if (!this.performance.has(component)) {
      this.performance.set(component, []);
    }

    const componentMetrics = this.performance.get(component)!;
    const latest = componentMetrics[componentMetrics.length - 1];
    
    const newMetrics: PerformanceMetrics = {
      component,
      responseTime,
      successRate: success ? 100 : 0,
      errorCount: success ? 0 : 1,
      requestCount: 1,
      timestamp: new Date()
    };

    if (latest && 
        new Date().getTime() - latest.timestamp.getTime() < 60000) {
      // Aggregate metrics within the same minute
      latest.responseTime = (latest.responseTime + responseTime) / 2;
      latest.successRate = ((latest.successRate * latest.requestCount) + (success ? 100 : 0)) / (latest.requestCount + 1);
      latest.errorCount += success ? 0 : 1;
      latest.requestCount += 1;
    } else {
      componentMetrics.push(newMetrics);
      
      // Keep only last 100 metrics per component
      if (componentMetrics.length > 100) {
        this.performance.set(component, componentMetrics.slice(-100));
      }
    }
  }

  getSystemHealth(): any {
    if (this.metrics.length === 0) {
      return { status: 'UNKNOWN', message: 'No metrics available' };
    }

    const latest = this.metrics[this.metrics.length - 1];
    const avgCpu = this.metrics.slice(-10).reduce((sum, m) => sum + m.cpu, 0) / 10;
    const avgMemory = this.metrics.slice(-10).reduce((sum, m) => sum + m.memory, 0) / 10;
    const avgLatency = this.metrics.slice(-10).reduce((sum, m) => sum + m.latency, 0) / 10;

    let status = 'HEALTHY';
    let message = 'All systems operational';

    if (avgCpu > 80 || avgMemory > 85 || avgLatency > 1000) {
      status = 'DEGRADED';
      message = 'System performance degraded';
    }

    if (avgCpu > 95 || avgMemory > 95 || latest.errorRate > 10) {
      status = 'CRITICAL';
      message = 'Critical system issues detected';
    }

    return {
      status,
      message,
      metrics: {
        cpu: avgCpu,
        memory: avgMemory,
        latency: avgLatency,
        errorRate: latest.errorRate,
        uptime: Date.now() - (this.metrics[0]?.timestamp.getTime() || Date.now())
      },
      timestamp: new Date()
    };
  }

  getPerformanceReport(): any {
    const report: any = {};
    
    this.performance.forEach((metrics, component) => {
      const recent = metrics.slice(-10);
      if (recent.length === 0) return;

      report[component] = {
        averageResponseTime: recent.reduce((sum, m) => sum + m.responseTime, 0) / recent.length,
        successRate: recent.reduce((sum, m) => sum + m.successRate, 0) / recent.length,
        totalRequests: recent.reduce((sum, m) => sum + m.requestCount, 0),
        totalErrors: recent.reduce((sum, m) => sum + m.errorCount, 0),
        lastUpdated: recent[recent.length - 1].timestamp
      };
    });

    return report;
  }

  getMetricsHistory(hours: number = 1): SystemMetrics[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.metrics.filter(m => m.timestamp > cutoff);
  }

  addAlertRule(rule: Omit<AlertRule, 'id'>): string {
    const id = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.alerts.push({ ...rule, id });
    return id;
  }

  updateAlertRule(id: string, updates: Partial<AlertRule>): boolean {
    const index = this.alerts.findIndex(a => a.id === id);
    if (index === -1) return false;
    
    this.alerts[index] = { ...this.alerts[index], ...updates };
    return true;
  }

  getActiveAlerts(): AlertRule[] {
    return this.alerts.filter(a => a.isActive);
  }
}
