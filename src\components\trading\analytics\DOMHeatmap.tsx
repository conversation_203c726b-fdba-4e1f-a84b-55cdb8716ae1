
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Activity, TrendingUp, Al<PERSON>Triangle, Zap } from "lucide-react";

interface OrderBookLevel {
  price: number;
  bidVolume: number;
  askVolume: number;
  bidOrders: number;
  askOrders: number;
  timestamp: number;
  heatIntensity: number;
  liquidityScore: number;
  orderImbalance: number;
}

interface DOMHeatmapProps {
  symbol?: string;
  depth?: number;
  orderBookData?: Array<{
    price: number;
    bidVolume: number;
    askVolume: number;
    bidOrders: number;
    askOrders: number;
    timestamp: number;
  }>;
}

export const DOMHeatmap = ({ 
  symbol = "NIFTY", 
  depth = 20,
  orderBookData = []
}: DOMHeatmapProps) => {
  const [processedData, setProcessedData] = useState<OrderBookLevel[]>([]);
  const [imbalanceAlerts, setImbalanceAlerts] = useState<Array<{ type: 'BUY' | 'SELL', level: number, ratio: number }>>([]);
  const [liquidityGaps, setLiquidityGaps] = useState<Array<{ price: number, gap: number }>>([]);
  const [currentPrice, setCurrentPrice] = useState<number>(0);

  // Real Order Flow Imbalance calculation
  const calculateImbalance = (data: OrderBookLevel[]) => {
    const alerts: Array<{ type: 'BUY' | 'SELL', level: number, ratio: number }> = [];
    
    data.forEach((level) => {
      const totalVolume = level.bidVolume + level.askVolume;
      if (totalVolume > 0) {
        const bidRatio = level.bidVolume / totalVolume;
        const askRatio = level.askVolume / totalVolume;
        
        // Detect significant imbalances (threshold: 75%)
        if (bidRatio > 0.75) {
          alerts.push({ type: 'BUY', level: level.price, ratio: bidRatio });
        } else if (askRatio > 0.75) {
          alerts.push({ type: 'SELL', level: level.price, ratio: askRatio });
        }
      }
    });
    
    return alerts.slice(0, 5).sort((a, b) => b.ratio - a.ratio);
  };

  // Real Liquidity Gap detection using volume analysis
  const detectLiquidityGaps = (data: OrderBookLevel[]) => {
    const gaps: Array<{ price: number, gap: number }> = [];
    
    if (data.length < 2) return gaps;
    
    // Calculate average liquidity
    const totalLiquidity = data.reduce((sum, level) => sum + level.bidVolume + level.askVolume, 0);
    const avgLiquidity = totalLiquidity / data.length;
    const gapThreshold = avgLiquidity * 0.3; // 70% drop from average
    
    for (let i = 0; i < data.length; i++) {
      const currentLiquidity = data[i].bidVolume + data[i].askVolume;
      
      if (currentLiquidity < gapThreshold && avgLiquidity > 0) {
        const liquidityDrop = (avgLiquidity - currentLiquidity) / avgLiquidity;
        
        gaps.push({
          price: data[i].price,
          gap: liquidityDrop * 100
        });
      }
    }
    
    return gaps.slice(0, 3).sort((a, b) => b.gap - a.gap);
  };

  // Calculate Heat Intensity based on volume relative to max volume
  const calculateHeatIntensity = (volume: number, maxVolume: number) => {
    return maxVolume > 0 ? Math.min(100, (volume / maxVolume) * 100) : 0;
  };

  // Calculate Liquidity Score using order count and volume
  const calculateLiquidityScore = (volume: number, orderCount: number, maxVolume: number, maxOrders: number) => {
    const volumeScore = maxVolume > 0 ? (volume / maxVolume) * 50 : 0;
    const orderScore = maxOrders > 0 ? (orderCount / maxOrders) * 50 : 0;
    return Math.min(100, volumeScore + orderScore);
  };

  // Calculate Order Imbalance ratio
  const calculateOrderImbalance = (bidVolume: number, askVolume: number) => {
    const totalVolume = bidVolume + askVolume;
    if (totalVolume === 0) return 0;
    return (bidVolume - askVolume) / totalVolume;
  };

  // Process raw order book data
  const processOrderBookData = (rawData: Array<{
    price: number;
    bidVolume: number;
    askVolume: number;
    bidOrders: number;
    askOrders: number;
    timestamp: number;
  }>) => {
    if (rawData.length === 0) return [];

    // Find current price (midpoint of best bid and ask)
    const sortedData = [...rawData].sort((a, b) => b.price - a.price);
    const bestBid = sortedData.find(level => level.bidVolume > 0);
    const bestAsk = sortedData.find(level => level.askVolume > 0);
    
    if (bestBid && bestAsk) {
      setCurrentPrice((bestBid.price + bestAsk.price) / 2);
    }

    // Calculate maximum values for normalization
    const maxVolume = Math.max(...rawData.map(level => level.bidVolume + level.askVolume));
    const maxOrders = Math.max(...rawData.map(level => level.bidOrders + level.askOrders));

    // Process each level
    const processed = rawData.map(level => {
      const totalVolume = level.bidVolume + level.askVolume;
      const totalOrders = level.bidOrders + level.askOrders;
      
      return {
        ...level,
        heatIntensity: calculateHeatIntensity(totalVolume, maxVolume),
        liquidityScore: calculateLiquidityScore(totalVolume, totalOrders, maxVolume, maxOrders),
        orderImbalance: calculateOrderImbalance(level.bidVolume, level.askVolume)
      };
    }).sort((a, b) => b.price - a.price); // Sort by price descending

    return processed;
  };

  useEffect(() => {
    if (orderBookData && orderBookData.length > 0) {
      const processed = processOrderBookData(orderBookData);
      setProcessedData(processed);
      setImbalanceAlerts(calculateImbalance(processed));
      setLiquidityGaps(detectLiquidityGaps(processed));
    }
  }, [orderBookData, symbol, depth]);

  const getTotalBidVolume = () => processedData.reduce((sum, level) => sum + level.bidVolume, 0);
  const getTotalAskVolume = () => processedData.reduce((sum, level) => sum + level.askVolume, 0);

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-trading-light flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            DOM Heatmap - {symbol}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Summary Metrics */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-xs text-trading-muted">Current Price</div>
              <div className="text-sm font-bold text-trading-light">
                {currentPrice > 0 ? `₹${currentPrice.toFixed(2)}` : '--'}
              </div>
            </div>
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-xs text-trading-muted">Total Bids</div>
              <div className="text-sm font-bold text-green-400">
                {getTotalBidVolume() > 0 ? `${(getTotalBidVolume() / 1000).toFixed(1)}K` : '--'}
              </div>
            </div>
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-xs text-trading-muted">Total Asks</div>
              <div className="text-sm font-bold text-red-400">
                {getTotalAskVolume() > 0 ? `${(getTotalAskVolume() / 1000).toFixed(1)}K` : '--'}
              </div>
            </div>
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-xs text-trading-muted">Imbalances</div>
              <div className="text-sm font-bold text-yellow-400">{imbalanceAlerts.length}</div>
            </div>
          </div>

          {/* Order Book Heatmap */}
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {processedData.length > 0 ? (
              processedData.map((level, index) => {
                const totalVolume = level.bidVolume + level.askVolume;
                const bidPercentage = totalVolume > 0 ? (level.bidVolume / totalVolume) * 100 : 0;
                const askPercentage = totalVolume > 0 ? (level.askVolume / totalVolume) * 100 : 0;
                const isNearCurrentPrice = Math.abs(level.price - currentPrice) < (currentPrice * 0.001);

                return (
                  <div
                    key={index}
                    className={`grid grid-cols-7 gap-1 text-xs p-1 rounded transition-all duration-200 ${
                      isNearCurrentPrice ? 'ring-1 ring-yellow-400 bg-yellow-400/10' : ''
                    }`}
                  >
                    {/* Bid Volume */}
                    <div 
                      className="text-right p-1 rounded"
                      style={{ 
                        backgroundColor: `rgba(16, 185, 129, ${level.heatIntensity / 100 * 0.6})`,
                        border: '1px solid rgba(16, 185, 129, 0.3)'
                      }}
                    >
                      <div className="text-green-400 font-medium">
                        {level.bidVolume > 0 ? level.bidVolume.toFixed(0) : ''}
                      </div>
                      <div className="text-green-300 opacity-70">
                        {level.bidOrders > 0 ? level.bidOrders : ''}
                      </div>
                    </div>

                    {/* Bid Percentage Bar */}
                    <div className="flex items-center">
                      <div className="w-full bg-trading-dark rounded-full h-2">
                        <div 
                          className="bg-green-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${bidPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Price */}
                    <div className="text-center p-1">
                      <div className={`font-medium ${isNearCurrentPrice ? 'text-yellow-400' : 'text-trading-light'}`}>
                        {level.price.toFixed(2)}
                      </div>
                    </div>

                    {/* Ask Percentage Bar */}
                    <div className="flex items-center">
                      <div className="w-full bg-trading-dark rounded-full h-2">
                        <div 
                          className="bg-red-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${askPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Ask Volume */}
                    <div 
                      className="text-left p-1 rounded"
                      style={{ 
                        backgroundColor: `rgba(239, 68, 68, ${level.heatIntensity / 100 * 0.6})`,
                        border: '1px solid rgba(239, 68, 68, 0.3)'
                      }}
                    >
                      <div className="text-red-400 font-medium">
                        {level.askVolume > 0 ? level.askVolume.toFixed(0) : ''}
                      </div>
                      <div className="text-red-300 opacity-70">
                        {level.askOrders > 0 ? level.askOrders : ''}
                      </div>
                    </div>

                    {/* Liquidity Score */}
                    <div className="text-center p-1">
                      <div className={`text-xs ${level.liquidityScore > 70 ? 'text-green-400' : level.liquidityScore > 40 ? 'text-yellow-400' : 'text-red-400'}`}>
                        {level.liquidityScore.toFixed(0)}
                      </div>
                    </div>

                    {/* Imbalance Indicator */}
                    <div className="text-center p-1">
                      <div 
                        className={`w-2 h-2 rounded-full mx-auto ${
                          Math.abs(level.orderImbalance) > 0.5 
                            ? level.orderImbalance > 0 
                              ? 'bg-green-400' 
                              : 'bg-red-400'
                            : 'bg-gray-400'
                        }`}
                        style={{
                          opacity: Math.min(1, Math.abs(level.orderImbalance) + 0.3)
                        }}
                      ></div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8 text-trading-muted">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No order book data available</p>
                <p className="text-xs mt-1">Connect to data feed to see DOM heatmap</p>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="flex gap-2 flex-wrap">
            <Button size="sm" variant="outline" disabled={processedData.length === 0}>
              <TrendingUp className="h-4 w-4 mr-1" />
              Auto-Scale
            </Button>
            <Button size="sm" variant="outline" disabled={processedData.length === 0}>
              <Zap className="h-4 w-4 mr-1" />
              Real-time
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alerts and Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Order Flow Imbalances */}
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-trading-light text-base">Order Flow Imbalances</CardTitle>
          </CardHeader>
          <CardContent>
            {imbalanceAlerts.length > 0 ? (
              <div className="space-y-2">
                {imbalanceAlerts.map((alert, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-400" />
                      <Badge 
                        variant="outline" 
                        className={alert.type === 'BUY' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}
                      >
                        {alert.type}
                      </Badge>
                      <span className="text-sm text-trading-light">₹{alert.level.toFixed(2)}</span>
                    </div>
                    <div className="text-sm text-blue-400">{(alert.ratio * 100).toFixed(1)}%</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-trading-muted">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No significant imbalances detected</p>
                <p className="text-xs mt-1">Connect to data feed for real-time analysis</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Liquidity Gaps */}
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-trading-light text-base">Liquidity Gaps</CardTitle>
          </CardHeader>
          <CardContent>
            {liquidityGaps.length > 0 ? (
              <div className="space-y-2">
                {liquidityGaps.map((gap, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-orange-400" />
                      <span className="text-sm text-trading-light">₹{gap.price.toFixed(2)}</span>
                    </div>
                    <div className="text-sm text-orange-400">{gap.gap.toFixed(1)}% gap</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-trading-muted">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No significant liquidity gaps found</p>
                <p className="text-xs mt-1">Connect to data feed for real-time analysis</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
