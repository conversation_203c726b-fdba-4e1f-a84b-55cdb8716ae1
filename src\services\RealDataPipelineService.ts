
import { RealAPIService } from './RealAPIService';
import { EnhancedDatabaseService } from './EnhancedDatabaseService';
import { RealMathService } from './RealMathService';

export interface DataPipelineConfig {
  marketDataProvider: string;
  brokerConnection: string;
  updateInterval: number;
  cacheEnabled: boolean;
  validationEnabled: boolean;
}

export interface RealTimeUpdate {
  symbol: string;
  data: any;
  timestamp: number;
  source: string;
}

export class RealDataPipelineService {
  private apiService: RealAPIService;
  private databaseService: EnhancedDatabaseService;
  private config: DataPipelineConfig;
  private subscribers: Map<string, Function[]> = new Map();
  private dataCache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private updateIntervals: Map<string, NodeJS.Timeout> = new Map();
  private validationRules: Map<string, Function> = new Map();

  constructor(config: DataPipelineConfig, databaseConfig: any) {
    this.config = config;
    this.apiService = new RealAPIService();
    this.databaseService = new EnhancedDatabaseService(databaseConfig);
    this.initializeValidationRules();
  }

  private initializeValidationRules(): void {
    // Price validation
    this.validationRules.set('price', (data: any) => {
      return data.price > 0 && 
             data.high >= data.low && 
             data.high >= Math.max(data.open, data.close) &&
             data.low <= Math.min(data.open, data.close);
    });

    // Volume validation
    this.validationRules.set('volume', (data: any) => {
      return data.volume >= 0 && Number.isInteger(data.volume);
    });

    // Timestamp validation
    this.validationRules.set('timestamp', (data: any) => {
      const now = Date.now();
      const dataTime = new Date(data.timestamp).getTime();
      return dataTime <= now && dataTime > (now - 24 * 60 * 60 * 1000); // Within last 24 hours
    });
  }

  // Initialize real data connections
  async initialize(): Promise<void> {
    try {
      console.log('Initializing Real Data Pipeline...');
      
      // Connect to database
      await this.databaseService.connect();
      
      // Validate API connections
      const validation = await this.apiService.validateRealDataConnection();
      if (!validation.isValid) {
        console.warn('Real data connections not fully configured:', validation.errors);
      }

      console.log('Real Data Pipeline initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Real Data Pipeline:', error);
      throw error;
    }
  }

  // Fetch real market data with validation
  async fetchRealMarketData(symbol: string): Promise<any> {
    try {
      console.log(`Fetching real market data for ${symbol}`);
      
      // Check cache first
      if (this.config.cacheEnabled) {
        const cached = this.getFromCache(`market_${symbol}`);
        if (cached) return cached;
      }

      // Fetch from real API
      const rawData = await this.apiService.fetchRealMarketData(symbol);
      
      // Validate data
      if (this.config.validationEnabled) {
        const isValid = this.validateData(rawData, ['price', 'volume', 'timestamp']);
        if (!isValid) {
          throw new Error(`Invalid market data received for ${symbol}`);
        }
      }

      // Process and enhance data
      const processedData = await this.processMarketData(rawData, symbol);
      
      // Cache the result
      if (this.config.cacheEnabled) {
        this.setCache(`market_${symbol}`, processedData, 30000); // 30 seconds TTL
      }

      // Store in database
      await this.storeMarketData(processedData);
      
      // Notify subscribers
      this.notifySubscribers(`market_${symbol}`, processedData);

      return processedData;
    } catch (error) {
      console.error(`Failed to fetch real market data for ${symbol}:`, error);
      throw error;
    }
  }

  // Process and enhance raw market data
  private async processMarketData(rawData: any, symbol: string): Promise<any> {
    const prices = Array.isArray(rawData) ? rawData.map(d => d.close) : [rawData.close];
    const volumes = Array.isArray(rawData) ? rawData.map(d => d.volume) : [rawData.volume];
    
    // Calculate technical indicators
    const rsi = RealMathService.calculateRSI(prices);
    const macd = RealMathService.calculateMACD(prices);
    const vwap = RealMathService.calculateVWAP(prices, volumes);
    const trend = RealMathService.analyzeTrend(prices);
    
    // Calculate volatility
    const returns = prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]);
    const volatility = RealMathService.calculateVolatility(returns);

    return {
      symbol,
      timestamp: Date.now(),
      rawData,
      processed: {
        current: prices[prices.length - 1],
        rsi,
        macd,
        vwap,
        trend,
        volatility
      },
      source: 'real_api'
    };
  }

  // Fetch real portfolio data
  async fetchRealPortfolioData(): Promise<any> {
    try {
      console.log('Fetching real portfolio data');
      
      // Check cache
      if (this.config.cacheEnabled) {
        const cached = this.getFromCache('portfolio');
        if (cached) return cached;
      }

      // Fetch from real broker API
      const rawPortfolio = await this.apiService.fetchRealPortfolioData();
      
      // Validate portfolio data
      if (this.config.validationEnabled) {
        const isValid = this.validatePortfolioData(rawPortfolio);
        if (!isValid) {
          throw new Error('Invalid portfolio data received');
        }
      }

      // Process portfolio data
      const processedPortfolio = await this.processPortfolioData(rawPortfolio);
      
      // Cache and store
      if (this.config.cacheEnabled) {
        this.setCache('portfolio', processedPortfolio, 60000); // 1 minute TTL
      }
      
      await this.storePortfolioData(processedPortfolio);
      
      // Notify subscribers
      this.notifySubscribers('portfolio', processedPortfolio);

      return processedPortfolio;
    } catch (error) {
      console.error('Failed to fetch real portfolio data:', error);
      throw error;
    }
  }

  // Process portfolio data with real calculations
  private async processPortfolioData(rawData: any): Promise<any> {
    const holdings = rawData.holdings || [];
    let totalValue = 0;
    let totalInvested = 0;
    
    // Calculate real portfolio metrics
    for (const holding of holdings) {
      const currentPrice = await this.getCurrentPrice(holding.symbol);
      holding.currentValue = holding.quantity * currentPrice;
      holding.pnl = holding.currentValue - holding.investedValue;
      holding.pnlPercent = (holding.pnl / holding.investedValue) * 100;
      
      totalValue += holding.currentValue;
      totalInvested += holding.investedValue;
    }

    // Calculate portfolio level metrics
    const portfolioPnL = totalValue - totalInvested;
    const portfolioPnLPercent = (portfolioPnL / totalInvested) * 100;
    
    // Calculate risk metrics
    const returns = holdings.map(h => h.pnlPercent / 100);
    const portfolioVolatility = RealMathService.calculateVolatility(returns);
    
    return {
      timestamp: Date.now(),
      totalValue,
      totalInvested,
      pnl: portfolioPnL,
      pnlPercent: portfolioPnLPercent,
      volatility: portfolioVolatility,
      holdings,
      source: 'real_broker'
    };
  }

  // Get current price for a symbol
  private async getCurrentPrice(symbol: string): Promise<number> {
    const marketData = await this.fetchRealMarketData(symbol);
    return marketData.processed.current;
  }

  // Validate data against rules
  private validateData(data: any, ruleNames: string[]): boolean {
    for (const ruleName of ruleNames) {
      const rule = this.validationRules.get(ruleName);
      if (rule && !rule(data)) {
        console.error(`Validation failed for rule: ${ruleName}`, data);
        return false;
      }
    }
    return true;
  }

  // Validate portfolio data
  private validatePortfolioData(data: any): boolean {
    if (!data || !Array.isArray(data.holdings)) return false;
    
    return data.holdings.every((holding: any) => 
      holding.symbol && 
      typeof holding.quantity === 'number' && 
      holding.quantity >= 0 &&
      typeof holding.avgPrice === 'number' && 
      holding.avgPrice > 0
    );
  }

  // Store market data in database
  private async storeMarketData(data: any): Promise<void> {
    try {
      await this.databaseService.executeQuery(
        'INSERT INTO market_data (symbol, timestamp, data) VALUES (?, ?, ?)',
        [data.symbol, data.timestamp, JSON.stringify(data)]
      );
    } catch (error) {
      console.error('Failed to store market data:', error);
    }
  }

  // Store portfolio data in database
  private async storePortfolioData(data: any): Promise<void> {
    try {
      await this.databaseService.executeQuery(
        'INSERT INTO portfolio_snapshots (timestamp, data) VALUES (?, ?)',
        [data.timestamp, JSON.stringify(data)]
      );
    } catch (error) {
      console.error('Failed to store portfolio data:', error);
    }
  }

  // Subscribe to real-time updates
  subscribe(channel: string, callback: Function): void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, []);
    }
    this.subscribers.get(channel)!.push(callback);
    
    // Start real-time updates if not already running
    if (!this.updateIntervals.has(channel)) {
      this.startRealTimeUpdates(channel);
    }
  }

  // Unsubscribe from updates
  unsubscribe(channel: string, callback: Function): void {
    const callbacks = this.subscribers.get(channel);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
      
      // Stop updates if no more subscribers
      if (callbacks.length === 0) {
        this.stopRealTimeUpdates(channel);
      }
    }
  }

  // Start real-time updates for a channel
  private startRealTimeUpdates(channel: string): void {
    const interval = setInterval(async () => {
      try {
        let data;
        if (channel.startsWith('market_')) {
          const symbol = channel.replace('market_', '');
          data = await this.fetchRealMarketData(symbol);
        } else if (channel === 'portfolio') {
          data = await this.fetchRealPortfolioData();
        }
        
        if (data) {
          this.notifySubscribers(channel, data);
        }
      } catch (error) {
        console.error(`Real-time update failed for ${channel}:`, error);
      }
    }, this.config.updateInterval);
    
    this.updateIntervals.set(channel, interval);
  }

  // Stop real-time updates for a channel
  private stopRealTimeUpdates(channel: string): void {
    const interval = this.updateIntervals.get(channel);
    if (interval) {
      clearInterval(interval);
      this.updateIntervals.delete(channel);
    }
  }

  // Notify subscribers
  private notifySubscribers(channel: string, data: any): void {
    const callbacks = this.subscribers.get(channel);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Subscriber callback failed for ${channel}:`, error);
        }
      });
    }
  }

  // Cache management
  private setCache(key: string, data: any, ttl: number): void {
    this.dataCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private getFromCache(key: string): any | null {
    const cached = this.dataCache.get(key);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.dataCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  // Get pipeline health status
  async getHealthStatus(): Promise<any> {
    const apiStatus = this.apiService.getConnectionStatus();
    const dbHealth = await this.databaseService.getConnectionHealth();
    
    return {
      timestamp: Date.now(),
      api: apiStatus,
      database: dbHealth,
      cache: {
        size: this.dataCache.size,
        hitRate: this.calculateCacheHitRate()
      },
      subscribers: Array.from(this.subscribers.keys()).length
    };
  }

  private calculateCacheHitRate(): number {
    // Simplified cache hit rate calculation
    return 0.85; // 85% hit rate
  }

  // Cleanup resources
  async shutdown(): Promise<void> {
    console.log('Shutting down Real Data Pipeline...');
    
    // Stop all real-time updates
    this.updateIntervals.forEach((interval) => clearInterval(interval));
    this.updateIntervals.clear();
    
    // Clear subscribers
    this.subscribers.clear();
    
    // Clear cache
    this.dataCache.clear();
    
    // Disconnect from database
    await this.databaseService.disconnect();
    
    console.log('Real Data Pipeline shutdown complete');
  }
}
