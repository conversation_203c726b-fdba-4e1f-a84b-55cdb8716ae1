
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { BrokerAPIService, BrokerConfig } from "../../../services/BrokerAPIService";
import { Wifi, WifiOff, AlertCircle, CheckCircle } from "lucide-react";

interface BrokerConfigurationProps {
  onConnectionChange: (connected: boolean, service?: BrokerAPIService) => void;
}

export const BrokerConfiguration: React.FC<BrokerConfigurationProps> = ({ 
  onConnectionChange 
}) => {
  const [config, setConfig] = useState<BrokerConfig>({
    apiKey: '',
    accessToken: '',
    broker: 'ZEROD<PERSON>',
    environment: 'SANDBOX'
  });
  
  const [brokerService, setBrokerService] = useState<BrokerAPIService | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const { toast } = useToast();

  const handleConfigChange = (field: keyof BrokerConfig, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const connectToBroker = async () => {
    if (!config.apiKey || !config.accessToken) {
      toast({
        title: "Configuration Required",
        description: "Please enter API key and access token",
        variant: "destructive"
      });
      return;
    }

    setIsConnecting(true);
    setConnectionStatus('connecting');

    try {
      const service = new BrokerAPIService(config);
      
      // Authenticate
      const authSuccess = await service.authenticate();
      if (!authSuccess) {
        throw new Error('Authentication failed');
      }

      // Connect WebSocket
      await service.connectWebSocket();
      
      setBrokerService(service);
      setConnectionStatus('connected');
      onConnectionChange(true, service);
      
      toast({
        title: "Broker Connected",
        description: `Successfully connected to ${config.broker} (${config.environment})`,
      });

    } catch (error) {
      console.error('Broker connection failed:', error);
      setConnectionStatus('error');
      
      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectFromBroker = () => {
    if (brokerService) {
      brokerService.disconnect();
      setBrokerService(null);
      setConnectionStatus('disconnected');
      onConnectionChange(false);
      
      toast({
        title: "Broker Disconnected",
        description: "Successfully disconnected from broker",
      });
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'connecting':
        return <Wifi className="h-4 w-4 text-yellow-400 animate-pulse" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <CardTitle className="text-trading-light flex items-center">
          {getStatusIcon()}
          <span className="ml-2">Broker Configuration</span>
          <Badge variant="outline" className={`ml-auto text-xs`}>
            <div className={`w-2 h-2 rounded-full mr-2 ${getStatusColor()}`}></div>
            {connectionStatus.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="broker" className="text-trading-light">Broker</Label>
            <Select 
              value={config.broker} 
              onValueChange={(value: 'ZERODHA' | 'DHAN') => handleConfigChange('broker', value)}
              disabled={connectionStatus === 'connected'}
            >
              <SelectTrigger className="bg-trading-dark border-trading-border text-trading-light">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ZERODHA">Zerodha (Kite)</SelectItem>
                <SelectItem value="DHAN">Dhan</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="environment" className="text-trading-light">Environment</Label>
            <Select 
              value={config.environment} 
              onValueChange={(value: 'PRODUCTION' | 'SANDBOX') => handleConfigChange('environment', value)}
              disabled={connectionStatus === 'connected'}
            >
              <SelectTrigger className="bg-trading-dark border-trading-border text-trading-light">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SANDBOX">Sandbox (Testing)</SelectItem>
                <SelectItem value="PRODUCTION">Production (Live)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="apiKey" className="text-trading-light">API Key</Label>
          <Input
            id="apiKey"
            type="text"
            value={config.apiKey}
            onChange={(e) => handleConfigChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="bg-trading-dark border-trading-border text-trading-light"
            disabled={connectionStatus === 'connected'}
          />
        </div>

        <div>
          <Label htmlFor="accessToken" className="text-trading-light">Access Token</Label>
          <Input
            id="accessToken"
            type="password"
            value={config.accessToken}
            onChange={(e) => handleConfigChange('accessToken', e.target.value)}
            placeholder="Enter your access token"
            className="bg-trading-dark border-trading-border text-trading-light"
            disabled={connectionStatus === 'connected'}
          />
        </div>

        <div className="flex space-x-2">
          {connectionStatus === 'connected' ? (
            <Button 
              onClick={disconnectFromBroker}
              variant="destructive"
              className="flex-1"
            >
              Disconnect
            </Button>
          ) : (
            <Button 
              onClick={connectToBroker}
              disabled={isConnecting}
              className="flex-1"
            >
              {isConnecting ? 'Connecting...' : 'Connect'}
            </Button>
          )}
        </div>

        {config.broker === 'ZERODHA' && config.environment === 'PRODUCTION' && (
          <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded">
            <p className="text-xs text-blue-400">
              <strong>Zerodha Setup:</strong> After clicking connect, you'll be redirected to Kite login. 
              After login, copy the request token from the URL and paste it in the browser console.
            </p>
          </div>
        )}

        {connectionStatus === 'connected' && brokerService && (
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-trading-light font-medium">
                {brokerService.getConnectionStatus().toUpperCase()}
              </div>
              <div className="text-trading-muted">WebSocket</div>
            </div>
            <div className="text-center p-2 bg-trading-dark rounded">
              <div className="text-trading-light font-medium">
                {config.environment.toUpperCase()}
              </div>
              <div className="text-trading-muted">Environment</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
