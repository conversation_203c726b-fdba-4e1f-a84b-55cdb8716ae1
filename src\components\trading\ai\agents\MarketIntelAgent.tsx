
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Brain, TrendingUp, Users, Activity, AlertTriangle } from "lucide-react";

interface MoneyFlow {
  entity: string;
  flow: string;
  trend: 'Buying' | 'Selling';
  sectors: string[];
  impact: string;
}

interface SectorRotationData {
  sector: string;
  momentum: number;
  flow: string;
  status: string;
}

interface MarketSentimentData {
  metric: string;
  value: number | string;
  status: string;
  color: string;
}

interface MarketAlert {
  type: 'success' | 'info' | 'warning';
  title: string;
  description: string;
  icon: any;
}

interface MarketIntelAgentProps {
  smartMoneyFlows?: MoneyFlow[];
  sectorRotation?: SectorRotationData[];
  marketSentiment?: MarketSentimentData[];
  marketAlerts?: MarketAlert[];
  isAnalyzing?: boolean;
}

export const MarketIntelAgent = ({
  smartMoneyFlows = [],
  sectorRotation = [],
  marketSentiment = [],
  marketAlerts = [],
  isAnalyzing = false
}: MarketIntelAgentProps) => {
  const defaultSentiment = [
    { metric: "Fear & Greed Index", value: "--", status: "No Data", color: "text-trading-muted" },
    { metric: "VIX Level", value: "--", status: "No Data", color: "text-trading-muted" },
    { metric: "Put/Call Ratio", value: "--", status: "No Data", color: "text-trading-muted" },
    { metric: "News Sentiment", value: "--", status: "No Data", color: "text-trading-muted" }
  ];

  const sentimentData = marketSentiment.length > 0 ? marketSentiment : defaultSentiment;

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-400" />
            Market Intelligence Agent
            <Badge className={`ml-2 ${isAnalyzing ? 'bg-purple-600' : 'bg-gray-600'}`}>
              {isAnalyzing ? 'Analyzing' : 'Idle'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {sentimentData.map((item, index) => (
              <div key={index} className="text-center p-3 bg-trading-dark rounded">
                <div className={`text-lg font-bold ${item.color}`}>{item.value}</div>
                <div className="text-xs text-trading-muted">{item.metric}</div>
                <div className="text-xs text-trading-light">{item.status}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="flows" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="flows" className="data-[state=active]:bg-trading-accent">
            <Users className="h-4 w-4 mr-2" />
            Smart Money Flows
          </TabsTrigger>
          <TabsTrigger value="sectors" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Sector Rotation
          </TabsTrigger>
          <TabsTrigger value="alerts" className="data-[state=active]:bg-trading-accent">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Market Alerts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="flows">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Institutional Money Flow Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {smartMoneyFlows.length > 0 ? (
                <div className="space-y-3">
                  {smartMoneyFlows.map((flow, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{flow.entity}</div>
                        <Badge variant="outline" className={flow.trend === "Buying" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {flow.trend}
                        </Badge>
                        <div className="text-sm text-trading-muted">Sectors: {flow.sectors.join(", ")}</div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${flow.trend === "Buying" ? "text-green-400" : "text-red-400"}`}>
                          {flow.flow}
                        </div>
                        <div className="text-xs text-trading-muted">{flow.impact}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No institutional flow data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to market data feed to see real-time flows</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sectors">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Sector Rotation Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              {sectorRotation.length > 0 ? (
                <div className="space-y-3">
                  {sectorRotation.map((sector, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{sector.sector}</div>
                        <div className="flex items-center space-x-2">
                          <div className="text-sm text-trading-muted">Momentum:</div>
                          <div className="w-20 bg-gray-700 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${sector.momentum > 70 ? 'bg-green-400' : sector.momentum > 50 ? 'bg-yellow-400' : 'bg-red-400'}`}
                              style={{ width: `${sector.momentum}%` }}
                            ></div>
                          </div>
                          <div className="text-sm text-trading-light">{sector.momentum}%</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${sector.flow.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                          {sector.flow}
                        </div>
                        <div className="text-xs text-trading-muted">{sector.status}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No sector rotation data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to market data feed to see sector analysis</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">AI Market Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              {marketAlerts.length > 0 ? (
                <div className="space-y-3">
                  {marketAlerts.map((alert, index) => (
                    <div key={index} className={`p-3 border rounded ${
                      alert.type === 'success' ? 'bg-green-900/20 border-green-500/50' :
                      alert.type === 'info' ? 'bg-blue-900/20 border-blue-500/50' :
                      'bg-yellow-900/20 border-yellow-500/50'
                    }`}>
                      <div className="flex items-center mb-2">
                        <alert.icon className={`h-4 w-4 mr-2 ${
                          alert.type === 'success' ? 'text-green-400' :
                          alert.type === 'info' ? 'text-blue-400' :
                          'text-yellow-400'
                        }`} />
                        <span className={`font-medium ${
                          alert.type === 'success' ? 'text-green-400' :
                          alert.type === 'info' ? 'text-blue-400' :
                          'text-yellow-400'
                        }`}>{alert.title}</span>
                      </div>
                      <div className="text-sm text-trading-light">
                        {alert.description}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No market alerts available</p>
                  <p className="text-sm text-trading-muted mt-1">Alerts will appear when significant market events are detected</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
