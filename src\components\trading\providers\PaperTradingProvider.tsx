
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";

interface PaperTrade {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: Date;
  status: 'FILLED' | 'PENDING' | 'CANCELLED';
}

interface PaperPosition {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  marketValue: number;
}

interface PaperTradingContextType {
  balance: number;
  positions: PaperPosition[];
  trades: PaperTrade[];
  totalPnL: number;
  dayPnL: number;
  executeTrade: (trade: Omit<PaperTrade, 'id' | 'timestamp' | 'status'>) => void;
  resetPortfolio: () => void;
}

const PaperTradingContext = createContext<PaperTradingContextType | null>(null);

export const usePaperTrading = () => {
  const context = useContext(PaperTradingContext);
  if (!context) {
    throw new Error('usePaperTrading must be used within a PaperTradingProvider');
  }
  return context;
};

interface PaperTradingProviderProps {
  children: React.ReactNode;
  initialBalance?: number;
}

export const PaperTradingProvider: React.FC<PaperTradingProviderProps> = ({ 
  children, 
  initialBalance = 1000000 
}) => {
  const [balance, setBalance] = useState(initialBalance);
  const [positions, setPositions] = useState<PaperPosition[]>([]);
  const [trades, setTrades] = useState<PaperTrade[]>([]);
  const { toast } = useToast();

  const executeTrade = (trade: Omit<PaperTrade, 'id' | 'timestamp' | 'status'>) => {
    const newTrade: PaperTrade = {
      ...trade,
      id: `PT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      status: 'FILLED'
    };

    const tradeValue = trade.quantity * trade.price;

    if (trade.type === 'BUY') {
      if (balance < tradeValue) {
        toast({
          title: "Insufficient Balance",
          description: `Required: ₹${tradeValue.toLocaleString()}, Available: ₹${balance.toLocaleString()}`,
          variant: "destructive",
        });
        return;
      }
      setBalance(prev => prev - tradeValue);
    } else {
      setBalance(prev => prev + tradeValue);
    }

    // Update positions
    setPositions(prev => {
      const existingPos = prev.find(p => p.symbol === trade.symbol);
      
      if (!existingPos) {
        if (trade.type === 'BUY') {
          return [...prev, {
            symbol: trade.symbol,
            quantity: trade.quantity,
            avgPrice: trade.price,
            currentPrice: trade.price,
            pnl: 0,
            pnlPercent: 0,
            marketValue: trade.quantity * trade.price
          }];
        }
        return prev;
      }

      const newPositions = prev.filter(p => p.symbol !== trade.symbol);
      
      if (trade.type === 'BUY') {
        const newQuantity = existingPos.quantity + trade.quantity;
        const newAvgPrice = ((existingPos.avgPrice * existingPos.quantity) + (trade.price * trade.quantity)) / newQuantity;
        
        newPositions.push({
          ...existingPos,
          quantity: newQuantity,
          avgPrice: newAvgPrice,
          marketValue: newQuantity * existingPos.currentPrice
        });
      } else {
        const newQuantity = existingPos.quantity - trade.quantity;
        if (newQuantity > 0) {
          newPositions.push({
            ...existingPos,
            quantity: newQuantity,
            marketValue: newQuantity * existingPos.currentPrice
          });
        }
      }
      
      return newPositions;
    });

    setTrades(prev => [newTrade, ...prev]);

    toast({
      title: "Trade Executed",
      description: `${trade.type} ${trade.quantity} ${trade.symbol} at ₹${trade.price}`,
    });
  };

  const resetPortfolio = () => {
    setBalance(initialBalance);
    setPositions([]);
    setTrades([]);
    toast({
      title: "Portfolio Reset",
      description: "Paper trading portfolio has been reset to initial state",
    });
  };

  const totalPnL = positions.reduce((sum, pos) => sum + pos.pnl, 0);
  const dayPnL = trades
    .filter(trade => {
      const today = new Date();
      return trade.timestamp.toDateString() === today.toDateString();
    })
    .reduce((sum, trade) => {
      return sum + (trade.type === 'BUY' ? -1 : 1) * (trade.quantity * trade.price);
    }, 0);

  const value: PaperTradingContextType = {
    balance,
    positions,
    trades,
    totalPnL,
    dayPnL,
    executeTrade,
    resetPortfolio
  };

  return (
    <PaperTradingContext.Provider value={value}>
      {children}
    </PaperTradingContext.Provider>
  );
};
