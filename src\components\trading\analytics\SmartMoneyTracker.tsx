
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Users, TrendingUp, Eye, DollarSign } from "lucide-react";

interface InstitutionalFlow {
  institution: string;
  flow: string;
  change: string;
  impact: 'Bullish' | 'Bearish' | 'Neutral';
  sectors: string[];
}

interface BlockTrade {
  symbol: string;
  volume: string;
  value: string;
  type: 'Buy' | 'Sell';
  time: string;
  impact: 'High' | 'Medium' | 'Low';
}

interface InsiderActivity {
  company: string;
  insider: string;
  action: 'Buy' | 'Sell';
  stake: string;
  value: string;
  date: string;
}

interface SmartMoneyStat {
  label: string;
  value: string;
  color: string;
}

interface SmartMoneyTrackerProps {
  institutionalFlows?: InstitutionalFlow[];
  blockTrades?: BlockTrade[];
  insiderActivity?: InsiderActivity[];
  smartMoneyStats?: SmartMoneyStat[];
}

export const SmartMoneyTracker = ({
  institutionalFlows = [],
  blockTrades = [],
  insiderActivity = [],
  smartMoneyStats = []
}: SmartMoneyTrackerProps) => {
  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Users className="h-5 w-5 mr-2 text-blue-400" />
            Smart Money Activity Tracker
          </CardTitle>
        </CardHeader>
        <CardContent>
          {smartMoneyStats.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {smartMoneyStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className={`text-lg font-bold ${stat.color}`}>{stat.value}</div>
                  <div className="text-xs text-trading-muted">{stat.label}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-trading-muted">No smart money statistics available</p>
              <p className="text-sm text-trading-muted mt-1">Connect to institutional data feed</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="flows" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="flows" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Institution Flows
          </TabsTrigger>
          <TabsTrigger value="blocks" className="data-[state=active]:bg-trading-accent">
            <DollarSign className="h-4 w-4 mr-2" />
            Block Trades
          </TabsTrigger>
          <TabsTrigger value="insider" className="data-[state=active]:bg-trading-accent">
            <Eye className="h-4 w-4 mr-2" />
            Insider Activity
          </TabsTrigger>
          <TabsTrigger value="analysis" className="data-[state=active]:bg-trading-accent">
            <Users className="h-4 w-4 mr-2" />
            Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="flows">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Institutional Money Flow</CardTitle>
            </CardHeader>
            <CardContent>
              {institutionalFlows.length > 0 ? (
                <div className="space-y-3">
                  {institutionalFlows.map((flow, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{flow.institution}</div>
                        <Badge variant="outline" className={flow.impact === "Bullish" ? "text-green-400 border-green-400" : flow.impact === "Bearish" ? "text-red-400 border-red-400" : "text-yellow-400 border-yellow-400"}>
                          {flow.impact}
                        </Badge>
                        <div className="text-sm text-trading-muted">Sectors: {flow.sectors.join(", ")}</div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${flow.flow.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                          {flow.flow}
                        </div>
                        <div className={`text-xs ${flow.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                          {flow.change}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No institutional flow data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to FII/DII data feed</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="blocks">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Block Trade Monitor</CardTitle>
            </CardHeader>
            <CardContent>
              {blockTrades.length > 0 ? (
                <div className="space-y-3">
                  {blockTrades.map((trade, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{trade.symbol}</div>
                        <Badge variant="outline" className={trade.type === "Buy" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {trade.type}
                        </Badge>
                        <div className="text-sm text-trading-muted">
                          {trade.volume} shares • {trade.value}
                        </div>
                        <Badge variant="outline" className={trade.impact === "High" ? "text-red-400 border-red-400" : "text-yellow-400 border-yellow-400"}>
                          {trade.impact} Impact
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-trading-muted">{trade.time}</div>
                        <Button size="sm" variant="outline">Analyze</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No block trade data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to block trade monitoring feed</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insider">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Insider Activity Tracker</CardTitle>
            </CardHeader>
            <CardContent>
              {insiderActivity.length > 0 ? (
                <div className="space-y-3">
                  {insiderActivity.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{activity.company}</div>
                        <div className="text-sm text-trading-muted">{activity.insider}</div>
                        <Badge variant="outline" className={activity.action === "Buy" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {activity.action}
                        </Badge>
                        <div className="text-sm text-trading-muted">{activity.stake}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-400">{activity.value}</div>
                        <div className="text-xs text-trading-muted">{activity.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No insider activity data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to insider trading data feed</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Smart Money Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-trading-muted">No analysis data available</p>
                <p className="text-sm text-trading-muted mt-1">Connect to real data feeds to generate smart money insights</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
