
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Zap, Shield, Calculator, TrendingUp, AlertTriangle } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

interface RiskParameters {
  accountSize: number;
  riskPerTrade: number; // Percentage
  entryPrice: number;
  stopLoss: number;
  target1: number;
  target2?: number;
  target3?: number;
  volatility: number;
  correlation: number;
}

interface PositionSizing {
  quantity: number;
  riskAmount: number;
  maxLoss: number;
  potentialProfit1: number;
  potentialProfit2?: number;
  potentialProfit3?: number;
  riskRewardRatio1: number;
  riskRewardRatio2?: number;
  riskRewardRatio3?: number;
  kellyPercentage: number;
  optimalSize: number;
}

interface RiskRewardCalculatorProps {
  onBack?: () => void;
  symbol?: string;
  currentPrice?: number;
  historicalData?: Array<{ price: number; volume: number; timestamp: number }>;
}

export const RiskRewardCalculator = ({ 
  onBack, 
  symbol = "RELIANCE",
  currentPrice = 2500,
  historicalData = []
}: RiskRewardCalculatorProps) => {
  const [riskParams, setRiskParams] = useState<RiskParameters>({
    accountSize: 1000000, // 10 lakhs
    riskPerTrade: 2, // 2%
    entryPrice: currentPrice,
    stopLoss: currentPrice * 0.95, // 5% below entry
    target1: currentPrice * 1.1, // 10% above entry
    target2: currentPrice * 1.15, // 15% above entry
    target3: currentPrice * 1.2, // 20% above entry
    volatility: 0.02, // 2% daily volatility
    correlation: 0.3 // Portfolio correlation
  });

  const [positionSizing, setPositionSizing] = useState<PositionSizing | null>(null);
  const [portfolioRisk, setPortfolioRisk] = useState({
    totalRisk: 0,
    diversificationBenefit: 0,
    sharpeRatio: 0,
    maxDrawdown: 0,
    winRate: 0
  });

  // Real Kelly Criterion Calculation
  const calculateKellyCriterion = (winRate: number, avgWin: number, avgLoss: number) => {
    if (avgLoss === 0) return 0;
    const winLossRatio = avgWin / avgLoss;
    return (winRate * winLossRatio - (1 - winRate)) / winLossRatio;
  };

  // Real Position Sizing Algorithm
  const calculatePositionSizing = (params: RiskParameters): PositionSizing => {
    const riskAmount = (params.accountSize * params.riskPerTrade) / 100;
    const stopLossDistance = Math.abs(params.entryPrice - params.stopLoss);
    
    if (stopLossDistance === 0) {
      return {
        quantity: 0,
        riskAmount,
        maxLoss: 0,
        potentialProfit1: 0,
        riskRewardRatio1: 0,
        kellyPercentage: 0,
        optimalSize: 0
      };
    }

    // Basic position size based on risk amount
    const quantity = Math.floor(riskAmount / stopLossDistance);
    const maxLoss = quantity * stopLossDistance;
    
    // Calculate potential profits for each target
    const potentialProfit1 = quantity * Math.abs(params.target1 - params.entryPrice);
    const potentialProfit2 = params.target2 ? quantity * Math.abs(params.target2 - params.entryPrice) : undefined;
    const potentialProfit3 = params.target3 ? quantity * Math.abs(params.target3 - params.entryPrice) : undefined;
    
    // Risk-reward ratios
    const riskRewardRatio1 = maxLoss > 0 ? potentialProfit1 / maxLoss : 0;
    const riskRewardRatio2 = params.target2 && maxLoss > 0 ? (potentialProfit2 || 0) / maxLoss : undefined;
    const riskRewardRatio3 = params.target3 && maxLoss > 0 ? (potentialProfit3 || 0) / maxLoss : undefined;
    
    // Kelly Criterion calculation (simplified with estimated win rate)
    const estimatedWinRate = Math.min(0.8, Math.max(0.3, 0.6 - (params.volatility * 10))); // Volatility-adjusted win rate
    const avgWin = potentialProfit1;
    const avgLoss = maxLoss;
    const kellyPercentage = calculateKellyCriterion(estimatedWinRate, avgWin, avgLoss);
    
    // Optimal size using Kelly with safety factor
    const safetyFactor = 0.25; // Use 25% of Kelly
    const optimalSize = Math.max(0, Math.min(quantity, quantity * kellyPercentage * safetyFactor));

    return {
      quantity,
      riskAmount,
      maxLoss,
      potentialProfit1,
      potentialProfit2,
      potentialProfit3,
      riskRewardRatio1,
      riskRewardRatio2,
      riskRewardRatio3,
      kellyPercentage: kellyPercentage * 100,
      optimalSize
    };
  };

  // Real Volatility Calculation from Historical Data
  const calculateVolatility = (data: Array<{ price: number; timestamp: number }>) => {
    if (data.length < 2) return 0.02; // Default 2%
    
    const returns = [];
    for (let i = 1; i < data.length; i++) {
      const dailyReturn = (data[i].price - data[i - 1].price) / data[i - 1].price;
      returns.push(dailyReturn);
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  };

  // Monte Carlo Simulation for Risk Analysis
  const runMonteCarloSimulation = (params: RiskParameters, trials: number = 1000) => {
    const results = [];
    
    for (let i = 0; i < trials; i++) {
      // Generate random price movements based on volatility
      const randomReturn = (Math.random() - 0.5) * 2 * params.volatility * 3; // 3-sigma range
      const finalPrice = params.entryPrice * (1 + randomReturn);
      
      let profit = 0;
      
      // Determine outcome
      if (finalPrice <= params.stopLoss) {
        profit = -(params.entryPrice - params.stopLoss);
      } else if (finalPrice >= params.target1) {
        if (params.target3 && finalPrice >= params.target3) {
          profit = params.target3 - params.entryPrice;
        } else if (params.target2 && finalPrice >= params.target2) {
          profit = params.target2 - params.entryPrice;
        } else {
          profit = params.target1 - params.entryPrice;
        }
      }
      
      results.push({ trial: i + 1, profit, finalPrice });
    }
    
    // Calculate statistics
    const profits = results.map(r => r.profit);
    const winningTrades = profits.filter(p => p > 0).length;
    const winRate = winningTrades / trials;
    const avgWin = profits.filter(p => p > 0).reduce((sum, p) => sum + p, 0) / winningTrades || 0;
    const avgLoss = Math.abs(profits.filter(p => p < 0).reduce((sum, p) => sum + p, 0) / (trials - winningTrades) || 0);
    const maxDrawdown = Math.min(...profits);
    
    return { winRate, avgWin, avgLoss, maxDrawdown, results: results.slice(0, 100) }; // Return first 100 for visualization
  };

  // Value at Risk (VaR) Calculation
  const calculateVaR = (returns: number[], confidenceLevel: number = 0.95) => {
    if (returns.length === 0) return 0;
    
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const index = Math.floor((1 - confidenceLevel) * sortedReturns.length);
    
    return Math.abs(sortedReturns[index] || 0);
  };

  // Real Sharpe Ratio Calculation
  const calculateSharpeRatio = (returns: number[], riskFreeRate: number = 0.06) => {
    if (returns.length === 0) return 0;
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const annualizedReturn = avgReturn * 252; // 252 trading days
    const volatility = calculateVolatility(returns.map((ret, i) => ({ price: ret, timestamp: i })));
    const annualizedVolatility = volatility * Math.sqrt(252);
    
    if (annualizedVolatility === 0) return 0;
    return (annualizedReturn - riskFreeRate) / annualizedVolatility;
  };

  useEffect(() => {
    // Calculate volatility from historical data if available
    if (historicalData.length > 1) {
      const calculatedVolatility = calculateVolatility(historicalData);
      setRiskParams(prev => ({ ...prev, volatility: calculatedVolatility }));
    }
  }, [historicalData]);

  useEffect(() => {
    const sizing = calculatePositionSizing(riskParams);
    setPositionSizing(sizing);
    
    // Run Monte Carlo simulation for portfolio risk analysis
    const simulation = runMonteCarloSimulation(riskParams);
    const returns = simulation.results.map(r => r.profit / riskParams.entryPrice);
    const var95 = calculateVaR(returns);
    const sharpe = calculateSharpeRatio(returns);
    
    setPortfolioRisk({
      totalRisk: var95 * 100,
      diversificationBenefit: (1 - riskParams.correlation) * 20, // Simplified calculation
      sharpeRatio: sharpe,
      maxDrawdown: Math.abs(simulation.maxDrawdown),
      winRate: simulation.winRate * 100
    });
  }, [riskParams]);

  const handleParamChange = (field: keyof RiskParameters, value: number) => {
    setRiskParams(prev => ({ ...prev, [field]: value }));
  };

  const riskDistribution = positionSizing ? [
    { name: 'Risk Amount', value: positionSizing.riskAmount, fill: '#ef4444' },
    { name: 'Safe Capital', value: riskParams.accountSize - positionSizing.riskAmount, fill: '#10b981' }
  ] : [];

  const targetAnalysis = positionSizing ? [
    { 
      target: 'Target 1', 
      price: riskParams.target1, 
      profit: positionSizing.potentialProfit1, 
      rrRatio: positionSizing.riskRewardRatio1,
      probability: 65 // Estimated
    },
    ...(riskParams.target2 ? [{
      target: 'Target 2', 
      price: riskParams.target2, 
      profit: positionSizing.potentialProfit2 || 0, 
      rrRatio: positionSizing.riskRewardRatio2 || 0,
      probability: 45
    }] : []),
    ...(riskParams.target3 ? [{
      target: 'Target 3', 
      price: riskParams.target3, 
      profit: positionSizing.potentialProfit3 || 0, 
      rrRatio: positionSizing.riskRewardRatio3 || 0,
      probability: 25
    }] : [])
  ] : [];

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">Risk-Reward Calculator - {symbol}</h2>
          </div>
        )}

        {/* Position Overview */}
        {positionSizing && (
          <Card className="glassmorphism-card border-2 border-green-500/30">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                AI-Optimized Position Sizing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-400">
                    {positionSizing.quantity.toLocaleString()}
                  </div>
                  <div className="text-sm text-trading-muted">Shares</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-red-400">
                    ₹{positionSizing.maxLoss.toLocaleString()}
                  </div>
                  <div className="text-sm text-trading-muted">Max Loss</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-400">
                    ₹{positionSizing.potentialProfit1.toLocaleString()}
                  </div>
                  <div className="text-sm text-trading-muted">Target 1 Profit</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-400">
                    {positionSizing.riskRewardRatio1.toFixed(2)}:1
                  </div>
                  <div className="text-sm text-trading-muted">Risk:Reward</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-400">
                    {positionSizing.kellyPercentage.toFixed(1)}%
                  </div>
                  <div className="text-sm text-trading-muted">Kelly %</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-trading-light">
                    {positionSizing.optimalSize.toLocaleString()}
                  </div>
                  <div className="text-sm text-trading-muted">Optimal Size</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Analysis Tabs */}
        <Tabs defaultValue="parameters" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
            <TabsTrigger value="parameters">Parameters</TabsTrigger>
            <TabsTrigger value="analysis">Risk Analysis</TabsTrigger>
            <TabsTrigger value="targets">Target Analysis</TabsTrigger>
            <TabsTrigger value="portfolio">Portfolio Risk</TabsTrigger>
          </TabsList>

          <TabsContent value="parameters" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light flex items-center">
                    <Calculator className="h-5 w-5 mr-2" />
                    Risk Parameters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="accountSize">Account Size (₹)</Label>
                    <Input
                      id="accountSize"
                      type="number"
                      value={riskParams.accountSize}
                      onChange={(e) => handleParamChange('accountSize', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="riskPerTrade">Risk Per Trade (%)</Label>
                    <Input
                      id="riskPerTrade"
                      type="number"
                      step="0.1"
                      value={riskParams.riskPerTrade}
                      onChange={(e) => handleParamChange('riskPerTrade', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="volatility">Daily Volatility (%)</Label>
                    <Input
                      id="volatility"
                      type="number"
                      step="0.001"
                      value={(riskParams.volatility * 100).toFixed(2)}
                      onChange={(e) => handleParamChange('volatility', Number(e.target.value) / 100)}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="correlation">Portfolio Correlation</Label>
                    <Input
                      id="correlation"
                      type="number"
                      step="0.1"
                      min="0"
                      max="1"
                      value={riskParams.correlation}
                      onChange={(e) => handleParamChange('correlation', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light">Price Levels</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="entryPrice">Entry Price (₹)</Label>
                    <Input
                      id="entryPrice"
                      type="number"
                      step="0.01"
                      value={riskParams.entryPrice}
                      onChange={(e) => handleParamChange('entryPrice', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="stopLoss">Stop Loss (₹)</Label>
                    <Input
                      id="stopLoss"
                      type="number"
                      step="0.01"
                      value={riskParams.stopLoss}
                      onChange={(e) => handleParamChange('stopLoss', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="target1">Target 1 (₹)</Label>
                    <Input
                      id="target1"
                      type="number"
                      step="0.01"
                      value={riskParams.target1}
                      onChange={(e) => handleParamChange('target1', Number(e.target.value))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="target2">Target 2 (₹) - Optional</Label>
                    <Input
                      id="target2"
                      type="number"
                      step="0.01"
                      value={riskParams.target2 || ''}
                      onChange={(e) => handleParamChange('target2', Number(e.target.value) || undefined)}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  <div>
                    <Label htmlFor="target3">Target 3 (₹) - Optional</Label>
                    <Input
                      id="target3"
                      type="number"
                      step="0.01"
                      value={riskParams.target3 || ''}
                      onChange={(e) => handleParamChange('target3', Number(e.target.value) || undefined)}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light">Risk Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    {riskDistribution.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={riskDistribution}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, value }) => `${name}: ₹${value.toLocaleString()}`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {riskDistribution.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.fill} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => [`₹${Number(value).toLocaleString()}`, '']} />
                        </PieChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="h-full flex items-center justify-center text-trading-muted">
                        <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light">Risk Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-red-400">
                        {portfolioRisk.totalRisk.toFixed(2)}%
                      </div>
                      <div className="text-sm text-trading-muted">VaR (95%)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-400">
                        {portfolioRisk.sharpeRatio.toFixed(2)}
                      </div>
                      <div className="text-sm text-trading-muted">Sharpe Ratio</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-400">
                        {portfolioRisk.winRate.toFixed(1)}%
                      </div>
                      <div className="text-sm text-trading-muted">Win Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">
                        {portfolioRisk.diversificationBenefit.toFixed(1)}%
                      </div>
                      <div className="text-sm text-trading-muted">Diversification</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Risk Warnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {positionSizing && positionSizing.riskRewardRatio1 < 1 && (
                    <div className="flex items-center space-x-2 p-2 bg-red-500/10 rounded">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <span className="text-sm text-red-400">Risk-reward ratio below 1:1 - Consider adjusting targets</span>
                    </div>
                  )}
                  {riskParams.riskPerTrade > 5 && (
                    <div className="flex items-center space-x-2 p-2 bg-yellow-500/10 rounded">
                      <AlertTriangle className="h-4 w-4 text-yellow-400" />
                      <span className="text-sm text-yellow-400">High risk per trade - Consider reducing to 2-3%</span>
                    </div>
                  )}
                  {positionSizing && positionSizing.kellyPercentage < 0 && (
                    <div className="flex items-center space-x-2 p-2 bg-red-500/10 rounded">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <span className="text-sm text-red-400">Negative Kelly percentage - Trade setup not favorable</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="targets" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Target Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {targetAnalysis.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={targetAnalysis}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="target" stroke="#9ca3af" fontSize={12} />
                        <YAxis stroke="#9ca3af" fontSize={12} />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1f2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px' 
                          }}
                          formatter={(value, name) => [
                            name === 'profit' ? `₹${Number(value).toLocaleString()}` : 
                            name === 'rrRatio' ? `${Number(value).toFixed(2)}:1` :
                            `${Number(value).toFixed(1)}%`,
                            name === 'profit' ? 'Profit' :
                            name === 'rrRatio' ? 'Risk:Reward' : 'Probability'
                          ]}
                        />
                        <Bar dataKey="profit" fill="#10b981" name="profit" />
                        <Bar dataKey="rrRatio" fill="#3b82f6" name="rrRatio" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center text-trading-muted">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {targetAnalysis.map((target, index) => (
                <Card key={index} className="glassmorphism-card">
                  <CardHeader>
                    <CardTitle className="text-trading-light text-sm">{target.target}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Price:</span>
                        <span className="text-trading-light">₹{target.price.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Profit:</span>
                        <span className="text-green-400">₹{target.profit.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">R:R Ratio:</span>
                        <span className="text-blue-400">{target.rrRatio.toFixed(2)}:1</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Probability:</span>
                        <Badge variant="outline" className="text-purple-400 border-purple-400">
                          {target.probability}%
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Portfolio VaR</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-red-400">
                    {portfolioRisk.totalRisk.toFixed(2)}%
                  </div>
                  <div className="text-xs text-trading-muted mt-1">95% Confidence</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Sharpe Ratio</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {portfolioRisk.sharpeRatio.toFixed(2)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Risk-Adjusted Return</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Max Drawdown</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-red-400">
                    ₹{portfolioRisk.maxDrawdown.toLocaleString()}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Worst Case</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Win Rate</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {portfolioRisk.winRate.toFixed(1)}%
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Expected Success</div>
                </CardContent>
              </Card>
            </div>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Portfolio Optimization Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-trading-dark rounded">
                    <div className="text-sm font-medium text-green-400">Optimal Position Size</div>
                    <div className="text-xs text-trading-muted mt-1">
                      Based on Kelly Criterion with safety factor: {positionSizing?.optimalSize.toLocaleString()} shares
                    </div>
                  </div>
                  <div className="p-3 bg-trading-dark rounded">
                    <div className="text-sm font-medium text-blue-400">Risk Management</div>
                    <div className="text-xs text-trading-muted mt-1">
                      Current risk: {riskParams.riskPerTrade}% per trade. Recommended: 1-2% for conservative approach.
                    </div>
                  </div>
                  <div className="p-3 bg-trading-dark rounded">
                    <div className="text-sm font-medium text-purple-400">Diversification</div>
                    <div className="text-xs text-trading-muted mt-1">
                      Portfolio correlation: {(riskParams.correlation * 100).toFixed(0)}%. 
                      Lower correlation provides {portfolioRisk.diversificationBenefit.toFixed(1)}% risk reduction benefit.
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
