
import { BehaviorSubject, Observable } from 'rxjs';

export interface PaperTradeOrder {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  orderType: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  quantity: number;
  price: number;
  stopPrice?: number;
  instrumentType: 'EQUITY' | 'OPTION';
  optionDetails?: {
    strike: number;
    expiry: Date;
    optionType: 'CALL' | 'PUT';
    premium: number;
  };
  strategy: 'INTRADAY' | 'SWING';
  timestamp: Date;
  status: 'PENDING' | 'FILLED' | 'CANCELLED' | 'REJECTED';
  fillPrice?: number;
  commission: number;
}

export interface PaperPosition {
  id: string;
  symbol: string;
  instrumentType: 'EQUITY' | 'OPTION';
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  marketValue: number;
  dayChange: number;
  dayChangePercent: number;
  strategy: 'INTRADAY' | 'SWING';
  entryDate: Date;
  optionDetails?: {
    strike: number;
    expiry: Date;
    optionType: 'CALL' | 'PUT';
    premium: number;
    greeks: {
      delta: number;
      gamma: number;
      theta: number;
      vega: number;
      rho: number;
    };
  };
}

export interface PaperPortfolio {
  totalValue: number;
  availableCash: number;
  equityValue: number;
  optionsValue: number;
  totalPnL: number;
  dayPnL: number;
  marginUsed: number;
  positions: PaperPosition[];
  orders: PaperTradeOrder[];
}

export class PaperTradingService {
  private portfolioSubject = new BehaviorSubject<PaperPortfolio>({
    totalValue: 1000000,
    availableCash: 1000000,
    equityValue: 0,
    optionsValue: 0,
    totalPnL: 0,
    dayPnL: 0,
    marginUsed: 0,
    positions: [],
    orders: []
  });

  private positions: Map<string, PaperPosition> = new Map();
  private orders: Map<string, PaperTradeOrder> = new Map();
  private tradeHistory: PaperTradeOrder[] = [];

  constructor() {
    this.startMarketSimulation();
  }

  placeOrder(orderRequest: Omit<PaperTradeOrder, 'id' | 'timestamp' | 'status' | 'commission'>): string {
    const order: PaperTradeOrder = {
      ...orderRequest,
      id: `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      status: 'PENDING',
      commission: this.calculateCommission(orderRequest)
    };

    // Basic validation
    if (!this.validateOrder(order)) {
      order.status = 'REJECTED';
      return order.id;
    }

    this.orders.set(order.id, order);
    
    // For market orders, execute immediately
    if (order.orderType === 'MARKET') {
      this.executeOrder(order);
    }

    this.updatePortfolio();
    return order.id;
  }

  private validateOrder(order: PaperTradeOrder): boolean {
    const portfolio = this.portfolioSubject.value;
    const orderValue = order.quantity * order.price + order.commission;

    if (order.type === 'BUY' && portfolio.availableCash < orderValue) {
      console.warn('Insufficient funds for order');
      return false;
    }

    return true;
  }

  private executeOrder(order: PaperTradeOrder): void {
    const currentPrice = this.getCurrentPrice(order.symbol);
    order.fillPrice = currentPrice;
    order.status = 'FILLED';

    if (order.instrumentType === 'EQUITY') {
      this.processEquityOrder(order);
    } else if (order.instrumentType === 'OPTION') {
      this.processOptionOrder(order);
    }

    this.tradeHistory.push(order);
    this.orders.delete(order.id);
  }

  private processEquityOrder(order: PaperTradeOrder): void {
    const positionKey = `${order.symbol}_EQUITY`;
    let position = this.positions.get(positionKey);

    if (!position) {
      // Create new position
      position = {
        id: positionKey,
        symbol: order.symbol,
        instrumentType: 'EQUITY',
        quantity: order.type === 'BUY' ? order.quantity : -order.quantity,
        avgPrice: order.fillPrice!,
        currentPrice: order.fillPrice!,
        pnl: 0,
        pnlPercent: 0,
        marketValue: order.quantity * order.fillPrice!,
        dayChange: 0,
        dayChangePercent: 0,
        strategy: order.strategy,
        entryDate: new Date()
      };
    } else {
      // Update existing position
      const newQuantity = position.quantity + (order.type === 'BUY' ? order.quantity : -order.quantity);
      
      if (newQuantity === 0) {
        // Position closed
        this.positions.delete(positionKey);
        return;
      }

      const totalCost = (position.avgPrice * Math.abs(position.quantity)) + (order.fillPrice! * order.quantity);
      position.avgPrice = totalCost / Math.abs(newQuantity);
      position.quantity = newQuantity;
    }

    this.positions.set(positionKey, position);
  }

  private processOptionOrder(order: PaperTradeOrder): void {
    if (!order.optionDetails) return;

    const positionKey = `${order.symbol}_${order.optionDetails.strike}_${order.optionDetails.expiry.toISOString()}_${order.optionDetails.optionType}`;
    let position = this.positions.get(positionKey);

    const greeks = this.calculateGreeks(order.symbol, order.optionDetails);

    if (!position) {
      position = {
        id: positionKey,
        symbol: order.symbol,
        instrumentType: 'OPTION',
        quantity: order.type === 'BUY' ? order.quantity : -order.quantity,
        avgPrice: order.optionDetails.premium,
        currentPrice: order.optionDetails.premium,
        pnl: 0,
        pnlPercent: 0,
        marketValue: order.quantity * order.optionDetails.premium,
        dayChange: 0,
        dayChangePercent: 0,
        strategy: order.strategy,
        entryDate: new Date(),
        optionDetails: {
          ...order.optionDetails,
          greeks
        }
      };
    } else {
      const newQuantity = position.quantity + (order.type === 'BUY' ? order.quantity : -order.quantity);
      
      if (newQuantity === 0) {
        this.positions.delete(positionKey);
        return;
      }

      position.quantity = newQuantity;
    }

    this.positions.set(positionKey, position);
  }

  private calculateGreeks(symbol: string, optionDetails: any): any {
    // Simplified Greeks calculation - in real implementation, use proper options pricing model
    const S = this.getCurrentPrice(symbol);
    const K = optionDetails.strike;
    const T = this.getTimeToExpiry(optionDetails.expiry);
    const r = 0.05; // Risk-free rate
    const sigma = 0.2; // Implied volatility

    // Basic delta approximation
    const delta = optionDetails.optionType === 'CALL' ? 
      (S > K ? 0.5 + (S - K) / (2 * S) : 0.1) :
      (S < K ? -0.5 - (K - S) / (2 * S) : -0.1);

    return {
      delta: Math.max(-1, Math.min(1, delta)),
      gamma: 0.02,
      theta: -0.05,
      vega: 0.1,
      rho: 0.01
    };
  }

  private getTimeToExpiry(expiry: Date): number {
    const now = new Date();
    const diffTime = Math.abs(expiry.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays / 365;
  }

  private getCurrentPrice(symbol: string): number {
    // Simulate real-time price - in real implementation, fetch from market data
    const basePrice = symbol === 'NIFTY' ? 18000 : 
                     symbol === 'BANKNIFTY' ? 43000 :
                     symbol === 'RELIANCE' ? 2450 :
                     symbol === 'TCS' ? 3850 : 1000;
    
    const volatility = 0.02;
    const randomChange = (Math.random() - 0.5) * volatility;
    return basePrice * (1 + randomChange);
  }

  private calculateCommission(order: Omit<PaperTradeOrder, 'id' | 'timestamp' | 'status' | 'commission'>): number {
    if (order.instrumentType === 'EQUITY') {
      return Math.max(20, order.quantity * order.price * 0.0003); // 0.03% with minimum ₹20
    } else {
      return Math.max(20, order.quantity * 20); // Flat ₹20 per lot for options
    }
  }

  private startMarketSimulation(): void {
    setInterval(() => {
      this.updatePositionPrices();
      this.processIntraday();
      this.updatePortfolio();
    }, 5000); // Update every 5 seconds
  }

  private updatePositionPrices(): void {
    for (const [key, position] of this.positions) {
      const newPrice = this.getCurrentPrice(position.symbol);
      const oldPrice = position.currentPrice;
      
      position.currentPrice = newPrice;
      position.dayChange = newPrice - oldPrice;
      position.dayChangePercent = ((newPrice - oldPrice) / oldPrice) * 100;
      
      if (position.instrumentType === 'EQUITY') {
        position.pnl = (newPrice - position.avgPrice) * position.quantity;
        position.pnlPercent = ((newPrice - position.avgPrice) / position.avgPrice) * 100;
        position.marketValue = Math.abs(position.quantity) * newPrice;
      } else if (position.optionDetails) {
        // Update option greeks and pricing
        position.optionDetails.greeks = this.calculateGreeks(position.symbol, position.optionDetails);
        position.pnl = (newPrice - position.avgPrice) * position.quantity;
        position.pnlPercent = ((newPrice - position.avgPrice) / position.avgPrice) * 100;
        position.marketValue = Math.abs(position.quantity) * newPrice;
      }
    }
  }

  private processIntraday(): void {
    const now = new Date();
    const cutoffTime = new Date();
    cutoffTime.setHours(15, 30, 0, 0); // 3:30 PM market close

    if (now > cutoffTime) {
      // Auto-close intraday positions
      for (const [key, position] of this.positions) {
        if (position.strategy === 'INTRADAY' && position.entryDate.toDateString() === now.toDateString()) {
          console.log(`Auto-closing intraday position: ${position.symbol}`);
          this.closePosition(position.id, 'Intraday auto-close');
        }
      }
    }
  }

  closePosition(positionId: string, reason: string): void {
    const position = this.positions.get(positionId);
    if (!position) return;

    // Create closing order
    const closeOrder: PaperTradeOrder = {
      id: `CLOSE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol: position.symbol,
      type: position.quantity > 0 ? 'SELL' : 'BUY',
      orderType: 'MARKET',
      quantity: Math.abs(position.quantity),
      price: position.currentPrice,
      instrumentType: position.instrumentType,
      optionDetails: position.optionDetails,
      strategy: position.strategy,
      timestamp: new Date(),
      status: 'FILLED',
      fillPrice: position.currentPrice,
      commission: this.calculateCommission({
        symbol: position.symbol,
        type: position.quantity > 0 ? 'SELL' : 'BUY',
        orderType: 'MARKET',
        quantity: Math.abs(position.quantity),
        price: position.currentPrice,
        instrumentType: position.instrumentType,
        strategy: position.strategy
      })
    };

    this.tradeHistory.push(closeOrder);
    this.positions.delete(positionId);
    this.updatePortfolio();

    console.log(`Position closed: ${position.symbol} - ${reason}, P&L: ₹${position.pnl.toFixed(2)}`);
  }

  private updatePortfolio(): void {
    const positions = Array.from(this.positions.values());
    const orders = Array.from(this.orders.values());

    const equityValue = positions
      .filter(p => p.instrumentType === 'EQUITY')
      .reduce((sum, p) => sum + p.marketValue, 0);

    const optionsValue = positions
      .filter(p => p.instrumentType === 'OPTION')
      .reduce((sum, p) => sum + p.marketValue, 0);

    const totalPnL = positions.reduce((sum, p) => sum + p.pnl, 0);
    const dayPnL = positions.reduce((sum, p) => sum + p.dayChange * Math.abs(p.quantity), 0);

    const portfolio: PaperPortfolio = {
      totalValue: 1000000 + totalPnL,
      availableCash: 1000000 - equityValue - optionsValue,
      equityValue,
      optionsValue,
      totalPnL,
      dayPnL,
      marginUsed: optionsValue * 0.1, // 10% margin for options
      positions,
      orders
    };

    this.portfolioSubject.next(portfolio);
  }

  getPortfolioStream(): Observable<PaperPortfolio> {
    return this.portfolioSubject.asObservable();
  }

  getTradeHistory(): PaperTradeOrder[] {
    return [...this.tradeHistory];
  }

  resetPortfolio(): void {
    this.positions.clear();
    this.orders.clear();
    this.tradeHistory = [];
    this.updatePortfolio();
  }
}

export const paperTradingService = new PaperTradingService();
