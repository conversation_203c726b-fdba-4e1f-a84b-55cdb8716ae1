
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  History, 
  Target, 
  RefreshCw,
  Activity 
} from "lucide-react";

interface Position {
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  marketValue: number;
}

interface Trade {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  timestamp: Date;
}

interface PaperTradingDashboardProps {
  balance?: number;
  positions?: Position[];
  trades?: Trade[];
  totalPnL?: number;
  dayPnL?: number;
  onExecuteTrade?: (symbol: string, type: 'BUY' | 'SELL', quantity: number, price: number) => void;
  onResetPortfolio?: () => void;
}

export const PaperTradingDashboard = ({
  balance = 1000000, // Starting with 10L virtual cash
  positions = [],
  trades = [],
  totalPnL = 0,
  dayPnL = 0,
  onExecuteTrade,
  onResetPortfolio
}: PaperTradingDashboardProps) => {
  const totalPortfolioValue = balance + positions.reduce((sum, pos) => sum + pos.marketValue, 0);
  const totalPnLPercent = totalPnL > 0 ? (totalPnL / (totalPortfolioValue - totalPnL)) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Paper Trading Portfolio
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm"
            onClick={onResetPortfolio}
            className="text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Reset Portfolio
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-trading-light">
                ₹{totalPortfolioValue.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Total Value</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                ₹{balance.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Available Cash</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₹{totalPnL.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">
                Total P&L ({totalPnLPercent.toFixed(2)}%)
              </div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${dayPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₹{dayPnL.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Day P&L</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{positions.length}</div>
              <div className="text-sm text-trading-muted">Active Positions</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="positions" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="positions" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Positions
          </TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-trading-accent">
            <History className="h-4 w-4 mr-2" />
            Trade History
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="positions">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Current Positions</CardTitle>
            </CardHeader>
            <CardContent>
              {positions.length === 0 ? (
                <div className="text-center py-8 text-trading-muted">
                  <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No positions held</p>
                  <p className="text-xs mt-1">Start trading to see your positions here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {positions.map((position, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                        <div>
                          <div className="font-semibold text-trading-light text-lg">
                            {position.symbol}
                          </div>
                          <div className="text-xs text-trading-muted">
                            Qty: {position.quantity}
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Avg Price</div>
                          <div className="text-trading-light">₹{position.avgPrice.toFixed(2)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Current Price</div>
                          <div className="text-trading-light">₹{position.currentPrice.toFixed(2)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">P&L</div>
                          <div className={`font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            ₹{position.pnl.toLocaleString()}
                          </div>
                          <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            ({position.pnlPercent > 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%)
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Market Value</div>
                          <div className="text-trading-light">₹{position.marketValue.toLocaleString()}</div>
                        </div>
                        <div className="flex flex-col space-y-2">
                          <Button size="sm" variant="outline" className="text-xs">
                            Add More
                          </Button>
                          <Button size="sm" variant="destructive" className="text-xs">
                            Close Position
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Trade History</CardTitle>
            </CardHeader>
            <CardContent>
              {trades.length === 0 ? (
                <div className="text-center py-8 text-trading-muted">
                  <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No trades executed yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {trades.slice(0, 10).map((trade) => (
                    <div key={trade.id} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <Badge variant="outline" className={trade.type === 'BUY' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                          {trade.type}
                        </Badge>
                        <div className="text-trading-light font-medium">{trade.symbol}</div>
                        <div className="text-sm text-trading-muted">
                          {trade.quantity} @ ₹{trade.price}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-trading-light">
                          ₹{(trade.quantity * trade.price).toLocaleString()}
                        </div>
                        <div className="text-xs text-trading-muted">
                          {trade.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-trading-dark rounded">
                  <div className="text-2xl font-bold text-blue-400">{trades.length}</div>
                  <div className="text-sm text-trading-muted">Total Trades</div>
                </div>
                <div className="text-center p-4 bg-trading-dark rounded">
                  <div className="text-2xl font-bold text-green-400">
                    {trades.filter(t => t.type === 'BUY').length}
                  </div>
                  <div className="text-sm text-trading-muted">Buy Orders</div>
                </div>
                <div className="text-center p-4 bg-trading-dark rounded">
                  <div className="text-2xl font-bold text-red-400">
                    {trades.filter(t => t.type === 'SELL').length}
                  </div>
                  <div className="text-sm text-trading-muted">Sell Orders</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
