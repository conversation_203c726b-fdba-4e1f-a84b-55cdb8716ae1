
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, DollarSign, Target, AlertTriangle } from "lucide-react";

interface Position {
  symbol: string;
  qty: number;
  avgPrice: number;
  ltp: number;
  pnl: number;
  pnlPct: number;
  type: 'LONG' | 'SHORT';
}

interface Order {
  symbol: string;
  type: 'BUY' | 'SELL';
  qty: number;
  price: number;
  status: 'PENDING' | 'EXECUTED' | 'CANCELLED';
}

interface PaperTradeDashboardProps {
  balance?: number;
  unrealizedPnL?: number;
  unrealizedPnLPercent?: number;
  positions?: Position[];
  orders?: Order[];
  winRate?: number;
  onPlaceTrade?: (symbol: string, type: 'BUY' | 'SELL', qty: number, price: number) => void;
  onModifyPosition?: (symbol: string) => void;
  onExitPosition?: (symbol: string) => void;
  onCancelOrder?: (orderId: string) => void;
}

export const PaperTradeDashboard = ({
  balance = 0,
  unrealizedPnL = 0,
  unrealizedPnLPercent = 0,
  positions = [],
  orders = [],
  winRate = 0,
  onPlaceTrade,
  onModifyPosition,
  onExitPosition,
  onCancelOrder
}: PaperTradeDashboardProps) => {
  return (
    <div className="space-y-4">
      {/* Paper Trading Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Paper Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">₹{balance.toLocaleString()}</div>
            <div className="text-xs text-blue-400">Virtual Capital</div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Unrealized P&L</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${unrealizedPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {unrealizedPnL >= 0 ? '+' : ''}₹{unrealizedPnL.toLocaleString()}
            </div>
            <div className={`text-xs ${unrealizedPnLPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {unrealizedPnLPercent >= 0 ? '+' : ''}{unrealizedPnLPercent.toFixed(2)}%
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Active Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">{positions.length}</div>
            <div className="text-xs text-trading-muted">
              {positions.filter(p => p.qty > 0).length} Long • {positions.filter(p => p.qty < 0).length} Short
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Win Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-400">{winRate.toFixed(1)}%</div>
            <div className="text-xs text-green-400">Historical</div>
          </CardContent>
        </Card>
      </div>

      {/* Paper Positions */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Paper Trading Positions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {positions.length === 0 ? (
            <div className="text-center py-8 text-trading-muted">
              <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No positions held</p>
              <p className="text-xs mt-1">Start trading to see your positions here</p>
            </div>
          ) : (
            <div className="space-y-3">
              {positions.map((position, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="font-medium text-trading-light">{position.symbol}</div>
                      <div className="text-xs text-trading-muted">
                        Qty: {position.qty} • Avg: ₹{position.avgPrice.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-trading-light">₹{position.ltp.toFixed(2)}</div>
                    <div className={`text-xs ${position.pnl >= 0 ? "text-green-400" : "text-red-400"}`}>
                      {position.pnl >= 0 ? "+" : ""}₹{position.pnl.toFixed(2)} ({position.pnlPct >= 0 ? "+" : ""}{position.pnlPct.toFixed(2)}%)
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onModifyPosition?.(position.symbol)}
                    >
                      Modify
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => onExitPosition?.(position.symbol)}
                    >
                      Exit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paper Orders */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Paper Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-8 text-trading-muted">
              <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No orders placed</p>
            </div>
          ) : (
            <div className="space-y-3">
              {orders.map((order, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-4">
                    <Badge variant="outline" className={order.type === "BUY" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                      {order.type}
                    </Badge>
                    <div>
                      <div className="font-medium text-trading-light">{order.symbol}</div>
                      <div className="text-xs text-trading-muted">
                        Qty: {order.qty} • Price: ₹{order.price.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="outline" 
                      className={
                        order.status === "EXECUTED" ? "text-green-400 border-green-400" :
                        order.status === "PENDING" ? "text-yellow-400 border-yellow-400" :
                        "text-red-400 border-red-400"
                      }
                    >
                      {order.status}
                    </Badge>
                    {order.status === "PENDING" && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => onCancelOrder?.(order.symbol)}
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Quick Paper Trade
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="bg-green-600 hover:bg-green-700">
              Paper BUY
            </Button>
            <Button className="bg-red-600 hover:bg-red-700">
              Paper SELL
            </Button>
            <Button variant="outline">
              Set Alert
            </Button>
            <Button variant="outline">
              Reset Portfolio
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
