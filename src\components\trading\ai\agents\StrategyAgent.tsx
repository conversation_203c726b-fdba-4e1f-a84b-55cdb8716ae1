
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { TradingStrategy } from "@/types/strategies";
import { Brain, Activity, TrendingUp, Zap } from "lucide-react";

interface PerformanceData {
  trades: number;
  winRate: number;
  pnl: number;
  confidence: number;
}

interface StrategyAgentProps {
  strategy: TradingStrategy;
  isActive: boolean;
  onToggle: (active: boolean) => void;
  performance?: PerformanceData;
}

export const StrategyAgent = ({ 
  strategy, 
  isActive, 
  onToggle, 
  performance 
}: StrategyAgentProps) => {
  const getCategoryIcon = () => {
    switch (strategy.category) {
      case 'Swing': return TrendingUp;
      case 'Intraday': return Activity;
      case 'Scalping': return Zap;
      case 'Price Action': return Brain;
      case 'Options': return Activity;
      default: return Brain;
    }
  };

  const getCategoryColor = () => {
    switch (strategy.category) {
      case 'Swing': return 'text-green-400 border-green-400';
      case 'Intraday': return 'text-blue-400 border-blue-400';
      case 'Scalping': return 'text-purple-400 border-purple-400';
      case 'Price Action': return 'text-orange-400 border-orange-400';
      case 'Options': return 'text-yellow-400 border-yellow-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const Icon = getCategoryIcon();

  // Use performance data if provided, otherwise show placeholders
  const displayData = performance || {
    trades: 0,
    winRate: 0,
    pnl: 0,
    confidence: 0
  };

  return (
    <Card className={`bg-trading-darker border-trading-border ${isActive ? 'border-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xs text-trading-light flex items-center">
            <Icon className="h-3 w-3 mr-1" />
            {strategy.name}
          </CardTitle>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-500' : 'bg-gray-500'}`}></div>
            <Switch 
              checked={isActive} 
              onCheckedChange={onToggle}
            />
          </div>
        </div>
        <div className="flex space-x-1">
          <Badge variant="outline" className={`text-xs ${getCategoryColor()}`}>
            {strategy.category}
          </Badge>
          <Badge variant="outline" className="text-xs text-trading-muted">
            {strategy.timeframe}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="text-center">
            <div className="text-green-400 font-medium">
              {performance ? `${displayData.winRate}%` : '--'}
            </div>
            <div className="text-trading-muted">Win Rate</div>
          </div>
          <div className="text-center">
            <div className="text-blue-400 font-medium">
              {performance ? displayData.trades : '--'}
            </div>
            <div className="text-trading-muted">Trades</div>
          </div>
          <div className="text-center">
            <div className={`font-medium ${performance ? (displayData.pnl >= 0 ? 'text-green-400' : 'text-red-400') : 'text-trading-muted'}`}>
              {performance ? `₹${displayData.pnl.toLocaleString()}` : '--'}
            </div>
            <div className="text-trading-muted">P&L</div>
          </div>
          <div className="text-center">
            <div className="text-purple-400 font-medium">
              {performance ? `${displayData.confidence}%` : '--'}
            </div>
            <div className="text-trading-muted">Confidence</div>
          </div>
        </div>

        <Button size="sm" variant="outline" className="w-full text-xs">
          Configure Agent
        </Button>
      </CardContent>
    </Card>
  );
};
