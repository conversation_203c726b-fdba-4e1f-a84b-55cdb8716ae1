import { DatabaseService, TickData } from './DatabaseService';
import { BrokerAPIService } from './BrokerAPIService';
import { AlertService } from './AlertService';
import { ETLService } from './ETLService';
import { RealAIAgentService, MarketData } from './RealAIAgentService';
import { MLPredictionService } from './MLPredictionService';
import { TechnicalIndicatorService } from './TechnicalIndicatorService';

export interface TradingStrategy {
  id: string;
  name: string;
  type: 'INTRADAY' | 'SWING' | 'SCALPING' | 'OPTIONS' | 'MULTIBAG';
  isActive: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  maxPositions: number;
  maxDrawdown: number;
  stopLoss: number;
  takeProfit: number;
  entryConditions: string[];
  exitConditions: string[];
}

export interface TradingSignal {
  id: string;
  symbol: string;
  action: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  stopLoss: number;
  takeProfit: number;
  confidence: number;
  strategy: string;
  timestamp: string;
  reasoning: string[];
  aiAnalysis: any;
  technicalAnalysis: any;
}

export interface Position {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  quantity: number;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  timestamp: string;
  strategy: string;
  stopLoss: number;
  takeProfit: number;
  unrealizedPnL: number;
  realizedPnL: number;
}

export class TradingEngineService {
  private dbService: DatabaseService;
  private brokerService: BrokerAPIService | null = null;
  private alertService: AlertService;
  private etlService: ETLService;
  private aiService: RealAIAgentService;
  private mlService: MLPredictionService;
  private strategies: Map<string, TradingStrategy> = new Map();
  private positions: Map<string, Position> = new Map();
  private signals: TradingSignal[] = [];
  private marketData: Map<string, TickData[]> = new Map();
  private isRunning = false;

  constructor(
    dbService: DatabaseService,
    alertService: AlertService,
    etlService: ETLService
  ) {
    this.dbService = dbService;
    this.alertService = alertService;
    this.etlService = etlService;
    this.aiService = new RealAIAgentService();
    this.mlService = new MLPredictionService();
    this.initializeRealStrategies();
  }

  setBrokerService(brokerService: BrokerAPIService): void {
    this.brokerService = brokerService;
  }

  private initializeRealStrategies(): void {
    const realStrategies: TradingStrategy[] = [
      {
        id: 'ai-momentum',
        name: 'AI Momentum Strategy',
        type: 'INTRADAY',
        isActive: true,
        riskLevel: 'MEDIUM',
        maxPositions: 3,
        maxDrawdown: 5,
        stopLoss: 2,
        takeProfit: 4,
        entryConditions: [
          'RSI < 30 AND price below VWAP',
          'MACD bullish crossover',
          'AI confidence > 75%'
        ],
        exitConditions: [
          'Take profit reached',
          'Stop loss triggered',
          'AI signal changes to SELL'
        ]
      },
      {
        id: 'ml-pattern',
        name: 'ML Pattern Recognition',
        type: 'SWING',
        isActive: true,
        riskLevel: 'LOW',
        maxPositions: 2,
        maxDrawdown: 8,
        stopLoss: 3,
        takeProfit: 9,
        entryConditions: [
          'Pattern recognition confidence > 70%',
          'Volume above 1.5x average',
          'Risk-reward ratio > 1:2'
        ],
        exitConditions: [
          'Pattern target reached',
          'Pattern invalidated',
          'Time-based exit (5 days)'
        ]
      }
    ];

    realStrategies.forEach(strategy => {
      this.strategies.set(strategy.id, strategy);
    });
  }

  async start(): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('Starting Real Trading Engine...');

    await this.etlService.start();

    // Subscribe to real market data
    this.dbService.subscribe('tick_NIFTY', (data) => this.processRealTickData(data));
    this.dbService.subscribe('tick_BANKNIFTY', (data) => this.processRealTickData(data));
    this.dbService.subscribe('tick_SENSEX', (data) => this.processRealTickData(data));

    this.startRealStrategyLoop();

    await this.alertService.sendSystemAlert({
      component: 'Real Trading Engine',
      status: 'ONLINE',
      message: 'Real trading engine started with AI and ML integration'
    });
  }

  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    await this.etlService.stop();
    
    await this.alertService.sendSystemAlert({
      component: 'Real Trading Engine',
      status: 'OFFLINE',
      message: 'Real trading engine stopped'
    });
  }

  private async processRealTickData(tickData: TickData): Promise<void> {
    // Store real market data
    if (!this.marketData.has(tickData.symbol)) {
      this.marketData.set(tickData.symbol, []);
    }
    
    const symbolData = this.marketData.get(tickData.symbol)!;
    symbolData.push(tickData);
    
    // Keep only last 500 ticks for performance
    if (symbolData.length > 500) {
      symbolData.splice(0, symbolData.length - 500);
    }

    // Update positions with real prices
    for (const [id, position] of this.positions) {
      if (position.symbol === tickData.symbol) {
        const updatedPosition = {
          ...position,
          currentPrice: tickData.ltp,
          pnl: this.calculateRealPnL(position, tickData.ltp),
          pnlPercent: ((tickData.ltp - position.entryPrice) / position.entryPrice) * 100,
          unrealizedPnL: this.calculateUnrealizedPnL(position, tickData.ltp)
        };
        this.positions.set(id, updatedPosition);

        await this.checkRealExitConditions(updatedPosition);
      }
    }

    // Generate real AI/ML signals
    if (symbolData.length >= 50) {
      await this.generateRealSignals(tickData.symbol, symbolData);
    }
  }

  private calculateRealPnL(position: Position, currentPrice: number): number {
    const multiplier = position.side === 'LONG' ? 1 : -1;
    const priceDiff = currentPrice - position.entryPrice;
    return priceDiff * position.quantity * multiplier;
  }

  private calculateUnrealizedPnL(position: Position, currentPrice: number): number {
    // Same as PnL for now, but can be extended for complex instruments
    return this.calculateRealPnL(position, currentPrice);
  }

  private async checkRealExitConditions(position: Position): Promise<void> {
    const strategy = this.strategies.get(position.strategy);
    if (!strategy) return;

    let shouldExit = false;
    let exitReason = '';

    // Stop loss check
    if (Math.abs(position.pnlPercent) >= strategy.stopLoss) {
      shouldExit = true;
      exitReason = `Stop loss triggered: ${position.pnlPercent.toFixed(2)}%`;
    }

    // Take profit check
    if (position.pnlPercent >= strategy.takeProfit) {
      shouldExit = true;
      exitReason = `Take profit reached: ${position.pnlPercent.toFixed(2)}%`;
    }

    // AI-based exit decision
    const marketData = this.marketData.get(position.symbol);
    if (marketData && marketData.length >= 20) {
      try {
        const formattedData: MarketData[] = marketData.slice(-50).map(tick => ({
          symbol: tick.symbol,
          timestamp: new Date(tick.timestamp).getTime(),
          open: tick.open,
          high: tick.high,
          low: tick.low,
          close: tick.close,
          volume: tick.volume
        }));

        const aiAnalysis = await this.aiService.analyzeMarket(formattedData);
        
        // Exit if AI strongly suggests opposite direction
        if ((position.side === 'LONG' && aiAnalysis.signal === 'STRONG_SELL') ||
            (position.side === 'SHORT' && aiAnalysis.signal === 'STRONG_BUY')) {
          shouldExit = true;
          exitReason = `AI signal changed: ${aiAnalysis.signal} (confidence: ${aiAnalysis.confidence}%)`;
        }
      } catch (error) {
        console.error('Error in AI exit analysis:', error);
      }
    }

    if (shouldExit) {
      await this.closePosition(position, exitReason);
    }
  }

  private async closePosition(position: Position, reason: string): Promise<void> {
    console.log(`Closing position: ${position.symbol} - ${reason}`);
    
    // Calculate final P&L
    const finalPnL = this.calculateRealPnL(position, position.currentPrice);
    
    // Update position with realized P&L
    const closedPosition = {
      ...position,
      realizedPnL: finalPnL,
      unrealizedPnL: 0
    };

    // Remove from active positions
    this.positions.delete(position.id);

    // Send execution alert with all required properties
    await this.alertService.sendExecutionAlert({
      orderId: position.id,
      symbol: position.symbol,
      status: 'FILLED',
      quantity: position.quantity,
      price: position.currentPrice,
      pnl: finalPnL,
      reason: reason,
      message: `Position closed: ${position.symbol} - ${reason}, P&L: ${finalPnL.toFixed(2)}`
    });
  }

  private async generateRealSignals(symbol: string, marketData: TickData[]): Promise<void> {
    try {
      // Convert tick data to format expected by AI/ML services
      const formattedData: MarketData[] = marketData.map(tick => ({
        symbol: tick.symbol,
        timestamp: new Date(tick.timestamp).getTime(),
        open: tick.open,
        high: tick.high,
        low: tick.low,
        close: tick.close,
        volume: tick.volume
      }));

      // Get AI analysis
      const aiAnalysis = await this.aiService.analyzeMarket(formattedData);
      
      // Get ML predictions
      const mlPredictions = await this.mlService.generatePrediction(symbol, formattedData);

      // Get technical analysis
      const prices = formattedData.map(d => d.close);
      const rsi = TechnicalIndicatorService.calculateRSI(prices, 14);
      const vwap = TechnicalIndicatorService.calculateVWAP(
        prices, 
        formattedData.map(d => d.volume),
        formattedData.map(d => d.timestamp)
      );

      const currentPrice = prices[prices.length - 1];
      const currentRSI = rsi[rsi.length - 1]?.value || 50;
      const currentVWAP = vwap[vwap.length - 1]?.vwap || currentPrice;

      // Generate signals based on real analysis
      for (const [strategyId, strategy] of this.strategies) {
        if (!strategy.isActive) continue;

        const signal = await this.evaluateRealStrategy(
          strategy,
          symbol,
          currentPrice,
          aiAnalysis,
          mlPredictions,
          { rsi: currentRSI, vwap: currentVWAP }
        );

        if (signal) {
          this.signals.push(signal);
          
          await this.alertService.sendSignalAlert({
            symbol: signal.symbol,
            action: signal.action,
            price: signal.price,
            confidence: signal.confidence,
            agent: signal.strategy,
            reasoning: signal.reasoning.join('; '),
            strategy: signal.strategy
          });
        }
      }
    } catch (error) {
      console.error(`Error generating signals for ${symbol}:`, error);
    }
  }

  private async evaluateRealStrategy(
    strategy: TradingStrategy,
    symbol: string,
    currentPrice: number,
    aiAnalysis: any,
    mlPredictions: any[],
    technicals: any
  ): Promise<TradingSignal | null> {
    
    const reasoning: string[] = [];
    let confidence = 0;
    let action: 'BUY' | 'SELL' | null = null;

    // AI-based decision
    if (aiAnalysis.signal === 'STRONG_BUY' || aiAnalysis.signal === 'BUY') {
      action = 'BUY';
      confidence += aiAnalysis.confidence * 0.4;
      reasoning.push(`AI Analysis: ${aiAnalysis.signal} (${aiAnalysis.confidence}%)`);
    } else if (aiAnalysis.signal === 'STRONG_SELL' || aiAnalysis.signal === 'SELL') {
      action = 'SELL';
      confidence += aiAnalysis.confidence * 0.4;
      reasoning.push(`AI Analysis: ${aiAnalysis.signal} (${aiAnalysis.confidence}%)`);
    }

    // ML predictions consensus
    const buyPredictions = mlPredictions.filter(p => p.direction === 'BUY');
    const sellPredictions = mlPredictions.filter(p => p.direction === 'SELL');
    
    if (buyPredictions.length > sellPredictions.length) {
      const avgConfidence = buyPredictions.reduce((sum, p) => sum + p.confidence, 0) / buyPredictions.length;
      confidence += avgConfidence * 0.3;
      reasoning.push(`ML Consensus: BUY (${buyPredictions.length}/${mlPredictions.length} models)`);
    } else if (sellPredictions.length > buyPredictions.length) {
      const avgConfidence = sellPredictions.reduce((sum, p) => sum + p.confidence, 0) / sellPredictions.length;
      confidence += avgConfidence * 0.3;
      reasoning.push(`ML Consensus: SELL (${sellPredictions.length}/${mlPredictions.length} models)`);
    }

    // Technical confirmation
    if (technicals.rsi < 30 && currentPrice < technicals.vwap) {
      if (action === 'BUY') confidence += 15;
      reasoning.push('Technical: RSI oversold + price below VWAP');
    } else if (technicals.rsi > 70 && currentPrice > technicals.vwap) {
      if (action === 'SELL') confidence += 15;
      reasoning.push('Technical: RSI overbought + price above VWAP');
    }

    // Risk management checks
    if (!this.canTakePosition(strategy)) {
      return null;
    }

    if (confidence < 65 || !action) {
      return null; // Not confident enough
    }

    // Calculate position size based on risk
    const quantity = this.calculatePositionSize(strategy, currentPrice, confidence);
    
    return {
      id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol,
      action,
      quantity,
      price: currentPrice,
      stopLoss: action === 'BUY' ? 
        currentPrice * (1 - strategy.stopLoss / 100) : 
        currentPrice * (1 + strategy.stopLoss / 100),
      takeProfit: action === 'BUY' ? 
        currentPrice * (1 + strategy.takeProfit / 100) : 
        currentPrice * (1 - strategy.takeProfit / 100),
      confidence,
      strategy: strategy.id,
      timestamp: new Date().toISOString(),
      reasoning,
      aiAnalysis,
      technicalAnalysis: technicals
    };
  }

  private calculatePositionSize(strategy: TradingStrategy, price: number, confidence: number): number {
    // Kelly Criterion-inspired position sizing
    const baseSize = 100; // Base position size
    const riskFactor = strategy.riskLevel === 'HIGH' ? 1.5 : strategy.riskLevel === 'LOW' ? 0.5 : 1.0;
    const confidenceFactor = confidence / 100;
    
    return Math.floor(baseSize * riskFactor * confidenceFactor);
  }

  private startRealStrategyLoop(): void {
    setInterval(async () => {
      if (!this.isRunning) return;
      
      await this.executeRealSignals();
      await this.monitorRealRisk();
      await this.updateModelPerformance();
    }, 5000);
  }

  private async executeRealSignals(): Promise<void> {
    const executableSignals = this.signals.filter(signal => 
      signal.confidence > 70 && this.canTakePosition(this.strategies.get(signal.strategy)!)
    );

    for (const signal of executableSignals) {
      await this.executeRealSignal(signal);
    }
  }

  private async executeRealSignal(signal: TradingSignal): Promise<void> {
    try {
      const position: Position = {
        id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol: signal.symbol,
        side: signal.action === 'BUY' ? 'LONG' : 'SHORT',
        quantity: signal.quantity,
        entryPrice: signal.price,
        currentPrice: signal.price,
        pnl: 0,
        pnlPercent: 0,
        timestamp: signal.timestamp,
        strategy: signal.strategy,
        stopLoss: signal.stopLoss,
        takeProfit: signal.takeProfit,
        unrealizedPnL: 0,
        realizedPnL: 0
      };

      this.positions.set(position.id, position);
      
      // Remove executed signal
      this.signals = this.signals.filter(s => s.id !== signal.id);
      
      console.log(`Executed real signal: ${signal.action} ${signal.symbol} @ ${signal.price} (Confidence: ${signal.confidence}%)`);
      
      // Log to database for analysis
      await this.dbService.logTrade({
        symbol: signal.symbol,
        action: signal.action,
        price: signal.price,
        quantity: signal.quantity,
        strategy: signal.strategy,
        confidence: signal.confidence,
        reasoning: signal.reasoning.join('; '),
        timestamp: signal.timestamp
      });
      
    } catch (error) {
      console.error('Error executing signal:', error);
    }
  }

  private async monitorRealRisk(): Promise<void> {
    const activePositions = Array.from(this.positions.values());
    if (activePositions.length === 0) return;

    const totalPnL = activePositions.reduce((sum, pos) => sum + pos.pnl, 0);
    const totalValue = activePositions.reduce((sum, pos) => sum + (pos.currentPrice * pos.quantity), 0);
    const portfolioDrawdown = totalValue > 0 ? (totalPnL / totalValue) * 100 : 0;

    // Risk assessment using AI
    try {
      const marketData = Array.from(this.marketData.values()).flat();
      if (marketData.length > 0) {
        const formattedData: MarketData[] = marketData.slice(-100).map(tick => ({
          symbol: tick.symbol,
          timestamp: new Date(tick.timestamp).getTime(),
          open: tick.open,
          high: tick.high,
          low: tick.low,
          close: tick.close,
          volume: tick.volume
        }));

        const riskAssessment = await this.aiService.assessRisk(
          { concentration: this.calculateConcentration() },
          formattedData
        );

        if (riskAssessment.portfolioRisk > 10) {
          await this.alertService.sendRiskAlert({
            type: 'PORTFOLIO_RISK',
            level: riskAssessment.portfolioRisk,
            threshold: 10,
            recommendations: riskAssessment.recommendations,
            severity: 'HIGH',
            message: `High portfolio risk detected: ${riskAssessment.portfolioRisk.toFixed(2)}%`
          });
        }
      }
    } catch (error) {
      console.error('Error in risk monitoring:', error);
    }

    if (portfolioDrawdown < -15) {
      await this.alertService.sendRiskAlert({
        type: 'DRAWDOWN',
        level: Math.abs(portfolioDrawdown),
        threshold: 15,
        severity: 'HIGH',
        message: `Critical drawdown reached: ${portfolioDrawdown.toFixed(2)}%`
      });
      
      // Emergency position closure
      await this.emergencyStopAll('Critical drawdown reached');
    }
  }

  private calculateConcentration(): number {
    const positions = Array.from(this.positions.values());
    if (positions.length === 0) return 0;

    const totalValue = positions.reduce((sum, pos) => sum + Math.abs(pos.pnl), 0);
    const maxPosition = Math.max(...positions.map(pos => Math.abs(pos.pnl)));
    
    return totalValue > 0 ? (maxPosition / totalValue) * 100 : 0;
  }

  private async updateModelPerformance(): Promise<void> {
    // Update ML model performance based on recent trades
    const recentTrades = await this.dbService.getRecentTrades(100);
    
    if (recentTrades && recentTrades.length > 50) {
      try {
        for (const model of this.mlService.getActiveModels()) {
          await this.mlService.retrainModel(model.id, recentTrades);
        }
      } catch (error) {
        console.error('Error updating model performance:', error);
      }
    }
  }

  private async emergencyStopAll(reason: string): Promise<void> {
    console.log(`Emergency stop triggered: ${reason}`);
    
    for (const [id, position] of this.positions) {
      await this.closePosition(position, `Emergency stop: ${reason}`);
    }
    
    await this.alertService.sendSystemAlert({
      component: 'Trading Engine',
      status: 'ERROR',
      message: `All positions closed: ${reason}`
    });
  }

  private canTakePosition(strategy: TradingStrategy): boolean {
    if (!strategy || !strategy.isActive) return false;

    const activePositions = Array.from(this.positions.values())
      .filter(p => p.strategy === strategy.id);
    
    return activePositions.length < strategy.maxPositions;
  }

  // Public getters
  getActivePositions(): Position[] {
    return Array.from(this.positions.values());
  }

  getPendingSignals(): TradingSignal[] {
    return [...this.signals];
  }

  getStrategies(): TradingStrategy[] {
    return Array.from(this.strategies.values());
  }

  getEngineStatus() {
    const positions = Array.from(this.positions.values());
    const totalPnL = positions.reduce((sum, pos) => sum + pos.pnl, 0);
    const totalUnrealizedPnL = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0);

    return {
      isRunning: this.isRunning,
      activePositions: this.positions.size,
      pendingSignals: this.signals.length,
      totalPnL,
      totalUnrealizedPnL,
      dataPoints: Array.from(this.marketData.values()).reduce((sum, data) => sum + data.length, 0),
      aiEnabled: true,
      mlEnabled: true
    };
  }
}
