
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ServiceManager } from "../../../services/ServiceManager";
import { BrokerAPIService } from "../../../services/BrokerAPIService";
import { BrokerConfiguration } from "../broker/BrokerConfiguration";
import { LiveMarketData } from "../broker/LiveMarketData";
import {
  Server,
  Database,
  Wifi,
  Activity,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  TrendingUp
} from "lucide-react";

export const SystemDashboard = () => {
  const [serviceManager] = useState(() => ServiceManager.getInstance());
  const [systemStatus, setSystemStatus] = useState(serviceManager.getSystemStatus());
  const [brokerService, setBrokerService] = useState<BrokerAPIService | null>(null);

  useEffect(() => {
    const updateStatus = () => {
      setSystemStatus(serviceManager.getSystemStatus());
    };

    // Update status every 5 seconds
    const interval = setInterval(updateStatus, 5000);

    // Subscribe to broker connection changes
    const unsubscribe = serviceManager.onBrokerConnectionChange((connected) => {
      setBrokerService(connected ? serviceManager.getBrokerService() : null);
      updateStatus();
    });

    return () => {
      clearInterval(interval);
      unsubscribe();
    };
  }, [serviceManager]);

  const handleBrokerConnectionChange = (connected: boolean, service?: BrokerAPIService) => {
    setBrokerService(connected && service ? service : null);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ONLINE':
      case 'connected':
      case 'running':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'connecting':
      case 'starting':
        return <RefreshCw className="h-4 w-4 text-yellow-400 animate-spin" />;
      case 'ERROR':
      case 'error':
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return <Server className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ONLINE':
      case 'connected':
      case 'running':
        return 'text-green-400 border-green-400';
      case 'connecting':
      case 'starting':
        return 'text-yellow-400 border-yellow-400';
      case 'ERROR':
      case 'error':
      case 'failed':
        return 'text-red-400 border-red-400';
      default:
        return 'text-gray-400 border-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-trading-light">System Dashboard</h1>
        <Badge variant="outline" className={getStatusColor(systemStatus.isInitialized ? 'ONLINE' : 'OFFLINE')}>
          {systemStatus.isInitialized ? 'SYSTEM ONLINE' : 'SYSTEM OFFLINE'}
        </Badge>
      </div>

      <Tabs defaultValue="broker" className="space-y-6">
        <TabsList className="bg-trading-darker border-trading-border">
          <TabsTrigger value="broker" className="data-[state=active]:bg-trading-accent">
            Broker & Data
          </TabsTrigger>
          <TabsTrigger value="services" className="data-[state=active]:bg-trading-accent">
            Core Services
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="broker" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <BrokerConfiguration onConnectionChange={handleBrokerConnectionChange} />
            <LiveMarketData brokerService={brokerService} />
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Database Service */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light flex items-center">
                  <Database className="h-4 w-4 mr-2" />
                  Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="outline" className="text-xs text-green-400 border-green-400">
                    CONNECTED
                  </Badge>
                  <div className="text-xs text-trading-muted">
                    <div>Memory: 89% used</div>
                    <div>Queries: 1,247/sec</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Broker Service */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light flex items-center">
                  <Wifi className="h-4 w-4 mr-2" />
                  Broker API
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="outline" className={`text-xs ${getStatusColor(systemStatus.broker)}`}>
                    {systemStatus.broker.toUpperCase()}
                  </Badge>
                  <div className="text-xs text-trading-muted">
                    <div>Type: Mock</div>
                    <div>Latency: {brokerService ? '12ms' : 'N/A'}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* ETL Service */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  ETL Pipeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="outline" className={`text-xs ${getStatusColor(systemStatus.etl?.isRunning ? 'running' : 'stopped')}`}>
                    {systemStatus.etl?.isRunning ? 'RUNNING' : 'STOPPED'}
                  </Badge>
                  <div className="text-xs text-trading-muted">
                    <div>Processed: {systemStatus.etl?.ticksProcessed || 0}</div>
                    <div>Rate: {systemStatus.etl?.processingRate || 0}/sec</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Trading Engine */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Trading Engine
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge variant="outline" className={`text-xs ${getStatusColor(systemStatus.engine?.isRunning ? 'running' : 'stopped')}`}>
                    {systemStatus.engine?.isRunning ? 'ACTIVE' : 'INACTIVE'}
                  </Badge>
                  <div className="text-xs text-trading-muted">
                    <div>Positions: {systemStatus.engine?.activePositions || 0}</div>
                    <div>Signals: {systemStatus.engine?.pendingSignals || 0}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Service Actions */}
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Service Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log('Restarting services...');
                    // Implement restart logic
                  }}
                >
                  Restart All Services
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    const status = serviceManager.getSystemStatus();
                    console.log('System Status:', status);
                  }}
                >
                  Export System Logs
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light">Memory Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-trading-light">423 MB</div>
                <div className="text-xs text-trading-muted">of 1.2 GB allocated</div>
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light">CPU Usage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-trading-light">34.2%</div>
                <div className="text-xs text-trading-muted">Average over 5 minutes</div>
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-trading-light">Network I/O</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-trading-light">2.4 MB/s</div>
                <div className="text-xs text-trading-muted">Incoming data rate</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
