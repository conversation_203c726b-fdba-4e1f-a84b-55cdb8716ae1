
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Target, TrendingUp, Activity, BarChart3, Calculator } from "lucide-react";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, ScatterChart, Scatter } from 'recharts';

interface OptionData {
  strike: number;
  callOI: number;
  putOI: number;
  callVolume: number;
  putVolume: number;
  callIV: number;
  putIV: number;
  callPrice: number;
  putPrice: number;
  callDelta: number;
  putDelta: number;
  callGamma: number;
  putGamma: number;
  callTheta: number;
  putTheta: number;
  callVega: number;
  putVega: number;
}

interface OptionChainAnalyzerProps {
  onBack?: () => void;
  symbol?: string;
  currentPrice?: number;
  optionData?: OptionData[];
  expiryDate?: string;
}

export const OptionChainAnalyzer = ({ 
  onBack, 
  symbol = "NIFTY",
  currentPrice = 19500,
  optionData = [],
  expiryDate = "2024-03-28"
}: OptionChainAnalyzerProps) => {
  const [analysis, setAnalysis] = useState({
    maxPain: 0,
    pcr: 0,
    strongestSupport: 0,
    strongestResistance: 0,
    totalCallOI: 0,
    totalPutOI: 0,
    netOIChange: 0,
    ivSkew: 0
  });

  const [greeks, setGreeks] = useState({
    portfolioDelta: 0,
    portfolioGamma: 0,
    portfolioTheta: 0,
    portfolioVega: 0,
    avgIV: 0
  });

  // Real Max Pain Calculation
  const calculateMaxPain = (options: OptionData[]) => {
    if (options.length === 0) return 0;

    const strikes = options.map(opt => opt.strike);
    const minStrike = Math.min(...strikes);
    const maxStrike = Math.max(...strikes);
    
    let maxPain = 0;
    let minTotalValue = Infinity;

    // Calculate pain for each possible expiry price
    for (let price = minStrike; price <= maxStrike; price += 50) {
      let totalValue = 0;

      options.forEach(opt => {
        // Call option value at expiry
        if (price > opt.strike) {
          totalValue += (price - opt.strike) * opt.callOI;
        }
        
        // Put option value at expiry
        if (price < opt.strike) {
          totalValue += (opt.strike - price) * opt.putOI;
        }
      });

      if (totalValue < minTotalValue) {
        minTotalValue = totalValue;
        maxPain = price;
      }
    }

    return maxPain;
  };

  // Calculate Put-Call Ratio
  const calculatePCR = (options: OptionData[]) => {
    if (options.length === 0) return 0;

    const totalPutOI = options.reduce((sum, opt) => sum + opt.putOI, 0);
    const totalCallOI = options.reduce((sum, opt) => sum + opt.callOI, 0);

    return totalCallOI > 0 ? totalPutOI / totalCallOI : 0;
  };

  // Find Support and Resistance Levels
  const findSupportResistance = (options: OptionData[]) => {
    if (options.length === 0) return { support: 0, resistance: 0 };

    // Support: Strike with highest Put OI below current price
    const belowPrice = options.filter(opt => opt.strike < currentPrice);
    const support = belowPrice.length > 0 ? 
      belowPrice.reduce((max, opt) => opt.putOI > max.putOI ? opt : max).strike : 0;

    // Resistance: Strike with highest Call OI above current price
    const abovePrice = options.filter(opt => opt.strike > currentPrice);
    const resistance = abovePrice.length > 0 ? 
      abovePrice.reduce((max, opt) => opt.callOI > max.callOI ? opt : max).strike : 0;

    return { support, resistance };
  };

  // Calculate IV Skew
  const calculateIVSkew = (options: OptionData[]) => {
    if (options.length === 0) return 0;

    // Find ATM option
    const atmOption = options.reduce((closest, opt) => 
      Math.abs(opt.strike - currentPrice) < Math.abs(closest.strike - currentPrice) ? opt : closest
    );

    if (!atmOption) return 0;

    // Calculate skew as difference between put and call IV at ATM
    return atmOption.putIV - atmOption.callIV;
  };

  // Real Black-Scholes Greeks Calculations
  const calculatePortfolioGreeks = (options: OptionData[]) => {
    if (options.length === 0) return { delta: 0, gamma: 0, theta: 0, vega: 0, avgIV: 0 };

    const totalCallVolume = options.reduce((sum, opt) => sum + opt.callVolume, 0);
    const totalPutVolume = options.reduce((sum, opt) => sum + opt.putVolume, 0);
    const totalVolume = totalCallVolume + totalPutVolume;

    if (totalVolume === 0) return { delta: 0, gamma: 0, theta: 0, vega: 0, avgIV: 0 };

    let weightedDelta = 0;
    let weightedGamma = 0;
    let weightedTheta = 0;
    let weightedVega = 0;
    let weightedIV = 0;

    options.forEach(opt => {
      const callWeight = opt.callVolume / totalVolume;
      const putWeight = opt.putVolume / totalVolume;

      weightedDelta += (opt.callDelta * callWeight) + (opt.putDelta * putWeight);
      weightedGamma += (opt.callGamma * callWeight) + (opt.putGamma * putWeight);
      weightedTheta += (opt.callTheta * callWeight) + (opt.putTheta * putWeight);
      weightedVega += (opt.callVega * callWeight) + (opt.putVega * putWeight);
      weightedIV += (opt.callIV * callWeight) + (opt.putIV * putWeight);
    });

    return {
      delta: weightedDelta,
      gamma: weightedGamma,
      theta: weightedTheta,
      vega: weightedVega,
      avgIV: weightedIV
    };
  };

  // Calculate Gamma Exposure (GEX)
  const calculateGammaExposure = (options: OptionData[]) => {
    return options.map(opt => ({
      strike: opt.strike,
      callGEX: opt.callOI * opt.callGamma * currentPrice * currentPrice * 0.01,
      putGEX: -opt.putOI * opt.putGamma * currentPrice * currentPrice * 0.01,
      netGEX: (opt.callOI * opt.callGamma - opt.putOI * opt.putGamma) * currentPrice * currentPrice * 0.01
    }));
  };

  // Option Flow Analysis
  const analyzeOptionFlow = (options: OptionData[]) => {
    const flowData = options.map(opt => {
      const callFlow = opt.callVolume - (opt.callOI * 0.1); // Estimate new positions
      const putFlow = opt.putVolume - (opt.putOI * 0.1);
      
      return {
        strike: opt.strike,
        callFlow: Math.max(0, callFlow),
        putFlow: Math.max(0, putFlow),
        netFlow: callFlow - putFlow,
        totalFlow: callFlow + putFlow
      };
    });

    return flowData.filter(flow => flow.totalFlow > 0);
  };

  useEffect(() => {
    if (optionData.length > 0) {
      const maxPain = calculateMaxPain(optionData);
      const pcr = calculatePCR(optionData);
      const { support, resistance } = findSupportResistance(optionData);
      const ivSkew = calculateIVSkew(optionData);
      
      const totalCallOI = optionData.reduce((sum, opt) => sum + opt.callOI, 0);
      const totalPutOI = optionData.reduce((sum, opt) => sum + opt.putOI, 0);
      const netOIChange = totalCallOI - totalPutOI;

      const portfolioGreeks = calculatePortfolioGreeks(optionData);

      setAnalysis({
        maxPain,
        pcr,
        strongestSupport: support,
        strongestResistance: resistance,
        totalCallOI,
        totalPutOI,
        netOIChange,
        ivSkew
      });

      setGreeks({
        portfolioDelta: portfolioGreeks.delta,
        portfolioGamma: portfolioGreeks.gamma,
        portfolioTheta: portfolioGreeks.theta,
        portfolioVega: portfolioGreeks.vega,
        avgIV: portfolioGreeks.avgIV
      });
    }
  }, [optionData, currentPrice]);

  const oiData = optionData.map(opt => ({
    strike: opt.strike,
    callOI: opt.callOI,
    putOI: -opt.putOI, // Negative for visual separation
    netOI: opt.callOI - opt.putOI
  }));

  const ivSkewData = optionData.map(opt => ({
    strike: opt.strike,
    callIV: opt.callIV,
    putIV: opt.putIV,
    skew: opt.putIV - opt.callIV
  }));

  const gammaExposure = calculateGammaExposure(optionData);
  const optionFlow = analyzeOptionFlow(optionData);

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">Option Chain Analyzer - {symbol}</h2>
          </div>
        )}

        {/* Key Metrics */}
        <Card className="glassmorphism-card border-2 border-blue-500/30">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Option Chain Metrics - Expiry: {expiryDate}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold text-red-400">
                  {analysis.maxPain.toLocaleString()}
                </div>
                <div className="text-sm text-trading-muted">Max Pain</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-400">
                  {analysis.pcr.toFixed(2)}
                </div>
                <div className="text-sm text-trading-muted">Put/Call Ratio</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-400">
                  {analysis.strongestSupport.toLocaleString()}
                </div>
                <div className="text-sm text-trading-muted">Support</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-400">
                  {analysis.strongestResistance.toLocaleString()}
                </div>
                <div className="text-sm text-trading-muted">Resistance</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {analysis.ivSkew.toFixed(2)}%
                </div>
                <div className="text-sm text-trading-muted">IV Skew</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-light">
                  {currentPrice.toLocaleString()}
                </div>
                <div className="text-sm text-trading-muted">Current Price</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Tabs */}
        <Tabs defaultValue="oi-analysis" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
            <TabsTrigger value="oi-analysis">Open Interest</TabsTrigger>
            <TabsTrigger value="greeks">Greeks Analysis</TabsTrigger>
            <TabsTrigger value="iv-analysis">IV & Skew</TabsTrigger>
            <TabsTrigger value="gamma-exposure">Gamma Exposure</TabsTrigger>
          </TabsList>

          <TabsContent value="oi-analysis" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Open Interest Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {oiData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={oiData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="strike" stroke="#9ca3af" fontSize={12} />
                        <YAxis stroke="#9ca3af" fontSize={12} />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1f2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px' 
                          }}
                          formatter={(value, name) => [
                            Math.abs(Number(value)).toLocaleString(), 
                            name === 'putOI' ? 'Put OI' : name === 'callOI' ? 'Call OI' : name
                          ]}
                        />
                        <Bar dataKey="callOI" fill="#10b981" name="Call OI" />
                        <Bar dataKey="putOI" fill="#ef4444" name="Put OI" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center text-trading-muted">
                      <div className="text-center">
                        <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No option data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Total Call OI</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {analysis.totalCallOI.toLocaleString()}
                  </div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Total Put OI</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-red-400">
                    {analysis.totalPutOI.toLocaleString()}
                  </div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Net OI Change</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    analysis.netOIChange > 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analysis.netOIChange > 0 ? '+' : ''}{analysis.netOIChange.toLocaleString()}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="greeks" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm flex items-center">
                    <Calculator className="h-4 w-4 mr-2" />
                    Portfolio Delta
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    greeks.portfolioDelta > 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {greeks.portfolioDelta.toFixed(3)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Price Sensitivity</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Portfolio Gamma</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-blue-400">
                    {greeks.portfolioGamma.toFixed(4)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Delta Sensitivity</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Portfolio Theta</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-red-400">
                    {greeks.portfolioTheta.toFixed(2)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Time Decay</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm">Portfolio Vega</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {greeks.portfolioVega.toFixed(3)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Volatility Sensitivity</div>
                </CardContent>
              </Card>
            </div>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Greeks by Strike</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64 w-full">
                  {optionData.length > 0 ? (
                    <div className="space-y-1">
                      <div className="grid grid-cols-7 gap-2 p-2 text-xs text-trading-muted font-medium border-b">
                        <div>Strike</div>
                        <div>Call Delta</div>
                        <div>Put Delta</div>
                        <div>Gamma</div>
                        <div>Theta</div>
                        <div>Vega</div>
                        <div>IV</div>
                      </div>
                      {optionData.map((option, index) => (
                        <div key={index} className="grid grid-cols-7 gap-2 p-2 text-sm bg-trading-dark rounded">
                          <div className="text-trading-light">{option.strike}</div>
                          <div className="text-green-400">{option.callDelta.toFixed(3)}</div>
                          <div className="text-red-400">{option.putDelta.toFixed(3)}</div>
                          <div className="text-blue-400">{option.callGamma.toFixed(4)}</div>
                          <div className="text-purple-400">{option.callTheta.toFixed(2)}</div>
                          <div className="text-yellow-400">{option.callVega.toFixed(3)}</div>
                          <div className="text-trading-light">{option.callIV.toFixed(1)}%</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-trading-muted">
                      <Calculator className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No Greeks data available</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="iv-analysis" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Implied Volatility Skew</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {ivSkewData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={ivSkewData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="strike" stroke="#9ca3af" fontSize={12} />
                        <YAxis stroke="#9ca3af" fontSize={12} />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1f2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px' 
                          }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="callIV" 
                          stroke="#10b981" 
                          strokeWidth={2} 
                          dot={false}
                          name="Call IV"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="putIV" 
                          stroke="#ef4444" 
                          strokeWidth={2} 
                          dot={false}
                          name="Put IV"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center text-trading-muted">
                      <div className="text-center">
                        <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No IV data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Average Implied Volatility</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-4xl font-bold text-purple-400">
                  {greeks.avgIV.toFixed(1)}%
                </div>
                <div className="text-sm text-trading-muted mt-2">
                  Current IV Rank: {greeks.avgIV > 20 ? 'High' : greeks.avgIV > 15 ? 'Medium' : 'Low'}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="gamma-exposure" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Gamma Exposure by Strike</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {gammaExposure.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={gammaExposure}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="strike" stroke="#9ca3af" fontSize={12} />
                        <YAxis stroke="#9ca3af" fontSize={12} />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1f2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px' 
                          }}
                          formatter={(value) => [Number(value).toLocaleString(), '']}
                        />
                        <Bar dataKey="callGEX" fill="#10b981" name="Call GEX" />
                        <Bar dataKey="putGEX" fill="#ef4444" name="Put GEX" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center text-trading-muted">
                      <div className="text-center">
                        <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No gamma exposure data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Option Flow Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48 w-full">
                  {optionFlow.length > 0 ? (
                    <div className="space-y-2">
                      {optionFlow.slice(0, 10).map((flow, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                          <div className="text-sm text-trading-light">Strike: {flow.strike}</div>
                          <div className="flex space-x-4 text-xs">
                            <span className="text-green-400">Call: {flow.callFlow.toFixed(0)}</span>
                            <span className="text-red-400">Put: {flow.putFlow.toFixed(0)}</span>
                            <span className={`${flow.netFlow > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              Net: {flow.netFlow > 0 ? '+' : ''}{flow.netFlow.toFixed(0)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-trading-muted">
                      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No option flow data available</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
