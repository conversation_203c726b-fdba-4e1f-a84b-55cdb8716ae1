
export interface PriceData {
  symbol: string;
  price: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
  timestamp: number;
}

export interface MarketData {
  timestamp: number;
  indices: IndexData[];
  marketStatus: 'open' | 'closed' | 'pre-market' | 'after-hours';
  breadth: MarketBreadth;
}

export interface IndexData {
  symbol: string;
  value: number;
  change: number;
  volume: number;
  volatility: number;
}

export interface MarketBreadth {
  advancing: number;
  declining: number;
  unchanged: number;
  totalVolume: number;
  advanceDeclineRatio: number;
}

export interface TechnicalAnalysis {
  timeframe: string;
  indicators: {
    rsi: number;
    macd: {
      macd: number;
      signal: number;
      histogram: number;
    };
    ema20: number;
    sma50: number;
    bollinger: {
      upper: number;
      middle: number;
      lower: number;
    };
  };
  support: number;
  resistance: number;
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  signals: string[];
  timestamp: number;
}

export interface Portfolio {
  value: number;
  pnl: number;
  pnlPercent: number;
  holdings: any[];
  dayChange: number;
  cash: number;
  timestamp: number;
}

export interface OptionChain {
  symbol: string;
  expiry: string;
  strikes: Array<{
    strike: number;
    call: {
      ltp: number;
      iv: number;
      oi: number;
      volume: number;
    };
    put: {
      ltp: number;
      iv: number;
      oi: number;
      volume: number;
    };
  }>;
  timestamp: number;
}

export interface AIAnalysis {
  symbol: string;
  prediction: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS';
    confidence: number;
    targetPrice: number;
    timeframe: string;
  };
  signals: string[];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  timestamp: number;
}

export interface RiskMetrics {
  portfolioRisk: {
    var95: number;
    expectedShortfall: number;
    beta: number;
    correlation: number;
  };
  positionRisk: {
    maxPositionSize: number;
    riskPerTrade: number;
    stopLossLevel: number;
  };
  timestamp: number;
}
