
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Target, Shield, Calendar, DollarSign } from "lucide-react";
import { PaperPosition } from '@/services/PaperTradingService';
import { paperTradingService } from '@/services/PaperTradingService';

interface PaperPositionsViewProps {
  positions: PaperPosition[];
}

export const PaperPositionsView: React.FC<PaperPositionsViewProps> = ({ positions }) => {
  const handleClosePosition = (positionId: string) => {
    paperTradingService.closePosition(positionId, 'Manual close');
  };

  if (positions.length === 0) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="text-center py-12">
          <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium text-trading-light mb-2">No Positions</h3>
          <p className="text-trading-muted">Start trading to see your positions here</p>
        </CardContent>
      </Card>
    );
  }

  const equityPositions = positions.filter(p => p.instrumentType === 'EQUITY');
  const optionPositions = positions.filter(p => p.instrumentType === 'OPTION');

  return (
    <div className="space-y-6">
      {/* Equity Positions */}
      {equityPositions.length > 0 && (
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
              Equity Positions ({equityPositions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {equityPositions.map((position) => (
                <div key={position.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                    {/* Symbol & Strategy */}
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-trading-light text-lg">
                        {position.symbol}
                      </span>
                      <Badge variant="outline" className={position.strategy === 'INTRADAY' ? 'text-yellow-400 border-yellow-400' : 'text-green-400 border-green-400'}>
                        {position.strategy}
                      </Badge>
                    </div>

                    {/* Quantity & Avg Price */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Quantity</div>
                      <div className="text-trading-light font-medium">{position.quantity}</div>
                      <div className="text-xs text-trading-muted">Avg: ₹{position.avgPrice.toFixed(2)}</div>
                    </div>

                    {/* Current Price */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Current Price</div>
                      <div className="text-trading-light font-medium">₹{position.currentPrice.toFixed(2)}</div>
                      <div className={`text-xs flex items-center justify-center ${position.dayChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {position.dayChangePercent >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                        {position.dayChangePercent.toFixed(2)}%
                      </div>
                    </div>

                    {/* P&L */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">P&L</div>
                      <div className={`font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ₹{position.pnl.toFixed(2)}
                      </div>
                      <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ({position.pnlPercent > 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%)
                      </div>
                    </div>

                    {/* Market Value */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Market Value</div>
                      <div className="text-trading-light font-medium">
                        ₹{position.marketValue.toFixed(2)}
                      </div>
                      <div className="text-xs text-trading-muted">
                        {position.entryDate.toLocaleDateString()}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col space-y-2">
                      <Button 
                        size="sm" 
                        variant="destructive" 
                        className="text-xs"
                        onClick={() => handleClosePosition(position.id)}
                      >
                        Close Position
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Options Positions */}
      {optionPositions.length > 0 && (
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Target className="h-5 w-5 mr-2 text-purple-400" />
              Options Positions ({optionPositions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {optionPositions.map((position) => (
                <div key={position.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 items-center">
                    {/* Symbol & Option Details */}
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-trading-light">{position.symbol}</span>
                        <Badge variant="outline" className={position.optionDetails?.optionType === 'CALL' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                          {position.optionDetails?.optionType}
                        </Badge>
                      </div>
                      <div className="text-xs text-trading-muted">
                        Strike: ₹{position.optionDetails?.strike} • {position.strategy}
                      </div>
                      <div className="text-xs text-trading-muted flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        Exp: {position.optionDetails?.expiry.toLocaleDateString()}
                      </div>
                    </div>

                    {/* Quantity & Premium */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Lots</div>
                      <div className="text-trading-light font-medium">{position.quantity}</div>
                      <div className="text-xs text-trading-muted">Premium: ₹{position.avgPrice.toFixed(2)}</div>
                    </div>

                    {/* Current Premium */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Current Premium</div>
                      <div className="text-trading-light font-medium">₹{position.currentPrice.toFixed(2)}</div>
                    </div>

                    {/* Greeks */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Greeks</div>
                      <div className="text-xs text-trading-light">
                        Δ: {position.optionDetails?.greeks.delta.toFixed(3)}
                      </div>
                      <div className="text-xs text-trading-muted">
                        Θ: {position.optionDetails?.greeks.theta.toFixed(3)}
                      </div>
                    </div>

                    {/* P&L */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">P&L</div>
                      <div className={`font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ₹{position.pnl.toFixed(2)}
                      </div>
                      <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ({position.pnlPercent > 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%)
                      </div>
                    </div>

                    {/* Market Value */}
                    <div className="text-center">
                      <div className="text-sm text-trading-muted">Market Value</div>
                      <div className="text-trading-light font-medium">
                        ₹{position.marketValue.toFixed(2)}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col space-y-2">
                      <Button 
                        size="sm" 
                        variant="destructive" 
                        className="text-xs"
                        onClick={() => handleClosePosition(position.id)}
                      >
                        Close Position
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
