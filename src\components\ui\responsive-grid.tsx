
import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
    '3xl'?: number;
  };
  gap?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
    '3xl'?: number;
  };
}

export function ResponsiveGrid({ 
  children, 
  className,
  cols = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5, '2xl': 6, '3xl': 7 },
  gap = { xs: 2, sm: 3, md: 4, lg: 5, xl: 6, '2xl': 7, '3xl': 8 }
}: ResponsiveGridProps) {
  const gridCols = [
    cols.xs && `grid-cols-${cols.xs}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
    cols['2xl'] && `2xl:grid-cols-${cols['2xl']}`,
    cols['3xl'] && `3xl:grid-cols-${cols['3xl']}`,
  ].filter(Boolean).join(' ');

  const gridGap = [
    gap.xs && `gap-${gap.xs}`,
    gap.sm && `sm:gap-${gap.sm}`,
    gap.md && `md:gap-${gap.md}`,
    gap.lg && `lg:gap-${gap.lg}`,
    gap.xl && `xl:gap-${gap.xl}`,
    gap['2xl'] && `2xl:gap-${gap['2xl']}`,
    gap['3xl'] && `3xl:gap-${gap['3xl']}`,
  ].filter(Boolean).join(' ');

  return (
    <div className={cn(
      'grid',
      gridCols,
      gridGap,
      className
    )}>
      {children}
    </div>
  );
}

// Trading-specific responsive layouts
export function TradingDashboardGrid({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <ResponsiveGrid
      cols={{ xs: 1, lg: 2, xl: 3, '2xl': 4 }}
      gap={{ xs: 3, md: 4, lg: 5, xl: 6 }}
      className={cn('auto-rows-min', className)}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function ChartGrid({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <ResponsiveGrid
      cols={{ xs: 1, xl: 2 }}
      gap={{ xs: 4, lg: 6 }}
      className={cn('min-h-0', className)}
    >
      {children}
    </ResponsiveGrid>
  );
}

export function WidgetGrid({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <ResponsiveGrid
      cols={{ xs: 1, sm: 2, lg: 3, xl: 4, '2xl': 5 }}
      gap={{ xs: 2, sm: 3, lg: 4 }}
      className={className}
    >
      {children}
    </ResponsiveGrid>
  );
}
