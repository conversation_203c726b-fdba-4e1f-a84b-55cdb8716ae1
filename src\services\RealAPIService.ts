
import { RealDataIntegrationService, RealMarketDataConfig } from './RealDataIntegrationService';

export interface APIEndpoint {
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers: Record<string, string>;
  rateLimit: number;
  retryCount: number;
}

export interface MarketDataProvider {
  name: string;
  baseUrl: string;
  apiKey: string;
  endpoints: Record<string, APIEndpoint>;
  isActive: boolean;
}

export interface BrokerConnection {
  name: string;
  baseUrl: string;
  credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken?: string;
  };
  endpoints: Record<string, APIEndpoint>;
  isConnected: boolean;
}

export class RealAPIService {
  private marketDataProviders: Map<string, MarketDataProvider> = new Map();
  private brokerConnections: Map<string, BrokerConnection> = new Map();
  private realDataService: RealDataIntegrationService;
  private requestQueue: Array<{ provider: string; endpoint: string; params: any; resolve: Function; reject: Function }> = [];
  private isProcessingQueue = false;

  constructor(config?: RealMarketDataConfig) {
    this.realDataService = new RealDataIntegrationService(config || { provider: 'mock' });
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Alpha Vantage Provider
    this.marketDataProviders.set('alphavantage', {
      name: 'Alpha Vantage',
      baseUrl: 'https://www.alphavantage.co/query',
      apiKey: '',
      endpoints: {
        dailyData: {
          name: 'TIME_SERIES_DAILY',
          url: '/query?function=TIME_SERIES_DAILY',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 5, // 5 requests per minute
          retryCount: 3
        },
        realtimeQuote: {
          name: 'GLOBAL_QUOTE',
          url: '/query?function=GLOBAL_QUOTE',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 5,
          retryCount: 3
        }
      },
      isActive: false
    });

    // Polygon.io Provider
    this.marketDataProviders.set('polygon', {
      name: 'Polygon.io',
      baseUrl: 'https://api.polygon.io',
      apiKey: '',
      endpoints: {
        dailyBars: {
          name: 'Daily Bars',
          url: '/v2/aggs/ticker/{symbol}/range/1/day/{from}/{to}',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 50, // 50 requests per minute
          retryCount: 3
        },
        realtimeTrade: {
          name: 'Real-time Trade',
          url: '/v3/trades/{symbol}',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 50,
          retryCount: 3
        }
      },
      isActive: false
    });

    // Finnhub Provider
    this.marketDataProviders.set('finnhub', {
      name: 'Finnhub',
      baseUrl: 'https://finnhub.io/api/v1',
      apiKey: '',
      endpoints: {
        candles: {
          name: 'Stock Candles',
          url: '/stock/candle',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 60, // 60 requests per minute
          retryCount: 3
        },
        quote: {
          name: 'Quote',
          url: '/quote',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 60,
          retryCount: 3
        }
      },
      isActive: false
    });

    // Initialize broker connections
    this.initializeBrokerConnections();
  }

  private initializeBrokerConnections(): void {
    // Zerodha Kite API
    this.brokerConnections.set('zerodha', {
      name: 'Zerodha Kite',
      baseUrl: 'https://api.kite.trade',
      credentials: {
        apiKey: '',
        apiSecret: '',
        accessToken: ''
      },
      endpoints: {
        portfolio: {
          name: 'Portfolio',
          url: '/portfolio/holdings',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 180, // 3 requests per second
          retryCount: 3
        },
        placeOrder: {
          name: 'Place Order',
          url: '/orders/regular',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 180,
          retryCount: 3
        },
        positions: {
          name: 'Positions',
          url: '/portfolio/positions',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 180,
          retryCount: 3
        }
      },
      isConnected: false
    });

    // Interactive Brokers
    this.brokerConnections.set('interactivebrokers', {
      name: 'Interactive Brokers',
      baseUrl: 'https://localhost:5000/v1/api',
      credentials: {
        apiKey: '',
        apiSecret: ''
      },
      endpoints: {
        accounts: {
          name: 'Accounts',
          url: '/portfolio/accounts',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 100,
          retryCount: 3
        },
        positions: {
          name: 'Positions',
          url: '/portfolio/{accountId}/positions',
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          rateLimit: 100,
          retryCount: 3
        }
      },
      isConnected: false
    });
  }

  // Configure market data provider
  async configureMarketDataProvider(providerName: string, apiKey: string): Promise<boolean> {
    const provider = this.marketDataProviders.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }

    provider.apiKey = apiKey;
    
    // Test the connection
    try {
      const testResult = await this.testProviderConnection(providerName);
      provider.isActive = testResult;
      return testResult;
    } catch (error) {
      console.error(`Failed to configure provider ${providerName}:`, error);
      provider.isActive = false;
      return false;
    }
  }

  // Configure broker connection
  async configureBrokerConnection(brokerName: string, credentials: any): Promise<boolean> {
    const broker = this.brokerConnections.get(brokerName);
    if (!broker) {
      throw new Error(`Broker ${brokerName} not found`);
    }

    broker.credentials = { ...broker.credentials, ...credentials };
    
    // Test the connection
    try {
      const testResult = await this.testBrokerConnection(brokerName);
      broker.isConnected = testResult;
      return testResult;
    } catch (error) {
      console.error(`Failed to configure broker ${brokerName}:`, error);
      broker.isConnected = false;
      return false;
    }
  }

  // Test provider connection
  private async testProviderConnection(providerName: string): Promise<boolean> {
    const provider = this.marketDataProviders.get(providerName);
    if (!provider || !provider.apiKey) return false;

    try {
      switch (providerName) {
        case 'alphavantage':
          const avResponse = await fetch(`${provider.baseUrl}?function=GLOBAL_QUOTE&symbol=AAPL&apikey=${provider.apiKey}`);
          const avData = await avResponse.json();
          return !avData['Error Message'] && !avData['Note'];
          
        case 'polygon':
          const polygonResponse = await fetch(`${provider.baseUrl}/v2/aggs/ticker/AAPL/prev?apikey=${provider.apiKey}`);
          return polygonResponse.ok;
          
        case 'finnhub':
          const finnhubResponse = await fetch(`${provider.baseUrl}/quote?symbol=AAPL&token=${provider.apiKey}`);
          return finnhubResponse.ok;
          
        default:
          return false;
      }
    } catch (error) {
      console.error(`Connection test failed for ${providerName}:`, error);
      return false;
    }
  }

  // Test broker connection
  private async testBrokerConnection(brokerName: string): Promise<boolean> {
    const broker = this.brokerConnections.get(brokerName);
    if (!broker) return false;

    try {
      switch (brokerName) {
        case 'zerodha':
          // Test Zerodha connection by checking session
          const zerodhaResponse = await fetch(`${broker.baseUrl}/user/profile`, {
            headers: {
              'Authorization': `token ${broker.credentials.apiKey}:${broker.credentials.accessToken}`,
              ...broker.endpoints.portfolio.headers
            }
          });
          return zerodhaResponse.ok;
          
        case 'interactivebrokers':
          // Test IB Gateway connection
          const ibResponse = await fetch(`${broker.baseUrl}/iserver/auth/status`);
          return ibResponse.ok;
          
        default:
          return false;
      }
    } catch (error) {
      console.error(`Broker connection test failed for ${brokerName}:`, error);
      return false;
    }
  }

  // Fetch real market data
  async fetchRealMarketData(symbol: string, provider?: string): Promise<any> {
    const activeProvider = provider || this.getActiveProvider();
    if (!activeProvider) {
      throw new Error('No active market data provider configured');
    }

    return this.queueRequest(activeProvider, 'marketData', { symbol });
  }

  // Fetch real portfolio data
  async fetchRealPortfolioData(broker?: string): Promise<any> {
    const activeBroker = broker || this.getActiveBroker();
    if (!activeBroker) {
      throw new Error('No active broker connection configured');
    }

    return this.queueRequest(activeBroker, 'portfolio', {});
  }

  // Queue API requests to handle rate limiting
  private async queueRequest(provider: string, endpoint: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ provider, endpoint, params, resolve, reject });
      if (!this.isProcessingQueue) {
        this.processRequestQueue();
      }
    });
  }

  // Process request queue with rate limiting
  private async processRequestQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) return;
    
    this.isProcessingQueue = true;
    
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!;
      
      try {
        const result = await this.executeAPIRequest(request.provider, request.endpoint, request.params);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
      
      // Rate limiting delay
      await this.sleep(1000); // 1 second between requests
    }
    
    this.isProcessingQueue = false;
  }

  // Execute actual API request
  private async executeAPIRequest(providerName: string, endpointName: string, params: any): Promise<any> {
    const provider = this.marketDataProviders.get(providerName) || this.brokerConnections.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }

    const endpoint = provider.endpoints[endpointName];
    if (!endpoint) {
      throw new Error(`Endpoint ${endpointName} not found for provider ${providerName}`);
    }

    let url = `${provider.baseUrl}${endpoint.url}`;
    
    // Replace URL parameters
    Object.keys(params).forEach(key => {
      url = url.replace(`{${key}}`, params[key]);
    });

    // Add API key as query parameter
    const urlObj = new URL(url);
    if ('apiKey' in provider && provider.apiKey) {
      urlObj.searchParams.append('apikey', provider.apiKey);
    }

    const response = await fetch(urlObj.toString(), {
      method: endpoint.method,
      headers: {
        ...endpoint.headers,
        ...(('credentials' in provider && provider.credentials.accessToken) ? 
          { 'Authorization': `Bearer ${provider.credentials.accessToken}` } : {})
      },
      ...(endpoint.method !== 'GET' && params.body ? { body: JSON.stringify(params.body) } : {})
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Get active provider
  private getActiveProvider(): string | null {
    for (const [name, provider] of this.marketDataProviders) {
      if (provider.isActive) return name;
    }
    return null;
  }

  // Get active broker
  private getActiveBroker(): string | null {
    for (const [name, broker] of this.brokerConnections) {
      if (broker.isConnected) return name;
    }
    return null;
  }

  // Utility function for delays
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get connection status
  getConnectionStatus(): { providers: any[]; brokers: any[] } {
    const providers = Array.from(this.marketDataProviders.entries()).map(([name, provider]) => ({
      name,
      isActive: provider.isActive,
      hasApiKey: !!provider.apiKey
    }));

    const brokers = Array.from(this.brokerConnections.entries()).map(([name, broker]) => ({
      name,
      isConnected: broker.isConnected,
      hasCredentials: !!(broker.credentials.apiKey && broker.credentials.apiSecret)
    }));

    return { providers, brokers };
  }

  // Remove all fake data methods
  async validateRealDataConnection(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];
    let isValid = true;

    // Check if any real providers are active
    const activeProviders = Array.from(this.marketDataProviders.values()).filter(p => p.isActive);
    if (activeProviders.length === 0) {
      errors.push('No real market data providers are active');
      isValid = false;
    }

    // Check if any brokers are connected
    const connectedBrokers = Array.from(this.brokerConnections.values()).filter(b => b.isConnected);
    if (connectedBrokers.length === 0) {
      errors.push('No real broker connections are active');
      isValid = false;
    }

    return { isValid, errors };
  }
}
