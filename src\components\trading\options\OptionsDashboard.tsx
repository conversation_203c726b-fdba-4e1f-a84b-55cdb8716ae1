
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, Target, Zap, Activity } from "lucide-react";

interface OptionChainData {
  strike: number;
  ce_ltp: number;
  pe_ltp: number;
  ce_iv: number;
  pe_iv: number;
  ce_oi: number;
  pe_oi: number;
}

interface OptionStrategy {
  name: string;
  setup: string;
  pnl: string;
  probability: number;
}

interface GreeksData {
  name: string;
  value: number;
  change: string;
  description: string;
}

interface OptionsMetrics {
  activePositions: number;
  profitablePositions: number;
  totalPnl: string;
  ivRank: number;
  successRate: number;
}

interface OptionsDashboardProps {
  optionChain?: OptionChainData[];
  strategies?: OptionStrategy[];
  greeks?: GreeksData[];
  metrics?: OptionsMetrics;
  isLoading?: boolean;
}

export const OptionsDashboard = ({
  optionChain = [],
  strategies = [],
  greeks = [],
  metrics,
  isLoading = false
}: OptionsDashboardProps) => {
  const renderEmptyState = (title: string, description: string, icon: React.ReactNode) => (
    <div className="text-center py-12">
      {icon}
      <div className="text-lg text-trading-muted">{isLoading ? 'Loading...' : title}</div>
      <div className="text-sm text-trading-muted mt-2">{isLoading ? 'Fetching options data...' : description}</div>
    </div>
  );

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Advanced Options Analytics Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Active Positions</div>
              <div className="text-2xl font-bold text-blue-400">
                {metrics ? metrics.activePositions : '--'}
              </div>
              <div className="text-xs text-green-400">
                {metrics ? `${metrics.profitablePositions} Profitable` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Total P&L</div>
              <div className="text-2xl font-bold text-green-400">
                {metrics ? metrics.totalPnl : '--'}
              </div>
              <div className="text-xs text-green-400">Today</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">IV Rank</div>
              <div className="text-2xl font-bold text-yellow-400">
                {metrics ? `${metrics.ivRank}%` : '--'}
              </div>
              <div className="text-xs text-trading-muted">
                {metrics && metrics.ivRank > 50 ? 'High volatility' : 'Low volatility'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Success Rate</div>
              <div className="text-2xl font-bold text-green-400">
                {metrics ? `${metrics.successRate}%` : '--'}
              </div>
              <div className="text-xs text-green-400">This month</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="chain" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="chain" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Option Chain
          </TabsTrigger>
          <TabsTrigger value="strategies" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Strategies
          </TabsTrigger>
          <TabsTrigger value="greeks" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Greeks
          </TabsTrigger>
          <TabsTrigger value="analysis" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chain">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Option Chain</CardTitle>
            </CardHeader>
            <CardContent>
              {optionChain.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-trading-border">
                        <th className="text-left p-2 text-trading-muted">CE OI</th>
                        <th className="text-left p-2 text-trading-muted">CE IV</th>
                        <th className="text-left p-2 text-trading-muted">CE LTP</th>
                        <th className="text-center p-2 text-trading-light font-bold">STRIKE</th>
                        <th className="text-right p-2 text-trading-muted">PE LTP</th>
                        <th className="text-right p-2 text-trading-muted">PE IV</th>
                        <th className="text-right p-2 text-trading-muted">PE OI</th>
                      </tr>
                    </thead>
                    <tbody>
                      {optionChain.map((row, index) => (
                        <tr key={index} className="border-b border-trading-border/50 hover:bg-trading-accent/10">
                          <td className="p-2 text-trading-light">{(row.ce_oi / 1000).toFixed(0)}K</td>
                          <td className="p-2 text-blue-400">{row.ce_iv}%</td>
                          <td className="p-2 text-green-400 font-medium">₹{row.ce_ltp}</td>
                          <td className="p-2 text-center text-trading-light font-bold bg-trading-dark">{row.strike}</td>
                          <td className="p-2 text-right text-red-400 font-medium">₹{row.pe_ltp}</td>
                          <td className="p-2 text-right text-blue-400">{row.pe_iv}%</td>
                          <td className="p-2 text-right text-trading-light">{(row.pe_oi / 1000).toFixed(0)}K</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                renderEmptyState(
                  'No Option Chain Data',
                  'Connect to options data feed to view live option chain',
                  <TrendingUp className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="strategies">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Option Strategies</CardTitle>
            </CardHeader>
            <CardContent>
              {strategies.length > 0 ? (
                <div className="space-y-4">
                  {strategies.map((strategy, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-lg font-medium text-trading-light">{strategy.name}</h4>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-green-400 border-green-400">
                            {strategy.probability}% Win
                          </Badge>
                          <Button size="sm">Execute</Button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-trading-muted">{strategy.setup}</span>
                        <span className="text-lg font-bold text-green-400">{strategy.pnl}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Strategies Available',
                  'AI will recommend strategies based on market conditions',
                  <Zap className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="greeks">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Portfolio Greeks</CardTitle>
            </CardHeader>
            <CardContent>
              {greeks.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {greeks.map((greek, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-lg font-medium text-trading-light">{greek.name}</h4>
                        <Badge variant="outline" className={greek.change.startsWith('+') ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {greek.change}
                        </Badge>
                      </div>
                      <div className="text-2xl font-bold text-blue-400 mb-1">{greek.value}</div>
                      <div className="text-xs text-trading-muted">{greek.description}</div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Greeks Data',
                  'Portfolio greeks will display when positions are active',
                  <Target className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Options Flow Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {renderEmptyState(
                'Options Flow Visualization',
                'Real-time options flow, unusual activity, and volatility analysis',
                <Activity className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
