
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Shield, Settings, AlertTriangle, Activity } from "lucide-react";
import { useState } from "react";

interface RiskMetrics {
  riskScore: string;
  activeAlerts: number;
  maxDrawdown: number;
  var95: number;
}

interface RiskAgentProps {
  isActive?: boolean;
  onToggle?: (active: boolean) => void;
  metrics?: RiskMetrics;
  onDrawdownLimitChange?: (limit: number) => void;
  onPortfolioHeatChange?: (heat: number) => void;
  isLoading?: boolean;
}

export const RiskAgent = ({
  isActive: propIsActive,
  onToggle,
  metrics,
  onDrawdownLimitChange,
  onPortfolioHeatChange,
  isLoading = false
}: RiskAgentProps) => {
  const [localIsActive, setLocalIsActive] = useState(true);
  const [maxDrawdown, setMaxDrawdown] = useState([5]);
  const [portfolioHeat, setPortfolioHeat] = useState([15]);

  const isActive = propIsActive !== undefined ? propIsActive : localIsActive;

  const handleToggle = (active: boolean) => {
    if (onToggle) {
      onToggle(active);
    } else {
      setLocalIsActive(active);
    }
  };

  const handleDrawdownChange = (value: number[]) => {
    setMaxDrawdown(value);
    if (onDrawdownLimitChange) {
      onDrawdownLimitChange(value[0]);
    }
  };

  const handlePortfolioHeatChange = (value: number[]) => {
    setPortfolioHeat(value);
    if (onPortfolioHeatChange) {
      onPortfolioHeatChange(value[0]);
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Shield className="h-5 w-5 mr-2 text-blue-400" />
            Risk Management Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
              {isActive ? "Monitoring" : "Offline"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Risk Score</div>
            <div className="text-2xl font-bold text-green-400">
              {isLoading ? '...' : (metrics ? metrics.riskScore : '--')}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Active Alerts</div>
            <div className="text-2xl font-bold text-yellow-400">
              {isLoading ? '...' : (metrics ? metrics.activeAlerts : '--')}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Max Drawdown</div>
            <div className="text-2xl font-bold text-trading-light">
              {isLoading ? '...' : (metrics ? `${metrics.maxDrawdown}%` : '--')}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">VaR (95%)</div>
            <div className="text-2xl font-bold text-trading-light">
              {isLoading ? '...' : (metrics ? `₹${metrics.var95.toLocaleString()}` : '--')}
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Risk Monitoring</span>
            <Switch checked={isActive} onCheckedChange={handleToggle} disabled={isLoading} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Max Drawdown Limit</span>
              <span className="text-sm text-trading-muted">{maxDrawdown[0]}%</span>
            </div>
            <Slider
              value={maxDrawdown}
              onValueChange={handleDrawdownChange}
              max={20}
              min={1}
              step={1}
              className="w-full"
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Portfolio Heat</span>
              <span className="text-sm text-trading-muted">{portfolioHeat[0]}%</span>
            </div>
            <Slider
              value={portfolioHeat}
              onValueChange={handlePortfolioHeatChange}
              max={50}
              min={5}
              step={1}
              className="w-full"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1" disabled={isLoading}>
            <Settings className="h-4 w-4 mr-2" />
            Rules
          </Button>
          <Button size="sm" variant="outline" className="flex-1" disabled={isLoading}>
            <AlertTriangle className="h-4 w-4 mr-2" />
            Alerts
          </Button>
          <Button size="sm" variant="outline" className="flex-1" disabled={isLoading}>
            <Activity className="h-4 w-4 mr-2" />
            Reports
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
