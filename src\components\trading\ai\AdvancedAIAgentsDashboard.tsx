
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Bot, 
  Zap, 
  Target, 
  TrendingUp, 
  Shield, 
  BarChart3,
  Activity,
  ArrowLeft,
  Play,
  Pause,
  Settings
} from "lucide-react";

interface AIAgent {
  id: string;
  name: string;
  type: 'Strategy' | 'Analysis' | 'Risk' | 'Execution';
  status: 'active' | 'paused' | 'stopped';
  performance: number;
  accuracy: number;
  trades: number;
  pnl: number;
  description: string;
  lastSignal?: {
    action: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    timestamp: number;
    symbol: string;
  };
}

interface AdvancedAIAgentsDashboardProps {
  onBack?: () => void;
}

export const AdvancedAIAgentsDashboard = ({ onBack }: AdvancedAIAgentsDashboardProps) => {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [agents, setAgents] = useState<AIAgent[]>([]);

  // Calculate agent performance metrics
  const calculateAgentMetrics = (agent: AIAgent) => {
    const winRate = agent.accuracy;
    const sharpeRatio = agent.pnl > 0 ? (agent.performance / 16) : 0; // Simplified Sharpe calculation
    const profitFactor = agent.pnl > 0 ? Math.abs(agent.pnl) / Math.max(1, Math.abs(agent.pnl * 0.3)) : 0;
    
    return { winRate, sharpeRatio, profitFactor };
  };

  // Real AI agent configuration
  const createAgent = (config: Partial<AIAgent>) => {
    const newAgent: AIAgent = {
      id: Date.now().toString(),
      name: config.name || 'New Agent',
      type: config.type || 'Strategy',
      status: 'stopped',
      performance: 0,
      accuracy: 0,
      trades: 0,
      pnl: 0,
      description: config.description || '',
      ...config
    };
    
    setAgents(prev => [...prev, newAgent]);
  };

  const toggleAgentStatus = (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: agent.status === 'active' ? 'paused' : 'active' }
        : agent
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 border-green-400';
      case 'paused': return 'text-yellow-400 border-yellow-400';
      case 'stopped': return 'text-red-400 border-red-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const agentTemplates = [
    {
      name: 'LSTM Price Predictor',
      type: 'Analysis' as const,
      description: 'Deep learning model for price prediction using LSTM networks',
      icon: <Brain className="h-5 w-5" />
    },
    {
      name: 'XGBoost Trend Detector',
      type: 'Analysis' as const,
      description: 'Gradient boosting model for trend detection and momentum analysis',
      icon: <TrendingUp className="h-5 w-5" />
    },
    {
      name: 'CNN Pattern Recognition',
      type: 'Strategy' as const,
      description: 'Convolutional neural network for chart pattern recognition',
      icon: <Target className="h-5 w-5" />
    },
    {
      name: 'Risk Management AI',
      type: 'Risk' as const,
      description: 'AI-powered risk assessment and portfolio optimization',
      icon: <Shield className="h-5 w-5" />
    },
    {
      name: 'Sentiment Analysis Bot',
      type: 'Analysis' as const,
      description: 'BERT-based sentiment analysis from news and social media',
      icon: <Activity className="h-5 w-5" />
    },
    {
      name: 'Smart Order Execution',
      type: 'Execution' as const,
      description: 'AI-driven order execution with market impact minimization',
      icon: <Zap className="h-5 w-5" />
    }
  ];

  return (
    <div className="space-y-4">
      {/* Header with Back Button */}
      {onBack && (
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h2 className="text-xl font-bold text-trading-light">Advanced AI Agents</h2>
        </div>
      )}

      {/* AI Agents Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="glassmorphism-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-blue-400" />
              <div>
                <div className="text-sm text-trading-muted">Active Agents</div>
                <div className="text-xl font-bold text-trading-light">
                  {agents.filter(a => a.status === 'active').length}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-400" />
              <div>
                <div className="text-sm text-trading-muted">Avg Performance</div>
                <div className="text-xl font-bold text-green-400">
                  {agents.length > 0 
                    ? `${(agents.reduce((sum, a) => sum + a.performance, 0) / agents.length).toFixed(1)}%`
                    : '0%'
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-400" />
              <div>
                <div className="text-sm text-trading-muted">Total Trades</div>
                <div className="text-xl font-bold text-trading-light">
                  {agents.reduce((sum, a) => sum + a.trades, 0)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-yellow-400" />
              <div>
                <div className="text-sm text-trading-muted">Total P&L</div>
                <div className={`text-xl font-bold ${agents.reduce((sum, a) => sum + a.pnl, 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  ₹{agents.reduce((sum, a) => sum + a.pnl, 0).toFixed(0)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="active">Active Agents</TabsTrigger>
          <TabsTrigger value="templates">Agent Templates</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light">Deployed AI Agents</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] w-full">
                {agents.length > 0 ? (
                  <div className="space-y-4">
                    {agents.map((agent) => {
                      const metrics = calculateAgentMetrics(agent);
                      return (
                        <div key={agent.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <Bot className="h-5 w-5 text-blue-400" />
                              <div>
                                <div className="font-medium text-trading-light">{agent.name}</div>
                                <div className="text-sm text-trading-muted">{agent.type} Agent</div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className={getStatusColor(agent.status)}>
                                {agent.status.toUpperCase()}
                              </Badge>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => toggleAgentStatus(agent.id)}
                              >
                                {agent.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                              </Button>
                              <Button size="sm" variant="outline">
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                            <div>
                              <div className="text-xs text-trading-muted">Performance</div>
                              <div className={`text-sm font-medium ${agent.performance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {agent.performance >= 0 ? '+' : ''}{agent.performance.toFixed(1)}%
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-trading-muted">Accuracy</div>
                              <div className="text-sm font-medium text-blue-400">{agent.accuracy.toFixed(1)}%</div>
                            </div>
                            <div>
                              <div className="text-xs text-trading-muted">Trades</div>
                              <div className="text-sm font-medium text-trading-light">{agent.trades}</div>
                            </div>
                            <div>
                              <div className="text-xs text-trading-muted">P&L</div>
                              <div className={`text-sm font-medium ${agent.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                ₹{agent.pnl.toFixed(0)}
                              </div>
                            </div>
                          </div>

                          {agent.lastSignal && (
                            <div className="p-2 bg-blue-600/20 rounded border border-blue-600/30">
                              <div className="text-xs text-blue-400 font-medium">Latest Signal</div>
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-trading-light">
                                  {agent.lastSignal.action} {agent.lastSignal.symbol}
                                </span>
                                <span className="text-trading-muted">
                                  Confidence: {agent.lastSignal.confidence}%
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-trading-muted">
                    <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No AI Agents Deployed</h3>
                    <p className="text-sm">Create and deploy AI agents from templates to get started</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light">AI Agent Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] w-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {agentTemplates.map((template, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="p-2 bg-blue-600/20 rounded">
                          {template.icon}
                        </div>
                        <div>
                          <div className="font-medium text-trading-light">{template.name}</div>
                          <Badge variant="outline" className="text-blue-400 border-blue-400 text-xs">
                            {template.type}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-trading-muted mb-3">{template.description}</p>
                      <Button 
                        size="sm" 
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={() => createAgent(template)}
                      >
                        Deploy Agent
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light">Agent Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] w-full">
                {agents.length > 0 ? (
                  <div className="space-y-4">
                    {agents.map((agent) => {
                      const metrics = calculateAgentMetrics(agent);
                      return (
                        <div key={agent.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                          <div className="flex items-center justify-between mb-4">
                            <div className="font-medium text-trading-light">{agent.name}</div>
                            <Badge variant="outline" className={getStatusColor(agent.status)}>
                              {agent.status}
                            </Badge>
                          </div>
                          
                          <div className="space-y-3">
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="text-trading-muted">Win Rate</span>
                                <span className="text-blue-400">{metrics.winRate.toFixed(1)}%</span>
                              </div>
                              <Progress value={metrics.winRate} className="h-2" />
                            </div>
                            
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="text-trading-muted">Sharpe Ratio</span>
                                <span className="text-green-400">{metrics.sharpeRatio.toFixed(2)}</span>
                              </div>
                              <Progress value={Math.min(100, metrics.sharpeRatio * 20)} className="h-2" />
                            </div>
                            
                            <div>
                              <div className="flex justify-between text-sm mb-1">
                                <span className="text-trading-muted">Profit Factor</span>
                                <span className="text-purple-400">{metrics.profitFactor.toFixed(2)}</span>
                              </div>
                              <Progress value={Math.min(100, metrics.profitFactor * 25)} className="h-2" />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-trading-muted">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">No performance data available</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
