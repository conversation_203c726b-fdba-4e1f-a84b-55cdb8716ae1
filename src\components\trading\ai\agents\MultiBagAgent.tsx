
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, Brain, Target, DollarSign } from "lucide-react";

export const MultiBagAgent = () => {
  const fundamentalSignals = [
    { symbol: "HDFC BANK", score: 92, potential: "3x-5x", timeframe: "2-3 years", confidence: 88 },
    { symbol: "TCS", score: 89, potential: "2x-3x", timeframe: "18 months", confidence: 85 },
    { symbol: "RELIANCE", score: 86, potential: "2x-4x", timeframe: "2 years", confidence: 82 },
    { symbol: "INFY", score: 84, potential: "1.5x-2.5x", timeframe: "12-18 months", confidence: 79 }
  ];

  const metrics = [
    { label: "Portfolio ROI", value: "247%", change: "+23%", color: "text-green-400" },
    { label: "Active Picks", value: "12", change: "+3", color: "text-blue-400" },
    { label: "Avg Hold Period", value: "18M", change: "-2M", color: "text-purple-400" },
    { label: "Success Rate", value: "84%", change: "+7%", color: "text-green-400" }
  ];

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            MultiBag AI Agent
            <Badge className="ml-2 bg-green-600">Active</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            {metrics.map((metric, index) => (
              <div key={index} className="text-center">
                <div className={`text-lg font-bold ${metric.color}`}>{metric.value}</div>
                <div className="text-xs text-trading-muted">{metric.label}</div>
                <div className="text-xs text-green-400">{metric.change}</div>
              </div>
            ))}
          </div>
          
          <div className="text-sm text-trading-light mb-4">
            <div className="flex items-center mb-2">
              <Brain className="h-4 w-4 mr-2 text-purple-400" />
              <span>AI Analysis: 5Y Financial Data + Smart Money Flow + Management Quality</span>
            </div>
            <div className="text-trading-muted">
              Last Analysis: 2 hours ago • Next Scan: 4 hours • Models: XGBoost + Linear Regression Ensemble
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Top Multibagger Opportunities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {fundamentalSignals.map((signal, index) => (
              <div key={index} className="p-3 bg-trading-dark rounded border border-trading-border">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <div className="text-trading-light font-medium">{signal.symbol}</div>
                    <Badge variant="outline" className="text-green-400 border-green-400">
                      {signal.potential}
                    </Badge>
                    <div className="text-sm text-trading-muted">{signal.timeframe}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-sm text-blue-400">{signal.confidence}% Confidence</div>
                    <Button size="sm" variant="outline">Analyze</Button>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-sm text-trading-muted">AI Score:</div>
                  <Progress value={signal.score} className="flex-1 h-2" />
                  <div className="text-sm text-trading-light">{signal.score}/100</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
