import { BrokerAPIService } from './BrokerAPIService';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Portfolio {
  totalValue: number;
  availableCash: number;
  totalPnL: number;
  dayPnL: number;
  positions: (EquityPosition | OptionsPosition)[];
  marginUsed: number;
}

export interface EquityPosition {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  realizedPnL: number;
}

export interface OptionsPosition extends EquityPosition {
  optionType: 'CALL' | 'PUT';
  strike: number;
  expiry: Date;
  premium: number;
  impliedVolatility?: number;
  greeks: {
    delta: number;
    gamma: number;
    theta: number;
    vega: number;
    rho: number;
  };
}

export interface EquityOrder {
  id: string;
  symbol: string;
  quantity: number;
  price: number;
  orderType: 'MARKET' | 'LIMIT' | 'STOP' | 'STOP_LIMIT';
  side: 'BUY' | 'SELL';
  status: 'PENDING' | 'FILLED' | 'CANCELLED' | 'REJECTED';
  timeInForce: 'DAY' | 'GTC' | 'IOC' | 'FOK';
  timestamp: Date;
  stopPrice?: number;
}

export interface OptionsOrder extends EquityOrder {
  optionType: 'CALL' | 'PUT';
  strike: number;
  expiry: Date;
  premium: number;
  impliedVolatility?: number;
  greeks: {
    delta: number;
    gamma: number;
    theta: number;
    vega: number;
    rho: number;
  };
}

export class RealPaperTradingService {
  private brokerService: BrokerAPIService;
  private equityOrders: Map<string, EquityOrder> = new Map();
  private optionsOrders: Map<string, OptionsOrder> = new Map();
  private equityPositions: Map<string, EquityPosition> = new Map();
  private optionsPositions: Map<string, OptionsPosition> = new Map();
  
  private portfolioSubject = new BehaviorSubject<Portfolio>({
    totalValue: 1000000,
    availableCash: 1000000,
    totalPnL: 0,
    dayPnL: 0,
    positions: [],
    marginUsed: 0
  });

  constructor(brokerService: BrokerAPIService) {
    this.brokerService = brokerService;
    this.initializeMarketDataSubscription();
  }

  private initializeMarketDataSubscription(): void {
    this.brokerService.getMarketDataStream().subscribe(marketData => {
      this.updatePositions(marketData);
    });
  }

  private updatePositions(marketData: Map<string, any>): void {
    let totalValue = this.portfolioSubject.value.availableCash;
    let totalPnL = 0;
    let dayPnL = 0;
    const positions: (EquityPosition | OptionsPosition)[] = [];

    // Update equity positions
    for (const [symbol, position] of this.equityPositions) {
      const market = marketData.get(symbol);
      if (market) {
        position.currentPrice = market.price;
        position.marketValue = position.quantity * position.currentPrice;
        position.unrealizedPnL = (position.currentPrice - position.averagePrice) * position.quantity;
        totalValue += position.marketValue;
        totalPnL += position.unrealizedPnL;
        positions.push(position);
      }
    }

    // Update options positions
    for (const [symbol, position] of this.optionsPositions) {
      const market = marketData.get(symbol);
      if (market) {
        position.currentPrice = market.price;
        position.marketValue = position.quantity * position.currentPrice;
        position.unrealizedPnL = (position.currentPrice - position.averagePrice) * position.quantity;
        totalValue += position.marketValue;
        totalPnL += position.unrealizedPnL;
        positions.push(position);
      }
    }

    this.portfolioSubject.next({
      ...this.portfolioSubject.value,
      totalValue,
      totalPnL,
      positions
    });
  }

  private generateOrderId(): string {
    return 'ORDER-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
  }

  async placeEquityOrder(orderRequest: Omit<EquityOrder, 'id' | 'status' | 'timestamp'>): Promise<string> {
    const order: EquityOrder = {
      ...orderRequest,
      id: this.generateOrderId(),
      status: 'PENDING',
      timestamp: new Date()
    };

    this.equityOrders.set(order.id, order);
    
    // Process order immediately for paper trading
    await this.processEquityOrder(order);
    
    return order.id;
  }

  async placeOptionsOrder(orderRequest: Omit<OptionsOrder, 'id' | 'status' | 'timestamp'>): Promise<string> {
    const order: OptionsOrder = {
      ...orderRequest,
      id: this.generateOrderId(),
      status: 'PENDING',
      timestamp: new Date(),
      premium: this.calculateOptionPremium(orderRequest),
      greeks: this.calculateGreeks(orderRequest)
    };

    this.optionsOrders.set(order.id, order);
    
    // Process order immediately for paper trading
    await this.processOptionsOrder(order);
    
    return order.id;
  }

  private async processEquityOrder(order: EquityOrder): Promise<void> {
    // Simulate order execution
    order.status = 'FILLED';
    
    // Update portfolio
    const portfolio = this.portfolioSubject.value;
    const marketPrice = order.price; // Assume market order fills at current price
    const cost = marketPrice * order.quantity;

    if (portfolio.availableCash >= cost) {
      portfolio.availableCash -= cost;
      
      // Update or create position
      let position = this.equityPositions.get(order.symbol);
      if (position) {
        // Update existing position
        const totalCost = position.averagePrice * position.quantity + cost;
        const totalQuantity = position.quantity + order.quantity;
        position.averagePrice = totalCost / totalQuantity;
        position.quantity = totalQuantity;
        position.marketValue = position.quantity * marketPrice;
        position.unrealizedPnL = (marketPrice - position.averagePrice) * position.quantity;
      } else {
        // Create new position
        position = {
          symbol: order.symbol,
          quantity: order.quantity,
          averagePrice: marketPrice,
          currentPrice: marketPrice,
          marketValue: marketPrice * order.quantity,
          unrealizedPnL: 0,
          realizedPnL: 0
        };
        this.equityPositions.set(order.symbol, position);
      }
      
      this.portfolioSubject.next(portfolio);
    } else {
      order.status = 'REJECTED';
      console.warn('Insufficient funds to place order');
    }
  }

  private async processOptionsOrder(order: OptionsOrder): Promise<void> {
    // Simulate option order execution
    order.status = 'FILLED';
    
    // Update portfolio
    const portfolio = this.portfolioSubject.value;
    const premium = order.premium * order.quantity;

    if (portfolio.availableCash >= premium) {
      portfolio.availableCash -= premium;
      
      // Update or create position
      let position = this.optionsPositions.get(order.symbol);
      if (position) {
        // Update existing position
        const totalCost = position.averagePrice * position.quantity + premium;
        const totalQuantity = position.quantity + order.quantity;
        position.averagePrice = totalCost / totalQuantity;
        position.quantity = totalQuantity;
        position.marketValue = position.quantity * order.premium;
        position.unrealizedPnL = (order.premium - position.averagePrice) * position.quantity;
      } else {
        // Create new position
        position = {
          symbol: order.symbol,
          quantity: order.quantity,
          averagePrice: order.premium,
          currentPrice: order.premium,
          marketValue: order.premium * order.quantity,
          unrealizedPnL: 0,
          realizedPnL: 0,
          optionType: order.optionType,
          strike: order.strike,
          expiry: order.expiry,
          premium: order.premium,
          greeks: order.greeks
        };
        this.optionsPositions.set(order.symbol, position);
      }
      
      this.portfolioSubject.next(portfolio);
    } else {
      order.status = 'REJECTED';
      console.warn('Insufficient funds to place order');
    }
  }

  private calculateOptionPremium(order: Partial<OptionsOrder>): number {
    // Black-Scholes option pricing model
    if (!order.strike || !order.price || !order.expiry) return 0;

    const S = order.price; // Current stock price
    const K = order.strike; // Strike price
    const T = this.getTimeToExpiry(order.expiry); // Time to expiry in years
    const r = 0.05; // Risk-free rate (5%)
    const sigma = order.impliedVolatility || 0.2; // Volatility (20% default)

    return this.blackScholes(S, K, T, r, sigma, order.optionType === 'CALL');
  }

  private blackScholes(S: number, K: number, T: number, r: number, sigma: number, isCall: boolean): number {
    const d1 = (Math.log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.sqrt(T));
    const d2 = d1 - sigma * Math.sqrt(T);
    
    const N = this.normalCDF;
    
    if (isCall) {
      return S * N(d1) - K * Math.exp(-r * T) * N(d2);
    } else {
      return K * Math.exp(-r * T) * N(-d2) - S * N(-d1);
    }
  }

  private normalCDF(x: number): number {
    // Approximation of normal cumulative distribution function
    const a1 =  0.254829592;
    const a2 = -0.284496736;
    const a3 =  1.421413741;
    const a4 = -1.453152027;
    const a5 =  1.061405429;
    const p  =  0.3275911;
    
    const sign = x < 0 ? -1 : 1;
    x = Math.abs(x) / Math.sqrt(2.0);
    
    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
    
    return 0.5 * (1.0 + sign * y);
  }

  private calculateGreeks(order: Partial<OptionsOrder>): OptionsOrder['greeks'] {
    if (!order.strike || !order.price || !order.expiry) {
      return { delta: 0, gamma: 0, theta: 0, vega: 0, rho: 0 };
    }

    const S = order.price;
    const K = order.strike;
    const T = this.getTimeToExpiry(order.expiry);
    const r = 0.05;
    const sigma = order.impliedVolatility || 0.2;
    
    const d1 = (Math.log(S / K) + (r + 0.5 * sigma * sigma) * T) / (sigma * Math.sqrt(T));
    const d2 = d1 - sigma * Math.sqrt(T);
    
    const N = this.normalCDF;
    const n = (x: number) => Math.exp(-0.5 * x * x) / Math.sqrt(2 * Math.PI); // Normal PDF
    
    // Delta
    const delta = order.optionType === 'CALL' ? N(d1) : N(d1) - 1;
    
    // Gamma
    const gamma = n(d1) / (S * sigma * Math.sqrt(T));
    
    // Theta
    const theta = order.optionType === 'CALL'
      ? (-S * n(d1) * sigma / (2 * Math.sqrt(T)) - r * K * Math.exp(-r * T) * N(d2)) / 365
      : (-S * n(d1) * sigma / (2 * Math.sqrt(T)) + r * K * Math.exp(-r * T) * N(-d2)) / 365;
    
    // Vega
    const vega = S * n(d1) * Math.sqrt(T) / 100;
    
    // Rho
    const rho = order.optionType === 'CALL'
      ? K * T * Math.exp(-r * T) * N(d2) / 100
      : -K * T * Math.exp(-r * T) * N(-d2) / 100;
    
    return { delta, gamma, theta, vega, rho };
  }

  private getTimeToExpiry(expiry: Date): number {
    const now = new Date();
    const diffTime = Math.abs(expiry.getTime() - now.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays / 365; // Convert to years
  }

  getPortfolioStream(): Observable<Portfolio> {
    return this.portfolioSubject.asObservable();
  }

  getEquityOrders(): EquityOrder[] {
    return Array.from(this.equityOrders.values());
  }

  getOptionsOrders(): OptionsOrder[] {
    return Array.from(this.optionsOrders.values());
  }

  getEquityPositions(): EquityPosition[] {
    return Array.from(this.equityPositions.values());
  }

  getOptionsPositions(): OptionsPosition[] {
    return Array.from(this.optionsPositions.values());
  }
}
