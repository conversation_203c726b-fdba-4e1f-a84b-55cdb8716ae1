
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { realTimeDataService } from '../../../services/RealTimeDataService';
import { useMarketStore } from '../../../store/useMarketStore';

interface MarketDataContextType {
  data: Record<string, any>;
  isConnected: boolean;
  subscribe: (symbol: string) => void;
  unsubscribe: (symbol: string) => void;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  latency: number;
}

const MarketDataContext = createContext<MarketDataContextType | null>(null);

export const useMarketData = () => {
  const context = useContext(MarketDataContext);
  if (!context) {
    throw new Error('useMarketData must be used within a MarketDataProvider');
  }
  return context;
};

interface MarketDataProviderProps {
  children: React.ReactNode;
  autoConnect?: boolean;
}

export const MarketDataProvider: React.FC<MarketDataProviderProps> = ({ 
  children, 
  autoConnect = true 
}) => {
  const { toast } = useToast();
  
  // Subscribe to market store
  const marketData = useMarketStore(state => state.marketData);
  const isConnected = useMarketStore(state => state.isConnected);
  const connectionStatus = useMarketStore(state => state.connectionStatus);
  const latency = useMarketStore(state => state.latency);
  
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    if (autoConnect && !hasInitialized) {
      initializeConnection();
      setHasInitialized(true);
    }

    return () => {
      if (hasInitialized) {
        realTimeDataService.disconnect();
      }
    };
  }, [autoConnect, hasInitialized]);

  // Listen to connection status changes for notifications
  useEffect(() => {
    const prevStatus = useMarketStore.getState().connectionStatus;
    
    const unsubscribe = useMarketStore.subscribe(
      (state) => state.connectionStatus,
      (status) => {
        if (status !== prevStatus) {
          handleConnectionStatusChange(status);
        }
      }
    );

    return unsubscribe;
  }, []);

  const initializeConnection = async () => {
    try {
      await realTimeDataService.connect();
      realTimeDataService.startBufferFlush();
      
      toast({
        title: "Market Data Connected",
        description: "Real-time market feed is now active",
      });
    } catch (error) {
      console.error('Failed to initialize market data connection:', error);
      toast({
        title: "Connection Failed",
        description: "Unable to connect to market data feed",
        variant: "destructive",
      });
    }
  };

  const handleConnectionStatusChange = (status: string) => {
    switch (status) {
      case 'connected':
        toast({
          title: "Connected",
          description: "Market data feed reconnected",
        });
        break;
      case 'disconnected':
        toast({
          title: "Disconnected",
          description: "Market data feed disconnected",
          variant: "destructive",
        });
        break;
      case 'error':
        toast({
          title: "Connection Error",
          description: "Market data feed error occurred",
          variant: "destructive",
        });
        break;
    }
  };

  const subscribe = (symbol: string) => {
    realTimeDataService.subscribeToSymbol(symbol, ['tick', 'orderbook']);
  };

  const unsubscribe = (symbol: string) => {
    realTimeDataService.unsubscribeFromSymbol(symbol);
  };

  const value: MarketDataContextType = {
    data: marketData,
    isConnected,
    subscribe,
    unsubscribe,
    connectionStatus,
    latency
  };

  return (
    <MarketDataContext.Provider value={value}>
      {children}
    </MarketDataContext.Provider>
  );
};
