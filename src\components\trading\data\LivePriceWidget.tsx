
import { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown } from "lucide-react";
import { useMarketData } from "./MarketDataProvider";

interface LivePriceWidgetProps {
  symbol: string;
  showChange?: boolean;
  compact?: boolean;
}

export const LivePriceWidget: React.FC<LivePriceWidgetProps> = ({ 
  symbol, 
  showChange = true, 
  compact = false 
}) => {
  const { data, subscribe, unsubscribe } = useMarketData();
  const symbolData = data[symbol];

  useEffect(() => {
    subscribe(symbol);
    return () => unsubscribe(symbol);
  }, [symbol, subscribe, unsubscribe]);

  if (!symbolData) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="p-3">
          <div className="text-xs text-trading-muted">Loading {symbol}...</div>
        </CardContent>
      </Card>
    );
  }

  const isPositive = symbolData.change >= 0;
  const changeColor = isPositive ? 'text-green-400' : 'text-red-400';
  const TrendIcon = isPositive ? TrendingUp : TrendingDown;

  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium text-trading-light">
          ₹{symbolData.price.toLocaleString()}
        </span>
        {showChange && (
          <Badge variant="outline" className={`text-xs ${changeColor} border-current`}>
            <TrendIcon className="h-3 w-3 mr-1" />
            {symbolData.changePercent.toFixed(2)}%
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-trading-light">{symbol}</span>
          <div className={`w-2 h-2 rounded-full bg-green-500 animate-pulse`}></div>
        </div>
        
        <div className="space-y-2">
          <div className="text-xl font-bold text-trading-light">
            ₹{symbolData.price.toLocaleString()}
          </div>
          
          {showChange && (
            <div className="flex items-center space-x-2">
              <TrendIcon className={`h-4 w-4 ${changeColor}`} />
              <span className={`text-sm font-medium ${changeColor}`}>
                ₹{Math.abs(symbolData.change).toFixed(2)}
              </span>
              <span className={`text-sm ${changeColor}`}>
                ({symbolData.changePercent.toFixed(2)}%)
              </span>
            </div>
          )}
          
          <div className="grid grid-cols-3 gap-2 text-xs text-trading-muted">
            <div>
              <div>High</div>
              <div className="text-trading-light">₹{symbolData.high.toLocaleString()}</div>
            </div>
            <div>
              <div>Low</div>
              <div className="text-trading-light">₹{symbolData.low.toLocaleString()}</div>
            </div>
            <div>
              <div>Volume</div>
              <div className="text-trading-light">{(symbolData.volume / 1000000).toFixed(1)}M</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
