
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Code, Globe, Key, Book, Zap } from "lucide-react";

export const APIDocumentation = () => {
  const endpoints = [
    {
      method: "GET",
      path: "/api/signals",
      description: "Get latest trading signals from all AI agents",
      response: `{
  "signals": [
    {
      "symbol": "RELIANCE",
      "action": "BUY",
      "confidence": 0.87,
      "agent": "IntradayAgent",
      "timestamp": "2025-05-24T10:30:00Z"
    }
  ]
}`
    },
    {
      method: "POST",
      path: "/api/orders",
      description: "Place trading order",
      request: `{
  "symbol": "NIFTY",
  "side": "BUY",
  "quantity": 25,
  "price": 24150,
  "orderType": "LIMIT"
}`,
      response: `{
  "orderId": "order_123456",
  "status": "PENDING",
  "message": "Order placed successfully"
}`
    },
    {
      method: "GET",
      path: "/api/portfolio",
      description: "Get current portfolio positions",
      response: `{
  "positions": [
    {
      "symbol": "RELIANCE",
      "quantity": 50,
      "avgPrice": 2520.30,
      "currentPrice": 2523.45,
      "pnl": 157.50
    }
  ],
  "totalValue": 245680.50
}`
    },
    {
      method: "GET",
      path: "/api/models/performance",
      description: "Get AI model performance metrics",
      response: `{
  "models": [
    {
      "name": "LSTM_Intraday",
      "accuracy": 0.84,
      "sharpeRatio": 1.67,
      "maxDrawdown": 0.035
    }
  ]
}`
    }
  ];

  const webhooks = [
    {
      event: "signal.generated",
      description: "Triggered when a new trading signal is generated",
      payload: `{
  "event": "signal.generated",
  "data": {
    "symbol": "BANKNIFTY",
    "action": "SELL",
    "confidence": 0.91,
    "agent": "SwingAgent"
  }
}`
    },
    {
      event: "order.filled",
      description: "Triggered when an order gets executed",
      payload: `{
  "event": "order.filled",
  "data": {
    "orderId": "order_789012",
    "symbol": "TCS",
    "quantity": 10,
    "price": 3920.50
  }
}`
    },
    {
      event: "risk.alert",
      description: "Triggered when risk thresholds are breached",
      payload: `{
  "event": "risk.alert",
  "data": {
    "type": "DRAWDOWN",
    "level": 0.052,
    "threshold": 0.05
  }
}`
    }
  ];

  const sdkExamples = {
    javascript: `// Install: npm install ai-trading-sdk

import { AITradingClient } from 'ai-trading-sdk';

const client = new AITradingClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.yourplatform.com'
});

// Get signals
const signals = await client.getSignals();

// Place order
const order = await client.placeOrder({
  symbol: 'RELIANCE',
  side: 'BUY',
  quantity: 50,
  orderType: 'MARKET'
});`,
    python: `# Install: pip install ai-trading-sdk

from ai_trading import AITradingClient

client = AITradingClient(
    api_key='your-api-key',
    base_url='https://api.yourplatform.com'
)

# Get signals
signals = client.get_signals()

# Place order
order = client.place_order(
    symbol='RELIANCE',
    side='BUY',
    quantity=50,
    order_type='MARKET'
)`,
    curl: `# Get signals
curl -X GET "https://api.yourplatform.com/api/signals" \\
  -H "Authorization: Bearer your-api-key"

# Place order
curl -X POST "https://api.yourplatform.com/api/orders" \\
  -H "Authorization: Bearer your-api-key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "symbol": "RELIANCE",
    "side": "BUY",
    "quantity": 50,
    "orderType": "MARKET"
  }'`
  };

  return (
    <div className="space-y-4">
      {/* API Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Code className="h-5 w-5 mr-2 text-blue-400" />
            API Documentation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">REST API</div>
              <div className="text-xs text-trading-muted">HTTP endpoints</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">WebSocket</div>
              <div className="text-xs text-trading-muted">Real-time data</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">Webhooks</div>
              <div className="text-xs text-trading-muted">Event notifications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">SDKs</div>
              <div className="text-xs text-trading-muted">Multiple languages</div>
            </div>
          </div>
          
          <div className="text-sm text-trading-light">
            <div className="flex items-center mb-2">
              <Globe className="h-4 w-4 mr-2 text-blue-400" />
              <span>Base URL: https://api.yourplatform.com</span>
            </div>
            <div className="flex items-center mb-2">
              <Key className="h-4 w-4 mr-2 text-green-400" />
              <span>Authentication: Bearer Token (API Key required)</span>
            </div>
            <div className="text-trading-muted">
              Rate Limit: 1000 requests/hour • Response Format: JSON
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="endpoints" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="endpoints" className="data-[state=active]:bg-trading-accent">
            <Globe className="h-4 w-4 mr-2" />
            Endpoints
          </TabsTrigger>
          <TabsTrigger value="webhooks" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Webhooks
          </TabsTrigger>
          <TabsTrigger value="sdks" className="data-[state=active]:bg-trading-accent">
            <Code className="h-4 w-4 mr-2" />
            SDKs
          </TabsTrigger>
          <TabsTrigger value="auth" className="data-[state=active]:bg-trading-accent">
            <Key className="h-4 w-4 mr-2" />
            Authentication
          </TabsTrigger>
        </TabsList>

        <TabsContent value="endpoints">
          <div className="space-y-4">
            {endpoints.map((endpoint, index) => (
              <Card key={index} className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-trading-light text-lg">
                      <Badge variant="outline" className={`mr-3 ${endpoint.method === 'GET' ? 'text-green-400 border-green-400' : 'text-blue-400 border-blue-400'}`}>
                        {endpoint.method}
                      </Badge>
                      {endpoint.path}
                    </CardTitle>
                    <Button variant="outline" size="sm">Try it</Button>
                  </div>
                  <div className="text-sm text-trading-muted">{endpoint.description}</div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {endpoint.request && (
                      <div>
                        <div className="text-sm font-medium text-trading-light mb-2">Request Body:</div>
                        <pre className="bg-trading-dark p-3 rounded text-xs text-green-400 overflow-x-auto">
                          {endpoint.request}
                        </pre>
                      </div>
                    )}
                    
                    <div>
                      <div className="text-sm font-medium text-trading-light mb-2">Response:</div>
                      <pre className="bg-trading-dark p-3 rounded text-xs text-blue-400 overflow-x-auto">
                        {endpoint.response}
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="webhooks">
          <div className="space-y-4">
            {webhooks.map((webhook, index) => (
              <Card key={index} className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <CardTitle className="text-trading-light">
                    <Badge variant="outline" className="text-purple-400 border-purple-400 mr-3">
                      EVENT
                    </Badge>
                    {webhook.event}
                  </CardTitle>
                  <div className="text-sm text-trading-muted">{webhook.description}</div>
                </CardHeader>
                <CardContent>
                  <div>
                    <div className="text-sm font-medium text-trading-light mb-2">Payload:</div>
                    <pre className="bg-trading-dark p-3 rounded text-xs text-purple-400 overflow-x-auto">
                      {webhook.payload}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="sdks">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">SDK Examples</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="javascript" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                  <TabsTrigger value="python">Python</TabsTrigger>
                  <TabsTrigger value="curl">cURL</TabsTrigger>
                </TabsList>
                
                {Object.entries(sdkExamples).map(([lang, code]) => (
                  <TabsContent key={lang} value={lang}>
                    <pre className="bg-trading-dark p-4 rounded text-sm text-trading-light overflow-x-auto">
                      {code}
                    </pre>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="auth">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Authentication Guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-900/20 border border-blue-500/50 rounded">
                <h3 className="text-blue-400 font-medium mb-2">1. Get Your API Key</h3>
                <p className="text-sm text-trading-light">
                  Generate your API key from the account settings page. Keep it secure and never share it publicly.
                </p>
              </div>
              
              <div className="p-4 bg-green-900/20 border border-green-500/50 rounded">
                <h3 className="text-green-400 font-medium mb-2">2. Include in Headers</h3>
                <p className="text-sm text-trading-light mb-2">
                  Add the API key to your request headers:
                </p>
                <pre className="bg-trading-dark p-2 rounded text-xs text-green-400">
                  Authorization: Bearer your-api-key-here
                </pre>
              </div>
              
              <div className="p-4 bg-yellow-900/20 border border-yellow-500/50 rounded">
                <h3 className="text-yellow-400 font-medium mb-2">3. Rate Limits</h3>
                <p className="text-sm text-trading-light">
                  Free tier: 100 requests/hour • Pro tier: 1000 requests/hour • Enterprise: Unlimited
                </p>
              </div>
              
              <div className="p-4 bg-purple-900/20 border border-purple-500/50 rounded">
                <h3 className="text-purple-400 font-medium mb-2">4. Error Handling</h3>
                <p className="text-sm text-trading-light mb-2">
                  Standard HTTP status codes are used:
                </p>
                <ul className="text-xs text-trading-muted space-y-1">
                  <li>• 200: Success</li>
                  <li>• 401: Unauthorized (invalid API key)</li>
                  <li>• 429: Rate limit exceeded</li>
                  <li>• 500: Internal server error</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
