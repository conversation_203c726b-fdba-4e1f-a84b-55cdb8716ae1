
export interface ValidationRule {
  field: string;
  type: 'number' | 'string' | 'date' | 'boolean';
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class DataValidationService {
  private tickValidationRules: ValidationRule[] = [
    { field: 'symbol', type: 'string', required: true },
    { field: 'ltp', type: 'number', required: true, min: 0 },
    { field: 'volume', type: 'number', required: true, min: 0 },
    { field: 'bid', type: 'number', required: true, min: 0 },
    { field: 'ask', type: 'number', required: true, min: 0 },
    { field: 'high', type: 'number', required: true, min: 0 },
    { field: 'low', type: 'number', required: true, min: 0 },
    { field: 'open', type: 'number', required: true, min: 0 }
  ];

  validateTick(tick: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const rule of this.tickValidationRules) {
      const value = tick[rule.field];

      if (rule.required && (value === undefined || value === null)) {
        errors.push(`${rule.field} is required`);
        continue;
      }

      if (value !== undefined && value !== null) {
        if (rule.type === 'number' && typeof value !== 'number') {
          errors.push(`${rule.field} must be a number`);
        }

        if (rule.type === 'string' && typeof value !== 'string') {
          errors.push(`${rule.field} must be a string`);
        }

        if (rule.type === 'number' && typeof value === 'number') {
          if (rule.min !== undefined && value < rule.min) {
            errors.push(`${rule.field} must be >= ${rule.min}`);
          }
          if (rule.max !== undefined && value > rule.max) {
            errors.push(`${rule.field} must be <= ${rule.max}`);
          }
        }
      }
    }

    // Business logic validations
    if (tick.bid && tick.ask && tick.bid >= tick.ask) {
      warnings.push('Bid price is greater than or equal to ask price');
    }

    if (tick.high && tick.low && tick.high < tick.low) {
      errors.push('High price cannot be less than low price');
    }

    if (tick.ltp && tick.high && tick.ltp > tick.high) {
      warnings.push('LTP is higher than high price');
    }

    if (tick.ltp && tick.low && tick.ltp < tick.low) {
      warnings.push('LTP is lower than low price');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateFinancials(data: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const requiredFields = ['symbol', 'date', 'roce', 'roe', 'eps'];
    for (const field of requiredFields) {
      if (!data[field]) {
        errors.push(`${field} is required`);
      }
    }

    if (data.roce && (data.roce < -100 || data.roce > 1000)) {
      warnings.push('ROCE value seems unusual');
    }

    if (data.roe && (data.roe < -100 || data.roe > 1000)) {
      warnings.push('ROE value seems unusual');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  cleanTickData(tick: any): any {
    const cleaned = { ...tick };

    // Round price values to 2 decimal places
    const priceFields = ['ltp', 'bid', 'ask', 'high', 'low', 'open', 'vwap'];
    for (const field of priceFields) {
      if (cleaned[field] && typeof cleaned[field] === 'number') {
        cleaned[field] = Math.round(cleaned[field] * 100) / 100;
      }
    }

    // Ensure volume is integer
    if (cleaned.volume && typeof cleaned.volume === 'number') {
      cleaned.volume = Math.floor(cleaned.volume);
    }

    // Normalize timestamp
    if (cleaned.timestamp && typeof cleaned.timestamp === 'string') {
      cleaned.timestamp = new Date(cleaned.timestamp).toISOString();
    }

    return cleaned;
  }
}
