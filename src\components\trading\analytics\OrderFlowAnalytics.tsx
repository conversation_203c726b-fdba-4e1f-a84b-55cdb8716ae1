
import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, Activity, TrendingUp, TrendingDown, DollarSign, Volume2 } from "lucide-react";

interface OrderFlowData {
  price: number;
  buyVolume: number;
  sellVolume: number;
  netVolume: number;
  trades: number;
  timestamp: number;
}

interface OrderFlowAnalyticsProps {
  onBack?: () => void;
  symbol?: string;
  orderFlowData?: OrderFlowData[];
}

export const OrderFlowAnalytics = ({ 
  onBack, 
  symbol = "NIFTY",
  orderFlowData = []
}: OrderFlowAnalyticsProps) => {
  const [flowMetrics, setFlowMetrics] = useState({
    totalBuyVolume: 0,
    totalSellVolume: 0,
    netFlow: 0,
    buyPressure: 0,
    sellPressure: 0,
    orderImbalance: 0
  });

  // Calculate real order flow metrics
  const calculateOrderFlowMetrics = (data: OrderFlowData[]) => {
    if (data.length === 0) {
      return {
        totalBuyVolume: 0,
        totalSellVolume: 0,
        netFlow: 0,
        buyPressure: 0,
        sellPressure: 0,
        orderImbalance: 0
      };
    }

    const totalBuyVolume = data.reduce((sum, item) => sum + item.buyVolume, 0);
    const totalSellVolume = data.reduce((sum, item) => sum + item.sellVolume, 0);
    const netFlow = totalBuyVolume - totalSellVolume;
    const totalVolume = totalBuyVolume + totalSellVolume;
    
    const buyPressure = totalVolume > 0 ? (totalBuyVolume / totalVolume) * 100 : 0;
    const sellPressure = totalVolume > 0 ? (totalSellVolume / totalVolume) * 100 : 0;
    const orderImbalance = totalVolume > 0 ? (netFlow / totalVolume) * 100 : 0;

    return {
      totalBuyVolume,
      totalSellVolume,
      netFlow,
      buyPressure,
      sellPressure,
      orderImbalance
    };
  };

  // Calculate Volume Weighted Average Price from order flow
  const calculateVWAP = (data: OrderFlowData[]) => {
    if (data.length === 0) return 0;
    
    let totalVolumePrice = 0;
    let totalVolume = 0;
    
    data.forEach(item => {
      const volume = item.buyVolume + item.sellVolume;
      totalVolumePrice += item.price * volume;
      totalVolume += volume;
    });
    
    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
  };

  // Detect order flow patterns
  const detectOrderFlowPatterns = (data: OrderFlowData[]) => {
    if (data.length < 10) return [];
    
    const patterns = [];
    const recent = data.slice(-10);
    
    // Aggressive buying pattern
    const aggressiveBuying = recent.filter(item => item.buyVolume > item.sellVolume * 1.5).length;
    if (aggressiveBuying >= 7) {
      patterns.push({ type: 'Aggressive Buying', confidence: 85 });
    }
    
    // Aggressive selling pattern
    const aggressiveSelling = recent.filter(item => item.sellVolume > item.buyVolume * 1.5).length;
    if (aggressiveSelling >= 7) {
      patterns.push({ type: 'Aggressive Selling', confidence: 85 });
    }
    
    // Volume spike pattern
    const avgVolume = recent.reduce((sum, item) => sum + item.buyVolume + item.sellVolume, 0) / recent.length;
    const volumeSpikes = recent.filter(item => (item.buyVolume + item.sellVolume) > avgVolume * 2).length;
    if (volumeSpikes >= 3) {
      patterns.push({ type: 'Volume Spike', confidence: 75 });
    }
    
    return patterns;
  };

  useEffect(() => {
    const metrics = calculateOrderFlowMetrics(orderFlowData);
    setFlowMetrics(metrics);
  }, [orderFlowData]);

  const vwap = calculateVWAP(orderFlowData);
  const patterns = detectOrderFlowPatterns(orderFlowData);

  return (
    <div className="space-y-4">
      {/* Header with Back Button */}
      {onBack && (
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h2 className="text-xl font-bold text-trading-light">Order Flow Analytics - {symbol}</h2>
        </div>
      )}

      {/* Order Flow Metrics */}
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Real-Time Order Flow Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Buy Volume</div>
              <div className="text-lg font-bold text-green-400">
                {flowMetrics.totalBuyVolume.toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Sell Volume</div>
              <div className="text-lg font-bold text-red-400">
                {flowMetrics.totalSellVolume.toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Net Flow</div>
              <div className={`text-lg font-bold ${flowMetrics.netFlow >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {flowMetrics.netFlow >= 0 ? '+' : ''}{flowMetrics.netFlow.toLocaleString()}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Buy Pressure</div>
              <div className="text-lg font-bold text-blue-400">
                {flowMetrics.buyPressure.toFixed(1)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Order Imbalance</div>
              <div className={`text-lg font-bold ${flowMetrics.orderImbalance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {flowMetrics.orderImbalance >= 0 ? '+' : ''}{flowMetrics.orderImbalance.toFixed(1)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">VWAP</div>
              <div className="text-lg font-bold text-trading-light">
                ₹{vwap.toFixed(2)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Flow Table with Scroll */}
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Volume2 className="h-5 w-5 mr-2" />
            Order Flow Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] w-full">
            {orderFlowData.length > 0 ? (
              <div className="space-y-1">
                {orderFlowData.slice(-50).reverse().map((flow, index) => (
                  <div key={index} className="grid grid-cols-6 gap-2 p-2 bg-trading-dark rounded text-sm">
                    <div className="text-trading-light">₹{flow.price.toFixed(2)}</div>
                    <div className="text-green-400">{flow.buyVolume.toLocaleString()}</div>
                    <div className="text-red-400">{flow.sellVolume.toLocaleString()}</div>
                    <div className={flow.netVolume >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {flow.netVolume >= 0 ? '+' : ''}{flow.netVolume.toLocaleString()}
                    </div>
                    <div className="text-trading-muted">{flow.trades}</div>
                    <div className="text-trading-muted">
                      {new Date(flow.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                ))}
                <div className="grid grid-cols-6 gap-2 p-2 text-xs text-trading-muted font-medium border-b">
                  <div>Price</div>
                  <div>Buy Vol</div>
                  <div>Sell Vol</div>
                  <div>Net</div>
                  <div>Trades</div>
                  <div>Time</div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-trading-muted">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No order flow data available</p>
                <p className="text-xs mt-1">Connect to data feed to see real-time order flow</p>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Order Flow Patterns */}
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Detected Patterns
          </CardTitle>
        </CardHeader>
        <CardContent>
          {patterns.length > 0 ? (
            <div className="space-y-2">
              {patterns.map((pattern, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded">
                  <Badge variant="outline" className="text-blue-400 border-blue-400">
                    {pattern.type}
                  </Badge>
                  <div className="text-sm text-trading-muted">
                    Confidence: {pattern.confidence}%
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-trading-muted">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No patterns detected</p>
              <p className="text-xs mt-1">Patterns will appear with sufficient order flow data</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
