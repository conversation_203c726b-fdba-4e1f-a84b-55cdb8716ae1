
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, Plus, Eye, TrendingUp, TrendingDown, Brain, User, Search, Settings, Trash2 } from "lucide-react";
import { watchlistService, Watchlist, WatchlistStock, AIWatchlistCriteria } from "@/services/WatchlistService";

export const WatchlistDashboard = () => {
  const [watchlists, setWatchlists] = useState<Watchlist[]>([]);
  const [selectedWatchlist, setSelectedWatchlist] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [newWatchlistName, setNewWatchlistName] = useState('');
  const [newWatchlistDescription, setNewWatchlistDescription] = useState('');
  const [newStockSymbol, setNewStockSymbol] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isAIDialogOpen, setIsAIDialogOpen] = useState(false);
  const [aiCriteria, setAiCriteria] = useState<AIWatchlistCriteria>({
    strategy: 'SWING',
    sectors: []
  });

  useEffect(() => {
    loadWatchlists();
  }, []);

  const loadWatchlists = () => {
    const allWatchlists = watchlistService.getAllWatchlists();
    setWatchlists(allWatchlists);
    if (allWatchlists.length > 0 && !selectedWatchlist) {
      setSelectedWatchlist(allWatchlists[0].id);
    }
  };

  const createUserWatchlist = () => {
    if (!newWatchlistName.trim()) return;
    
    watchlistService.createUserWatchlist(newWatchlistName, newWatchlistDescription);
    setNewWatchlistName('');
    setNewWatchlistDescription('');
    setIsCreateDialogOpen(false);
    loadWatchlists();
  };

  const generateAIWatchlist = () => {
    watchlistService.generateAIWatchlist(aiCriteria);
    setIsAIDialogOpen(false);
    loadWatchlists();
  };

  const addStockToWatchlist = (watchlistId: string) => {
    if (!newStockSymbol.trim()) return;

    const mockStock: Omit<WatchlistStock, 'lastUpdated'> = {
      symbol: newStockSymbol.toUpperCase(),
      name: `${newStockSymbol.toUpperCase()} Limited`,
      price: 1000 + Math.random() * 2000,
      change: (Math.random() - 0.5) * 100,
      changePercent: (Math.random() - 0.5) * 5,
      volume: Math.floor(Math.random() * 5000000)
    };

    if (watchlistService.addStockToWatchlist(watchlistId, mockStock)) {
      setNewStockSymbol('');
      loadWatchlists();
    }
  };

  const removeStock = (watchlistId: string, symbol: string) => {
    watchlistService.removeStockFromWatchlist(watchlistId, symbol);
    loadWatchlists();
  };

  const deleteWatchlist = (id: string) => {
    if (watchlistService.deleteWatchlist(id)) {
      loadWatchlists();
      if (selectedWatchlist === id) {
        setSelectedWatchlist(watchlists[0]?.id || '');
      }
    }
  };

  const filteredWatchlists = watchlists.filter(w => 
    w.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    w.stocks.some(s => s.symbol.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const aiWatchlists = filteredWatchlists.filter(w => w.type === 'AI');
  const userWatchlists = filteredWatchlists.filter(w => w.type === 'USER');

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const isPositive = change >= 0;
    const color = isPositive ? 'text-green-400' : 'text-red-400';
    const icon = isPositive ? TrendingUp : TrendingDown;
    const Icon = icon;

    return (
      <div className={`flex items-center ${color} text-sm`}>
        <Icon className="h-3 w-3 mr-1" />
        <span>{isPositive ? '+' : ''}₹{Math.abs(change).toFixed(2)} ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center justify-between">
            <div className="flex items-center">
              <Star className="h-5 w-5 mr-2" />
              Smart Watchlists
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-trading-muted" />
                <Input
                  placeholder="Search watchlists or stocks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 bg-trading-dark border-trading-border"
                />
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Total Watchlists</div>
              <div className="text-2xl font-bold text-blue-400">{watchlists.length}</div>
              <div className="text-xs text-green-400">{aiWatchlists.length} AI + {userWatchlists.length} User</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Total Stocks</div>
              <div className="text-2xl font-bold text-trading-light">
                {watchlists.reduce((total, w) => total + w.stocks.length, 0)}
              </div>
              <div className="text-xs text-trading-muted">Being tracked</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">AI Confidence</div>
              <div className="text-2xl font-bold text-green-400">87.2%</div>
              <div className="text-xs text-green-400">Average</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Active Alerts</div>
              <div className="text-2xl font-bold text-yellow-400">24</div>
              <div className="text-xs text-trading-muted">Price & AI alerts</div>
            </div>
          </div>

          <div className="flex space-x-2">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  New Watchlist
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-trading-darker border-trading-border">
                <DialogHeader>
                  <DialogTitle className="text-trading-light">Create New Watchlist</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name" className="text-trading-light">Name</Label>
                    <Input
                      id="name"
                      value={newWatchlistName}
                      onChange={(e) => setNewWatchlistName(e.target.value)}
                      className="bg-trading-dark border-trading-border text-trading-light"
                      placeholder="My Watchlist"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description" className="text-trading-light">Description (Optional)</Label>
                    <Textarea
                      id="description"
                      value={newWatchlistDescription}
                      onChange={(e) => setNewWatchlistDescription(e.target.value)}
                      className="bg-trading-dark border-trading-border text-trading-light"
                      placeholder="Description of your watchlist..."
                    />
                  </div>
                  <Button onClick={createUserWatchlist} className="w-full">
                    Create Watchlist
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={isAIDialogOpen} onOpenChange={setIsAIDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Brain className="h-4 w-4 mr-2" />
                  AI Generate
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-trading-darker border-trading-border">
                <DialogHeader>
                  <DialogTitle className="text-trading-light">Generate AI Watchlist</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label className="text-trading-light">Strategy</Label>
                    <Select value={aiCriteria.strategy} onValueChange={(value: any) => setAiCriteria({...aiCriteria, strategy: value})}>
                      <SelectTrigger className="bg-trading-dark border-trading-border text-trading-light">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-trading-dark border-trading-border">
                        <SelectItem value="SWING">Swing Trading</SelectItem>
                        <SelectItem value="INTRADAY">Intraday</SelectItem>
                        <SelectItem value="MULTIBAGGER">Multibagger</SelectItem>
                        <SelectItem value="OPTIONS">Options</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={generateAIWatchlist} className="w-full">
                    <Brain className="h-4 w-4 mr-2" />
                    Generate Watchlist
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Watchlist Tabs */}
      <Tabs defaultValue="ai" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 bg-trading-darker">
          <TabsTrigger value="ai" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            AI Watchlists ({aiWatchlists.length})
          </TabsTrigger>
          <TabsTrigger value="user" className="data-[state=active]:bg-trading-accent">
            <User className="h-4 w-4 mr-2" />
            My Watchlists ({userWatchlists.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="ai">
          <div className="grid gap-4">
            {aiWatchlists.map((watchlist) => (
              <Card key={watchlist.id} className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <CardTitle className="text-trading-light flex items-center justify-between">
                    <div className="flex items-center">
                      <Brain className="h-5 w-5 mr-2 text-purple-400" />
                      {watchlist.name}
                      <Badge variant="outline" className="ml-2 text-purple-400 border-purple-400">
                        AI Generated
                      </Badge>
                    </div>
                    <div className="text-sm text-trading-muted">
                      {watchlist.stocks.length} stocks
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-trading-muted mb-4">{watchlist.description}</p>
                  <div className="space-y-3">
                    {watchlist.stocks.map((stock) => (
                      <div key={stock.symbol} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center space-x-4">
                          <div>
                            <div className="font-medium text-trading-light">{stock.symbol}</div>
                            <div className="text-xs text-trading-muted">{stock.sector}</div>
                          </div>
                          <div className="text-trading-light">{formatPrice(stock.price)}</div>
                          {formatChange(stock.change, stock.changePercent)}
                          {stock.aiConfidence && (
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className={
                                stock.aiSignal === 'BUY' ? 'text-green-400 border-green-400' :
                                stock.aiSignal === 'SELL' ? 'text-red-400 border-red-400' :
                                'text-yellow-400 border-yellow-400'
                              }>
                                {stock.aiSignal}
                              </Badge>
                              <span className="text-xs text-blue-400">{stock.aiConfidence.toFixed(0)}%</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Analyze
                          </Button>
                          <Button size="sm">Trade</Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="user">
          <div className="grid gap-4">
            {userWatchlists.map((watchlist) => (
              <Card key={watchlist.id} className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <CardTitle className="text-trading-light flex items-center justify-between">
                    <div className="flex items-center">
                      <User className="h-5 w-5 mr-2 text-blue-400" />
                      {watchlist.name}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-trading-muted">{watchlist.stocks.length} stocks</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteWatchlist(watchlist.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-400" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-trading-muted mb-4">{watchlist.description}</p>
                  
                  {/* Add Stock */}
                  <div className="flex space-x-2 mb-4">
                    <Input
                      placeholder="Add stock symbol (e.g., RELIANCE)"
                      value={newStockSymbol}
                      onChange={(e) => setNewStockSymbol(e.target.value.toUpperCase())}
                      className="bg-trading-dark border-trading-border text-trading-light"
                    />
                    <Button onClick={() => addStockToWatchlist(watchlist.id)}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {watchlist.stocks.map((stock) => (
                      <div key={stock.symbol} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center space-x-4">
                          <div>
                            <div className="font-medium text-trading-light">{stock.symbol}</div>
                            <div className="text-xs text-trading-muted">{stock.name}</div>
                          </div>
                          <div className="text-trading-light">{formatPrice(stock.price)}</div>
                          {formatChange(stock.change, stock.changePercent)}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeStock(watchlist.id, stock.symbol)}
                          >
                            <Trash2 className="h-4 w-4 text-red-400" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Analyze
                          </Button>
                          <Button size="sm">Trade</Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
