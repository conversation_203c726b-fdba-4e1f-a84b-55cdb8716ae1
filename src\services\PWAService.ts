
export interface PWAConfig {
  enableOffline: boolean;
  enablePushNotifications: boolean;
  enableBackgroundSync: boolean;
  cacheStrategy: 'CACHE_FIRST' | 'NETWORK_FIRST' | 'STALE_WHILE_REVALIDATE';
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
}

export class PWAService {
  private config: PWAConfig;
  private swRegistration: ServiceWorkerRegistration | null = null;
  private isInstalled = false;
  private offlineQueue: Array<() => Promise<any>> = [];

  constructor(config: PWAConfig) {
    this.config = config;
    this.initialize();
  }

  private async initialize(): Promise<void> {
    // Check if app is installed
    this.checkInstallation();

    // Register service worker
    if ('serviceWorker' in navigator) {
      await this.registerServiceWorker();
    }

    // Set up offline handling
    if (this.config.enableOffline) {
      this.setupOfflineHandling();
    }

    // Request notification permission
    if (this.config.enablePushNotifications) {
      await this.requestNotificationPermission();
    }

    // Set up background sync
    if (this.config.enableBackgroundSync) {
      this.setupBackgroundSync();
    }
  }

  private checkInstallation(): void {
    // Check if running as PWA
    this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                     (window.navigator as any).standalone ||
                     document.referrer.includes('android-app://');
  }

  private async registerServiceWorker(): Promise<void> {
    try {
      this.swRegistration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', this.swRegistration);

      // Handle updates
      this.swRegistration.addEventListener('updatefound', () => {
        const newWorker = this.swRegistration!.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateAvailable();
            }
          });
        }
      });
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  private setupOfflineHandling(): void {
    window.addEventListener('online', () => {
      console.log('Back online - processing queued requests');
      this.processOfflineQueue();
    });

    window.addEventListener('offline', () => {
      console.log('Gone offline - queueing requests');
    });
  }

  private async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  private setupBackgroundSync(): void {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.ready.then(registration => {
        // Background sync is available
        console.log('Background sync available');
      });
    }
  }

  async sendNotification(payload: NotificationPayload): Promise<void> {
    if (!await this.requestNotificationPermission()) {
      console.warn('Notification permission denied');
      return;
    }

    if (this.swRegistration) {
      // Send via service worker for better control with actions support
      await this.swRegistration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/icons/icon-192x192.png',
        badge: payload.badge || '/icons/badge-72x72.png',
        data: payload.data,
        actions: payload.actions,
        requireInteraction: true,
        vibrate: [200, 100, 200]
      } as any); // Cast to any to bypass TypeScript restriction
    } else {
      // Fallback to basic notification without actions
      new Notification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/icons/icon-192x192.png'
      });
    }
  }

  queueOfflineAction(action: () => Promise<any>): void {
    if (navigator.onLine) {
      action(); // Execute immediately if online
    } else {
      this.offlineQueue.push(action);
    }
  }

  private async processOfflineQueue(): Promise<void> {
    while (this.offlineQueue.length > 0 && navigator.onLine) {
      const action = this.offlineQueue.shift();
      if (action) {
        try {
          await action();
        } catch (error) {
          console.error('Failed to process offline action:', error);
          // Re-queue if failed
          this.offlineQueue.unshift(action);
          break;
        }
      }
    }
  }

  private showUpdateAvailable(): void {
    // Show update notification
    this.sendNotification({
      title: 'App Update Available',
      body: 'A new version of the trading platform is available. Refresh to update.',
      actions: [
        { action: 'update', title: 'Update Now' }
      ]
    });
  }

  async installApp(): Promise<boolean> {
    const deferredPrompt = (window as any).deferredPrompt;
    if (!deferredPrompt) {
      console.warn('App installation not available');
      return false;
    }

    deferredPrompt.prompt();
    const choiceResult = await deferredPrompt.userChoice;
    
    if (choiceResult.outcome === 'accepted') {
      console.log('App installed');
      this.isInstalled = true;
      return true;
    }
    
    return false;
  }

  getInstallationStatus(): {
    isInstalled: boolean;
    canInstall: boolean;
    platform: string;
  } {
    return {
      isInstalled: this.isInstalled,
      canInstall: !!(window as any).deferredPrompt,
      platform: this.detectPlatform()
    };
  }

  private detectPlatform(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/android/.test(userAgent)) return 'android';
    if (/iphone|ipad|ipod/.test(userAgent)) return 'ios';
    if (/windows/.test(userAgent)) return 'windows';
    if (/mac/.test(userAgent)) return 'macos';
    if (/linux/.test(userAgent)) return 'linux';
    
    return 'unknown';
  }

  enableVoiceCommands(): void {
    if ('speechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      
      recognition.onresult = (event: any) => {
        const command = event.results[event.results.length - 1][0].transcript.toLowerCase();
        this.processVoiceCommand(command);
      };
      
      recognition.start();
    }
  }

  private processVoiceCommand(command: string): void {
    console.log('Voice command:', command);
    
    if (command.includes('buy') || command.includes('sell')) {
      this.sendNotification({
        title: 'Voice Command Detected',
        body: `Processing: ${command}`,
        actions: [
          { action: 'confirm', title: 'Confirm' },
          { action: 'cancel', title: 'Cancel' }
        ]
      });
    }
  }

  optimizeForMobile(): void {
    // Add mobile-specific optimizations
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover'
      );
    }

    // Prevent zoom on input focus (iOS)
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      input.addEventListener('focus', () => {
        if (this.detectPlatform() === 'ios') {
          (input as HTMLElement).style.fontSize = '16px';
        }
      });
    });

    // Add touch-friendly classes
    document.body.classList.add('mobile-optimized');
  }

  getOfflineCapabilities(): {
    isOffline: boolean;
    queuedActions: number;
    lastSync: Date | null;
    cacheSize: number;
  } {
    return {
      isOffline: !navigator.onLine,
      queuedActions: this.offlineQueue.length,
      lastSync: new Date(), // Mock
      cacheSize: 0 // Would need to calculate actual cache size
    };
  }
}
