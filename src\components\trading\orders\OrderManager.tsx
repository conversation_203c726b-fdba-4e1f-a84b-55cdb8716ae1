
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ShoppingCart, Clock, CheckCircle, XCircle, BarChart3 } from "lucide-react";
import { useState } from "react";

interface Order {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  orderType: 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'BRACKET';
  quantity: number;
  price?: number;
  stopLoss?: number;
  target?: number;
  status: 'PENDING' | 'FILLED' | 'PARTIAL' | 'CANCELLED' | 'REJECTED';
  timestamp: Date;
  fillPrice?: number;
  filledQty?: number;
}

const mockOrders: Order[] = [
  {
    id: '1',
    symbol: 'RELIANCE',
    type: 'BUY',
    orderType: 'LIMIT',
    quantity: 100,
    price: 2450,
    status: 'PENDING',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: '2',
    symbol: 'TCS',
    type: 'SELL',
    orderType: 'MARKET',
    quantity: 50,
    status: 'FILLED',
    fillPrice: 3850,
    filledQty: 50,
    timestamp: new Date(Date.now() - 600000)
  }
];

export const OrderManager = () => {
  const [orders, setOrders] = useState<Order[]>(mockOrders);
  const [newOrder, setNewOrder] = useState({
    symbol: '',
    type: 'BUY' as 'BUY' | 'SELL',
    orderType: 'MARKET' as 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'BRACKET',
    quantity: 0,
    price: 0,
    stopLoss: 0,
    target: 0
  });

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'FILLED': return 'text-green-400 border-green-400';
      case 'PENDING': return 'text-yellow-400 border-yellow-400';
      case 'PARTIAL': return 'text-blue-400 border-blue-400';
      case 'CANCELLED': return 'text-gray-400 border-gray-400';
      case 'REJECTED': return 'text-red-400 border-red-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'FILLED': return <CheckCircle className="h-4 w-4" />;
      case 'PENDING': return <Clock className="h-4 w-4" />;
      case 'CANCELLED':
      case 'REJECTED': return <XCircle className="h-4 w-4" />;
      default: return <BarChart3 className="h-4 w-4" />;
    }
  };

  const placeOrder = () => {
    const order: Order = {
      id: Date.now().toString(),
      ...newOrder,
      status: 'PENDING',
      timestamp: new Date()
    };
    
    setOrders(prev => [order, ...prev]);
    setNewOrder({
      symbol: '',
      type: 'BUY',
      orderType: 'MARKET',
      quantity: 0,
      price: 0,
      stopLoss: 0,
      target: 0
    });
  };

  const cancelOrder = (orderId: string) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: 'CANCELLED' as const }
        : order
    ));
  };

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Order Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="place-order" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
              <TabsTrigger value="place-order">Place Order</TabsTrigger>
              <TabsTrigger value="active-orders">Active Orders</TabsTrigger>
              <TabsTrigger value="order-history">Order History</TabsTrigger>
            </TabsList>

            <TabsContent value="place-order" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-trading-muted">Symbol</label>
                    <Input
                      placeholder="Enter symbol (e.g., RELIANCE)"
                      value={newOrder.symbol}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, symbol: e.target.value }))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-sm text-trading-muted">Type</label>
                      <Select value={newOrder.type} onValueChange={(value: 'BUY' | 'SELL') => setNewOrder(prev => ({ ...prev, type: value }))}>
                        <SelectTrigger className="bg-trading-dark border-trading-border">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BUY">BUY</SelectItem>
                          <SelectItem value="SELL">SELL</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <label className="text-sm text-trading-muted">Order Type</label>
                      <Select value={newOrder.orderType} onValueChange={(value: any) => setNewOrder(prev => ({ ...prev, orderType: value }))}>
                        <SelectTrigger className="bg-trading-dark border-trading-border">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MARKET">Market</SelectItem>
                          <SelectItem value="LIMIT">Limit</SelectItem>
                          <SelectItem value="STOP_LOSS">Stop Loss</SelectItem>
                          <SelectItem value="BRACKET">Bracket</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm text-trading-muted">Quantity</label>
                    <Input
                      type="number"
                      placeholder="Enter quantity"
                      value={newOrder.quantity || ''}
                      onChange={(e) => setNewOrder(prev => ({ ...prev, quantity: parseInt(e.target.value) || 0 }))}
                      className="bg-trading-dark border-trading-border"
                    />
                  </div>

                  {newOrder.orderType !== 'MARKET' && (
                    <div>
                      <label className="text-sm text-trading-muted">Price</label>
                      <Input
                        type="number"
                        placeholder="Enter price"
                        value={newOrder.price || ''}
                        onChange={(e) => setNewOrder(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                        className="bg-trading-dark border-trading-border"
                      />
                    </div>
                  )}

                  {(newOrder.orderType === 'BRACKET' || newOrder.orderType === 'STOP_LOSS') && (
                    <>
                      <div>
                        <label className="text-sm text-trading-muted">Stop Loss</label>
                        <Input
                          type="number"
                          placeholder="Enter stop loss"
                          value={newOrder.stopLoss || ''}
                          onChange={(e) => setNewOrder(prev => ({ ...prev, stopLoss: parseFloat(e.target.value) || 0 }))}
                          className="bg-trading-dark border-trading-border"
                        />
                      </div>
                      
                      {newOrder.orderType === 'BRACKET' && (
                        <div>
                          <label className="text-sm text-trading-muted">Target</label>
                          <Input
                            type="number"
                            placeholder="Enter target"
                            value={newOrder.target || ''}
                            onChange={(e) => setNewOrder(prev => ({ ...prev, target: parseFloat(e.target.value) || 0 }))}
                            className="bg-trading-dark border-trading-border"
                          />
                        </div>
                      )}
                    </>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-trading-dark rounded border border-trading-border">
                    <h4 className="text-sm font-medium text-trading-light mb-2">Order Summary</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Symbol:</span>
                        <span className="text-trading-light">{newOrder.symbol || '-'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Action:</span>
                        <span className={newOrder.type === 'BUY' ? 'text-green-400' : 'text-red-400'}>
                          {newOrder.type}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Quantity:</span>
                        <span className="text-trading-light">{newOrder.quantity || 0}</span>
                      </div>
                      {newOrder.orderType !== 'MARKET' && (
                        <div className="flex justify-between">
                          <span className="text-trading-muted">Price:</span>
                          <span className="text-trading-light">₹{newOrder.price || 0}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <Button 
                    onClick={placeOrder}
                    className={`w-full ${newOrder.type === 'BUY' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}`}
                    disabled={!newOrder.symbol || !newOrder.quantity}
                  >
                    Place {newOrder.type} Order
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="active-orders">
              <div className="space-y-3">
                {orders.filter(order => order.status === 'PENDING' || order.status === 'PARTIAL').map((order) => (
                  <div key={order.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Badge variant="outline" className={getStatusColor(order.status)}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">{order.status}</span>
                        </Badge>
                        <span className="font-medium text-trading-light">{order.symbol}</span>
                        <Badge variant="outline" className={order.type === 'BUY' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                          {order.type}
                        </Badge>
                        <span className="text-sm text-trading-muted">{order.orderType}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-trading-light">Qty: {order.quantity}</span>
                        {order.price && <span className="text-sm text-trading-light">@ ₹{order.price}</span>}
                        <Button size="sm" variant="destructive" onClick={() => cancelOrder(order.id)}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="order-history">
              <div className="space-y-3">
                {orders.map((order) => (
                  <div key={order.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Badge variant="outline" className={getStatusColor(order.status)}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">{order.status}</span>
                        </Badge>
                        <span className="font-medium text-trading-light">{order.symbol}</span>
                        <Badge variant="outline" className={order.type === 'BUY' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                          {order.type}
                        </Badge>
                        <span className="text-sm text-trading-muted">{order.orderType}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-trading-light">
                          {order.filledQty || order.quantity} @ ₹{order.fillPrice || order.price || 'Market'}
                        </div>
                        <div className="text-xs text-trading-muted">
                          {order.timestamp.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
