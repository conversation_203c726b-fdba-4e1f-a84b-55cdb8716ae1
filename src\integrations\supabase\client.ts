// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dxuqrmbzvxamfczvloou.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4dXFybWJ6dnhhbWZjenZsb291Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxMDM0NjgsImV4cCI6MjA2MzY3OTQ2OH0.Lw9GgvT7bvpq_Qg5TbKb-Hlmi56arO7S44tp6c9BXt4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);