
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { MessageCircle, Send, Mic, X } from "lucide-react";

export const ConversationalAI = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    { type: "ai", content: "Hi! I'm your AI trading assistant. How can I help you today?" },
    { type: "user", content: "What's the market sentiment for NIFTY?" },
    { type: "ai", content: "NIFTY is showing bullish sentiment with 68% positive indicators. Smart money flow is +₹45.2Cr. Current levels suggest continuation above 24,150." },
  ]);

  const handleSendMessage = () => {
    if (message.trim()) {
      setMessages([...messages, { type: "user", content: message }]);
      setMessage("");
      
      // Simulate AI response
      setTimeout(() => {
        setMessages(prev => [...prev, { 
          type: "ai", 
          content: "I'm analyzing your request. Based on current market data and AI models, here's my recommendation..." 
        }]);
      }, 1000);
    }
  };

  if (!isOpen) {
    return (
      <Button 
        onClick={() => setIsOpen(true)}
        className="rounded-full w-14 h-14 bg-blue-600 hover:bg-blue-700"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>
    );
  }

  return (
    <Card className="w-80 h-96 bg-trading-darker border-trading-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <MessageCircle className="h-5 w-5 mr-2" />
            AI Assistant
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-green-400 border-green-400">
              Online
            </Badge>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-3">
        <div className="h-56 overflow-y-auto space-y-3 mb-3">
          {messages.map((msg, index) => (
            <div key={index} className={`flex ${msg.type === "user" ? "justify-end" : "justify-start"}`}>
              <div className={`max-w-xs p-2 rounded-lg text-xs ${
                msg.type === "user" 
                  ? "bg-blue-600 text-white" 
                  : "bg-trading-dark text-trading-light border border-trading-border"
              }`}>
                {msg.content}
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex space-x-2">
          <Input
            placeholder="Ask about markets, trades, or strategies..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            className="text-xs bg-trading-dark border-trading-border"
          />
          <Button size="sm" onClick={handleSendMessage}>
            <Send className="h-3 w-3" />
          </Button>
          <Button variant="outline" size="sm">
            <Mic className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="mt-2 flex flex-wrap gap-1">
          {["Market Summary", "Risk Check", "Best Trades", "AI Signals"].map((quick) => (
            <Button
              key={quick}
              variant="outline"
              size="sm"
              className="text-xs h-6"
              onClick={() => setMessage(quick)}
            >
              {quick}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
