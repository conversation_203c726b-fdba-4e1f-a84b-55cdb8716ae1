
import { RealDataPipelineService } from './RealDataPipelineService';
import { EnhancedDatabaseService } from './EnhancedDatabaseService';
import { RealMathService } from './RealMathService';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Strategy {
  id: string;
  name: string;
  type: 'intraday' | 'swing' | 'scalping';
  indicators: string[];
  stopLoss: number;
  takeProfit: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface StrategySignal {
  strategyId: string;
  symbol: string;
  action: 'BUY' | 'SELL';
  price: number;
  confidence: number;
  timestamp: string;
  reason: string;
  stopLoss: number;
  takeProfit: number;
}

export interface StrategyExecution {
  id: string;
  strategyId: string;
  symbol: string;
  status: 'ACTIVE' | 'PAUSED' | 'STOPPED' | 'ERROR';
  lastSignal?: StrategySignal;
  performance: {
    totalSignals: number;
    successfulSignals: number;
    winRate: number;
    totalPnL: number;
    lastUpdated: string;
  };
  configuration: {
    riskLevel: number;
    maxPositionSize: number;
    stopLoss: number;
    takeProfit: number;
  };
}

export interface RealTimeStrategyUpdate {
  strategyId: string;
  signal: StrategySignal;
  performance: any;
  timestamp: number;
}

export class StrategyExecutionEngine {
  private dataService: RealDataPipelineService;
  private databaseService: EnhancedDatabaseService;
  private executions: Map<string, StrategyExecution> = new Map();
  private updateSubject = new BehaviorSubject<RealTimeStrategyUpdate[]>([]);
  private isRunning = false;
  private executionIntervals: Map<string, NodeJS.Timeout> = new Map();
  private strategies: Map<string, Strategy> = new Map();

  constructor() {
    this.databaseService = new EnhancedDatabaseService({
      host: 'localhost',
      port: 5432,
      database: 'trading',
      username: 'admin',
      password: 'password'
    });
    this.dataService = new RealDataPipelineService(
      {
        marketDataProvider: 'polygon',
        brokerConnection: 'zerodha',
        updateInterval: 5000,
        cacheEnabled: true,
        validationEnabled: true
      },
      {
        host: 'localhost',
        port: 5432,
        database: 'trading',
        username: 'admin',
        password: 'password'
      }
    );
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    const defaultStrategies: Strategy[] = [
      {
        id: 'momentum-ema',
        name: 'EMA Momentum',
        type: 'intraday',
        indicators: ['EMA_9', 'EMA_21', 'RSI', 'VOLUME'],
        stopLoss: 2,
        takeProfit: 4,
        riskLevel: 'MEDIUM'
      },
      {
        id: 'swing-rsi',
        name: 'RSI Swing',
        type: 'swing',
        indicators: ['RSI', 'MACD', 'VWAP'],
        stopLoss: 3,
        takeProfit: 6,
        riskLevel: 'LOW'
      }
    ];

    defaultStrategies.forEach(strategy => {
      this.strategies.set(strategy.id, strategy);
    });
  }

  async initialize(): Promise<void> {
    await this.databaseService.connect();
    await this.dataService.initialize();
    await this.loadExecutionStates();
    console.log('StrategyExecutionEngine initialized');
  }

  private async loadExecutionStates(): Promise<void> {
    try {
      const executions = await this.databaseService.query(
        'SELECT * FROM strategy_executions WHERE status = ?',
        ['ACTIVE']
      );
      
      executions.forEach((exec: any) => {
        this.executions.set(exec.id, {
          id: exec.id,
          strategyId: exec.strategy_id,
          symbol: exec.symbol,
          status: exec.status,
          performance: JSON.parse(exec.performance || '{}'),
          configuration: JSON.parse(exec.configuration || '{}')
        });
      });
    } catch (error) {
      console.error('Error loading execution states:', error);
    }
  }

  async activateStrategy(strategyId: string, symbols: string[], config?: any): Promise<string> {
    const strategy = this.strategies.get(strategyId);
    if (!strategy) {
      throw new Error(`Strategy ${strategyId} not found`);
    }

    const executionId = `exec_${strategyId}_${Date.now()}`;
    
    for (const symbol of symbols) {
      const execution: StrategyExecution = {
        id: `${executionId}_${symbol}`,
        strategyId,
        symbol,
        status: 'ACTIVE',
        performance: {
          totalSignals: 0,
          successfulSignals: 0,
          winRate: 0,
          totalPnL: 0,
          lastUpdated: new Date().toISOString()
        },
        configuration: {
          riskLevel: config?.riskLevel || 1,
          maxPositionSize: config?.maxPositionSize || 10000,
          stopLoss: strategy.stopLoss,
          takeProfit: strategy.takeProfit
        }
      };

      this.executions.set(execution.id, execution);
      await this.saveExecution(execution);
      await this.startStrategyExecution(execution);
    }

    return executionId;
  }

  async deactivateStrategy(strategyId: string): Promise<void> {
    for (const [id, execution] of this.executions.entries()) {
      if (execution.strategyId === strategyId) {
        await this.stopStrategyExecution(id);
      }
    }
  }

  private async startStrategyExecution(execution: StrategyExecution): Promise<void> {
    this.dataService.subscribe(`market_${execution.symbol}`, (data) => {
      this.processStrategySignal(execution, data);
    });

    const interval = setInterval(async () => {
      await this.evaluateStrategy(execution);
    }, 30000);

    this.executionIntervals.set(execution.id, interval);
    console.log(`Started execution for strategy ${execution.strategyId} on ${execution.symbol}`);
  }

  private async stopStrategyExecution(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    if (!execution) return;

    execution.status = 'STOPPED';
    
    const interval = this.executionIntervals.get(executionId);
    if (interval) {
      clearInterval(interval);
      this.executionIntervals.delete(executionId);
    }

    this.dataService.unsubscribe(`market_${execution.symbol}`, () => {});
    await this.saveExecution(execution);
    console.log(`Stopped execution ${executionId}`);
  }

  private async evaluateStrategy(execution: StrategyExecution): Promise<void> {
    try {
      const strategy = this.strategies.get(execution.strategyId);
      if (!strategy || execution.status !== 'ACTIVE') return;

      const marketData = await this.dataService.fetchRealMarketData(execution.symbol);
      const indicators = await this.calculateIndicators(execution.symbol, strategy.indicators);
      const signal = await this.generateStrategySignal(strategy, execution.symbol, marketData, indicators);
      
      if (signal) {
        execution.performance.totalSignals++;
        execution.lastSignal = signal;
        execution.performance.lastUpdated = new Date().toISOString();

        await this.storeSignal(signal, execution.id);
        this.emitStrategyUpdate({
          strategyId: execution.strategyId,
          signal,
          performance: execution.performance,
          timestamp: Date.now()
        });

        await this.saveExecution(execution);
        console.log(`Generated signal for ${execution.strategyId}: ${signal.action} ${execution.symbol} at ${signal.price}`);
      }
    } catch (error) {
      console.error(`Error evaluating strategy ${execution.strategyId}:`, error);
      execution.status = 'ERROR';
      await this.saveExecution(execution);
    }
  }

  private async calculateIndicators(symbol: string, requiredIndicators: string[]): Promise<Record<string, number>> {
    const indicators: Record<string, number> = {};
    const historicalData = await this.dataService.fetchRealMarketData(symbol);
    const prices = [historicalData.processed.current];
    
    for (const indicator of requiredIndicators) {
      switch (indicator) {
        case 'RSI':
          indicators.RSI = historicalData.processed.rsi;
          break;
        case 'MACD':
          indicators.MACD = historicalData.processed.macd;
          break;
        case 'EMA_9':
          indicators.EMA_9 = RealMathService.calculateEMA(prices, 9)[0] || 0;
          break;
        case 'EMA_21':
          indicators.EMA_21 = RealMathService.calculateEMA(prices, 21)[0] || 0;
          break;
        case 'VOLUME':
          indicators.VOLUME = historicalData.rawData.volume || 0;
          break;
        case 'VWAP':
          indicators.VWAP = historicalData.processed.vwap;
          break;
      }
    }
    
    return indicators;
  }

  private async generateStrategySignal(
    strategy: Strategy, 
    symbol: string, 
    marketData: any, 
    indicators: Record<string, number>
  ): Promise<StrategySignal | null> {
    const signalStrength = this.evaluateSignalConditions(strategy, indicators);
    
    if (Math.abs(signalStrength) > 0.6) {
      return {
        strategyId: strategy.id,
        symbol,
        action: signalStrength > 0 ? 'BUY' : 'SELL',
        price: marketData.processed.current,
        confidence: Math.abs(signalStrength),
        timestamp: new Date().toISOString(),
        reason: this.generateSignalReason(strategy, indicators),
        stopLoss: signalStrength > 0 
          ? marketData.processed.current * (1 - strategy.stopLoss / 100)
          : marketData.processed.current * (1 + strategy.stopLoss / 100),
        takeProfit: signalStrength > 0 
          ? marketData.processed.current * (1 + strategy.takeProfit / 100)
          : marketData.processed.current * (1 - strategy.takeProfit / 100)
      };
    }
    
    return null;
  }

  private evaluateSignalConditions(strategy: Strategy, indicators: Record<string, number>): number {
    let signal = 0;
    
    switch (strategy.type) {
      case 'intraday':
        if (indicators.EMA_9 && indicators.EMA_21) {
          signal += indicators.EMA_9 > indicators.EMA_21 ? 0.3 : -0.3;
        }
        if (indicators.RSI) {
          if (indicators.RSI > 50 && indicators.RSI < 70) signal += 0.2;
          if (indicators.RSI < 50 && indicators.RSI > 30) signal -= 0.2;
        }
        if (indicators.VOLUME && indicators.VOLUME > 1000000) {
          signal += 0.3;
        }
        break;
        
      case 'swing':
        if (indicators.RSI) {
          signal += indicators.RSI > 60 ? 0.4 : -0.4;
        }
        if (indicators.MACD) {
          signal += indicators.MACD > 0 ? 0.3 : -0.3;
        }
        break;
    }
    
    return Math.max(-1, Math.min(1, signal));
  }

  private generateSignalReason(strategy: Strategy, indicators: Record<string, number>): string {
    const conditions = [];
    
    if (indicators.EMA_9 && indicators.EMA_21) {
      conditions.push(`EMA 9/21: ${indicators.EMA_9 > indicators.EMA_21 ? 'Bullish' : 'Bearish'}`);
    }
    if (indicators.RSI) {
      conditions.push(`RSI: ${indicators.RSI.toFixed(2)}`);
    }
    if (indicators.VOLUME) {
      conditions.push(`Volume: ${indicators.VOLUME.toLocaleString()}`);
    }
    
    return conditions.join(', ') || `${strategy.name} conditions met`;
  }

  private async storeSignal(signal: StrategySignal, executionId: string): Promise<void> {
    try {
      await this.databaseService.executeQuery(
        'INSERT INTO strategy_signals (execution_id, strategy_id, symbol, action, price, confidence, reason, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [executionId, signal.strategyId, signal.symbol, signal.action, signal.price, signal.confidence, signal.reason, signal.timestamp]
      );
    } catch (error) {
      console.error('Error storing signal:', error);
    }
  }

  private async saveExecution(execution: StrategyExecution): Promise<void> {
    try {
      await this.databaseService.executeQuery(
        'INSERT OR REPLACE INTO strategy_executions (id, strategy_id, symbol, status, performance, configuration) VALUES (?, ?, ?, ?, ?, ?)',
        [
          execution.id,
          execution.strategyId,
          execution.symbol,
          execution.status,
          JSON.stringify(execution.performance),
          JSON.stringify(execution.configuration)
        ]
      );
    } catch (error) {
      console.error('Error saving execution:', error);
    }
  }

  private processStrategySignal(execution: StrategyExecution, marketData: any): void {
    console.log(`Processing real-time data for ${execution.strategyId} on ${execution.symbol}`);
  }

  private emitStrategyUpdate(update: RealTimeStrategyUpdate): void {
    const currentUpdates = this.updateSubject.value;
    this.updateSubject.next([...currentUpdates, update]);
  }

  // Public API methods
  getActiveExecutions(): StrategyExecution[] {
    return Array.from(this.executions.values()).filter(e => e.status === 'ACTIVE');
  }

  getExecution(executionId: string): StrategyExecution | undefined {
    return this.executions.get(executionId);
  }

  getExecutionsForStrategy(strategyId: string): StrategyExecution[] {
    return Array.from(this.executions.values()).filter(e => e.strategyId === strategyId);
  }

  getUpdatesStream(): Observable<RealTimeStrategyUpdate[]> {
    return this.updateSubject.asObservable();
  }

  getStrategies(): Strategy[] {
    return Array.from(this.strategies.values());
  }

  async getStrategyPerformance(strategyId: string): Promise<any> {
    const executions = this.getExecutionsForStrategy(strategyId);
    
    const totalSignals = executions.reduce((sum, exec) => sum + exec.performance.totalSignals, 0);
    const totalSuccessful = executions.reduce((sum, exec) => sum + exec.performance.successfulSignals, 0);
    const totalPnL = executions.reduce((sum, exec) => sum + exec.performance.totalPnL, 0);
    
    return {
      totalSignals,
      winRate: totalSignals > 0 ? (totalSuccessful / totalSignals) * 100 : 0,
      totalPnL,
      activeExecutions: executions.filter(e => e.status === 'ACTIVE').length,
      lastUpdated: new Date().toISOString()
    };
  }

  async shutdown(): Promise<void> {
    this.isRunning = false;
    
    for (const [id, execution] of this.executions.entries()) {
      if (execution.status === 'ACTIVE') {
        await this.stopStrategyExecution(id);
      }
    }
    
    await this.dataService.shutdown();
    await this.databaseService.disconnect();
    console.log('StrategyExecutionEngine shutdown complete');
  }
}

export const strategyExecutionEngine = new StrategyExecutionEngine();
