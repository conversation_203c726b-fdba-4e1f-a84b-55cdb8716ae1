
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { RealDataIntegrationService, RealMarketDataConfig } from "../../../services/RealDataIntegrationService";
import { Database, TrendingUp, AlertCircle, CheckCircle } from "lucide-react";

interface RealDataConfigurationProps {
  onServiceChange: (service: RealDataIntegrationService | null) => void;
}

export const RealDataConfiguration: React.FC<RealDataConfigurationProps> = ({ 
  onServiceChange 
}) => {
  const [config, setConfig] = useState<RealMarketDataConfig>({
    provider: 'mock',
    alphaVantageApiKey: '',
    polygonApiKey: '',
    finnhubApiKey: ''
  });
  
  const [service, setService] = useState<RealDataIntegrationService | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [testResults, setTestResults] = useState<any>(null);
  const { toast } = useToast();

  const handleConfigChange = (field: keyof RealMarketDataConfig, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const connectToRealData = async () => {
    setIsConnecting(true);
    setConnectionStatus('connecting');

    try {
      const newService = new RealDataIntegrationService(config);
      
      // Test the connection by fetching sample data
      const testData = await newService.fetchRealMarketData('AAPL', '1d');
      
      if (testData.length > 0) {
        setService(newService);
        setConnectionStatus('connected');
        onServiceChange(newService);
        
        // Run a quick backtest to show capabilities
        const backtest = await newService.performBacktest('AAPL', 'swing', '1mo');
        setTestResults(backtest);
        
        toast({
          title: "Real Data Connected",
          description: `Successfully connected to ${config.provider} with ${testData.length} data points`,
        });
      } else {
        throw new Error('No data received from provider');
      }

    } catch (error) {
      console.error('Real data connection failed:', error);
      setConnectionStatus('error');
      
      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectFromRealData = () => {
    setService(null);
    setConnectionStatus('disconnected');
    setTestResults(null);
    onServiceChange(null);
    
    toast({
      title: "Real Data Disconnected",
      description: "Switched back to mock data",
    });
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'connecting':
        return <Database className="h-4 w-4 text-yellow-400 animate-pulse" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return <Database className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            {getStatusIcon()}
            <span className="ml-2">Real Data Integration</span>
            <Badge variant="outline" className="ml-auto text-xs">
              <div className={`w-2 h-2 rounded-full mr-2 ${getStatusColor()}`}></div>
              {connectionStatus.toUpperCase()}
            </Badge>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="provider" className="text-trading-light">Data Provider</Label>
            <Select 
              value={config.provider} 
              onValueChange={(value: 'alphavantage' | 'polygon' | 'finnhub' | 'mock') => handleConfigChange('provider', value)}
              disabled={connectionStatus === 'connected'}
            >
              <SelectTrigger className="bg-trading-dark border-trading-border text-trading-light">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mock">Mock Data (Free)</SelectItem>
                <SelectItem value="alphavantage">Alpha Vantage</SelectItem>
                <SelectItem value="polygon">Polygon.io</SelectItem>
                <SelectItem value="finnhub">Finnhub</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {config.provider === 'alphavantage' && (
            <div>
              <Label htmlFor="alphaVantageKey" className="text-trading-light">Alpha Vantage API Key</Label>
              <Input
                id="alphaVantageKey"
                type="password"
                value={config.alphaVantageApiKey || ''}
                onChange={(e) => handleConfigChange('alphaVantageApiKey', e.target.value)}
                placeholder="Enter your Alpha Vantage API key"
                className="bg-trading-dark border-trading-border text-trading-light"
                disabled={connectionStatus === 'connected'}
              />
              <p className="text-xs text-trading-muted mt-1">
                Get free API key from <a href="https://www.alphavantage.co/support/#api-key" target="_blank" rel="noopener noreferrer" className="text-blue-400">Alpha Vantage</a>
              </p>
            </div>
          )}

          {config.provider === 'polygon' && (
            <div>
              <Label htmlFor="polygonKey" className="text-trading-light">Polygon.io API Key</Label>
              <Input
                id="polygonKey"
                type="password"
                value={config.polygonApiKey || ''}
                onChange={(e) => handleConfigChange('polygonApiKey', e.target.value)}
                placeholder="Enter your Polygon.io API key"
                className="bg-trading-dark border-trading-border text-trading-light"
                disabled={connectionStatus === 'connected'}
              />
              <p className="text-xs text-trading-muted mt-1">
                Get API key from <a href="https://polygon.io/" target="_blank" rel="noopener noreferrer" className="text-blue-400">Polygon.io</a>
              </p>
            </div>
          )}

          {config.provider === 'finnhub' && (
            <div>
              <Label htmlFor="finnhubKey" className="text-trading-light">Finnhub API Key</Label>
              <Input
                id="finnhubKey"
                type="password"
                value={config.finnhubApiKey || ''}
                onChange={(e) => handleConfigChange('finnhubApiKey', e.target.value)}
                placeholder="Enter your Finnhub API key"
                className="bg-trading-dark border-trading-border text-trading-light"
                disabled={connectionStatus === 'connected'}
              />
              <p className="text-xs text-trading-muted mt-1">
                Get free API key from <a href="https://finnhub.io/" target="_blank" rel="noopener noreferrer" className="text-blue-400">Finnhub</a>
              </p>
            </div>
          )}

          <div className="flex space-x-2">
            {connectionStatus === 'connected' ? (
              <Button 
                onClick={disconnectFromRealData}
                variant="destructive"
                className="flex-1"
              >
                Disconnect
              </Button>
            ) : (
              <Button 
                onClick={connectToRealData}
                disabled={isConnecting}
                className="flex-1"
              >
                {isConnecting ? 'Connecting...' : 'Connect to Real Data'}
              </Button>
            )}
          </div>

          {config.provider === 'mock' && (
            <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded">
              <p className="text-xs text-blue-400">
                <strong>Mock Mode:</strong> Using realistic calculated data for demonstration. 
                Connect to a real data provider for live market data and accurate backtesting.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {testResults && connectionStatus === 'connected' && (
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Real Data Test Results
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center p-3 bg-trading-dark rounded">
                <div className="text-trading-light font-medium">
                  {testResults.totalTrades}
                </div>
                <div className="text-trading-muted">Total Trades</div>
              </div>
              <div className="text-center p-3 bg-trading-dark rounded">
                <div className="text-trading-light font-medium">
                  {(testResults.winRate * 100).toFixed(1)}%
                </div>
                <div className="text-trading-muted">Win Rate</div>
              </div>
              <div className="text-center p-3 bg-trading-dark rounded">
                <div className="text-trading-light font-medium">
                  {testResults.totalReturn.toFixed(2)}%
                </div>
                <div className="text-trading-muted">Total Return</div>
              </div>
              <div className="text-center p-3 bg-trading-dark rounded">
                <div className="text-trading-light font-medium">
                  {testResults.sharpeRatio.toFixed(2)}
                </div>
                <div className="text-trading-muted">Sharpe Ratio</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
