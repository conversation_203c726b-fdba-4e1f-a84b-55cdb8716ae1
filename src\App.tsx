
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { EnhancedThemeProvider } from "@/components/ui/enhanced-theme-provider";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { MarketDataProvider } from "@/components/trading/data/MarketDataProvider";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import "./styles/glassmorphism.css";
import "./styles/animations.css";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 400-499 errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <EnhancedThemeProvider defaultTheme="dark" enableTransitions>
        <MarketDataProvider>
          <TooltipProvider>
            <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 transition-all duration-300">
              <Toaster />
              <Sonner 
                position="top-right"
                richColors
                theme="dark"
                className="glass-card"
              />
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </div>
          </TooltipProvider>
        </MarketDataProvider>
      </EnhancedThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
