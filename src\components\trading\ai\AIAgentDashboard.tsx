
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Brain, Crown, Users, Target, BarChart3, TrendingUp, Zap, DollarSign } from "lucide-react";
import { CommanderAgent } from "./agents/CommanderAgent";
import { ScalpingAgent } from "./agents/ScalpingAgent";
import { SwingAgent } from "./agents/SwingAgent";
import { OptionsAgent } from "./agents/OptionsAgent";
import { RiskAgent } from "./agents/RiskAgent";
import { AnalysisAgent } from "./agents/AnalysisAgent";
import { IntradayAgent } from "./agents/IntradayAgent";
import { MultiBagAgent } from "./agents/MultiBagAgent";
import { MarketIntelAgent } from "./agents/MarketIntelAgent";
import { StrategyAgentsDashboard } from "./StrategyAgentsDashboard";
import { StrategyMLDashboard } from "../ml/StrategyMLDashboard";
import { PerformanceAnalytics } from "../analytics/PerformanceAnalytics";

interface AISystemStats {
  totalAgents: number;
  avgConfidence: number;
  totalTrades: number;
  totalPnL: number;
  activeModels: number;
}

interface AIAgentDashboardProps {
  systemStats?: AISystemStats;
}

export const AIAgentDashboard = ({ systemStats }: AIAgentDashboardProps) => {
  const stats = systemStats || {
    totalAgents: 0,
    avgConfidence: 0,
    totalTrades: 0,
    totalPnL: 0,
    activeModels: 0
  };

  return (
    <div className="space-y-6">
      {/* AI Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            Advanced AI Trading System
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{stats.totalAgents}</div>
              <div className="text-sm text-trading-muted">Total AI Agents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{stats.avgConfidence}%</div>
              <div className="text-sm text-trading-muted">Avg Confidence</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-trading-light">{stats.totalTrades.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">Total Trades</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">₹{stats.totalPnL.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">AI Generated P&L</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{stats.activeModels}</div>
              <div className="text-sm text-trading-muted">ML Models Active</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="commander" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6 bg-trading-darker">
          <TabsTrigger value="commander" className="data-[state=active]:bg-trading-accent">
            <Crown className="h-4 w-4 mr-2" />
            Commander
          </TabsTrigger>
          <TabsTrigger value="core-agents" className="data-[state=active]:bg-trading-accent">
            <Users className="h-4 w-4 mr-2" />
            Core Agents
          </TabsTrigger>
          <TabsTrigger value="strategy-agents" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Strategy Agents
          </TabsTrigger>
          <TabsTrigger value="ml-models" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            ML Models
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="coordination" className="data-[state=active]:bg-trading-accent">
            <BarChart3 className="h-4 w-4 mr-2" />
            Coordination
          </TabsTrigger>
        </TabsList>

        <TabsContent value="commander">
          <div className="space-y-4">
            <CommanderAgent />
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">System Coordination</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-trading-dark rounded">
                    <div className="text-lg font-bold text-green-400">Real-time</div>
                    <div className="text-sm text-trading-muted">Agent Communication</div>
                  </div>
                  <div className="text-center p-4 bg-trading-dark rounded">
                    <div className="text-lg font-bold text-blue-400">Optimized</div>
                    <div className="text-sm text-trading-muted">Resource Allocation</div>
                  </div>
                  <div className="text-center p-4 bg-trading-dark rounded">
                    <div className="text-lg font-bold text-purple-400">Advanced</div>
                    <div className="text-sm text-trading-muted">Risk Coordination</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="core-agents">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <IntradayAgent />
            <ScalpingAgent />
            <SwingAgent />
            <MultiBagAgent />
            <OptionsAgent />
            <MarketIntelAgent />
            <RiskAgent />
            <AnalysisAgent />
          </div>
        </TabsContent>

        <TabsContent value="strategy-agents">
          <StrategyAgentsDashboard />
        </TabsContent>

        <TabsContent value="ml-models">
          <StrategyMLDashboard />
        </TabsContent>

        <TabsContent value="performance">
          <PerformanceAnalytics />
        </TabsContent>

        <TabsContent value="coordination">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Agent Coordination Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-9 gap-2 text-center text-xs">
                  <div></div>
                  <div className="text-trading-muted">Commander</div>
                  <div className="text-trading-muted">Intraday</div>
                  <div className="text-trading-muted">Scalping</div>
                  <div className="text-trading-muted">Swing</div>
                  <div className="text-trading-muted">MultiBag</div>
                  <div className="text-trading-muted">Options</div>
                  <div className="text-trading-muted">MarketIntel</div>
                  <div className="text-trading-muted">Risk</div>
                </div>
                {["Commander", "Intraday", "Scalping", "Swing", "MultiBag", "Options", "MarketIntel", "Risk"].map((agent, i) => (
                  <div key={agent} className="grid grid-cols-9 gap-2 text-center text-xs">
                    <div className="text-trading-light">{agent}</div>
                    {[...Array(8)].map((_, j) => (
                      <div key={j} className={`p-2 rounded ${
                        i === j ? "bg-gray-600" : "bg-blue-900/30 text-blue-400"
                      }`}>
                        {i === j ? "—" : "○"}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
