
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TradingDashboardGrid } from "@/components/ui/responsive-grid";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { MarketDataStatus } from "./MarketDataStatus";
import { useMarketData } from "./MarketDataProvider";
import { useMarketStore } from "../../../store/useMarketStore";
import { Plus, X, TrendingUp, TrendingDown, Activity } from "lucide-react";
import { cn } from "@/lib/utils";

export const LiveDataDashboard = () => {
  const { subscribe, unsubscribe } = useMarketData();
  const marketData = useMarketStore(state => state.marketData);
  const subscribedSymbols = useMarketStore(state => state.subscribedSymbols);
  
  const [newSymbol, setNewSymbol] = useState('');
  const [searchFilter, setSearchFilter] = useState('');

  // Default symbols to subscribe on mount
  useEffect(() => {
    const defaultSymbols = ['NIFTY', 'BANKNIFTY', 'SENSEX'];
    defaultSymbols.forEach(symbol => {
      if (!subscribedSymbols.has(symbol)) {
        subscribe(symbol);
      }
    });
  }, []);

  const handleAddSymbol = () => {
    const symbol = newSymbol.trim().toUpperCase();
    if (symbol && !subscribedSymbols.has(symbol)) {
      subscribe(symbol);
      setNewSymbol('');
    }
  };

  const handleRemoveSymbol = (symbol: string) => {
    unsubscribe(symbol);
  };

  const filteredSymbols = Array.from(subscribedSymbols).filter(symbol =>
    symbol.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-IN', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  };

  const formatChange = (change: number, changePercent: number) => {
    const isPositive = change >= 0;
    const color = isPositive ? 'text-green-400' : 'text-red-400';
    const icon = isPositive ? TrendingUp : TrendingDown;
    const Icon = icon;

    return (
      <div className={cn('flex items-center space-x-1', color)}>
        <Icon className="h-3 w-3" />
        <span className="text-xs">
          {isPositive ? '+' : ''}₹{Math.abs(change).toFixed(2)} ({changePercent.toFixed(2)}%)
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-trading-light">Live Market Data</h2>
          <p className="text-trading-muted">Real-time market feed with advanced data processing</p>
        </div>
        <MarketDataStatus />
      </div>

      {/* Symbol Management */}
      <Card className="glass-trading-panel">
        <CardHeader>
          <CardTitle className="text-trading-light">Symbol Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              value={newSymbol}
              onChange={(e) => setNewSymbol(e.target.value.toUpperCase())}
              placeholder="Add symbol (e.g., RELIANCE, TCS)"
              className="bg-trading-dark border-trading-border text-trading-light"
              onKeyPress={(e) => e.key === 'Enter' && handleAddSymbol()}
            />
            <Button onClick={handleAddSymbol} className="glass-button">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <Input
            value={searchFilter}
            onChange={(e) => setSearchFilter(e.target.value)}
            placeholder="Filter symbols..."
            className="bg-trading-dark border-trading-border text-trading-light"
          />

          <div className="text-xs text-trading-muted">
            Subscribed to {subscribedSymbols.size} symbols
          </div>
        </CardContent>
      </Card>

      {/* Live Data Grid */}
      {filteredSymbols.length > 0 ? (
        <TradingDashboardGrid>
          {filteredSymbols.map(symbol => {
            const data = marketData[symbol];
            const isPositive = data?.change >= 0;
            
            return (
              <EnhancedCard 
                key={symbol}
                className="transition-all duration-300 hover:scale-105"
                status={data ? (isPositive ? 'positive' : 'negative') : 'neutral'}
                animation="slide-in"
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-bold text-trading-light">{symbol}</h3>
                      {data && (
                        <div className={cn(
                          "w-2 h-2 rounded-full animate-pulse",
                          isPositive ? "bg-green-400" : "bg-red-400"
                        )} />
                      )}
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => handleRemoveSymbol(symbol)}
                      className="h-6 w-6 text-trading-muted hover:text-red-400"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>

                  {data ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="text-xl font-bold text-trading-light">
                          ₹{formatPrice(data.price)}
                        </div>
                        <Activity className={cn(
                          "h-4 w-4",
                          isPositive ? "text-green-400" : "text-red-400"
                        )} />
                      </div>

                      {formatChange(data.change, data.changePercent)}

                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div>
                          <div className="text-trading-muted">High</div>
                          <div className="text-trading-light font-medium">₹{formatPrice(data.high)}</div>
                        </div>
                        <div>
                          <div className="text-trading-muted">Low</div>
                          <div className="text-trading-light font-medium">₹{formatPrice(data.low)}</div>
                        </div>
                        <div>
                          <div className="text-trading-muted">Volume</div>
                          <div className="text-trading-light font-medium">
                            {data.volume > 0 ? `${(data.volume / 1000000).toFixed(1)}M` : '--'}
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between text-xs text-trading-muted pt-2 border-t border-trading-border">
                        <span>Bid: ₹{formatPrice(data.bid)}</span>
                        <span>Ask: ₹{formatPrice(data.ask)}</span>
                      </div>

                      <div className="text-xs text-trading-muted text-center">
                        Updated: {new Date(data.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <div className="shimmer-loading h-4 w-24 mx-auto mb-2 rounded"></div>
                      <div className="text-xs text-trading-muted">Loading data...</div>
                    </div>
                  )}
                </CardContent>
              </EnhancedCard>
            );
          })}
        </TradingDashboardGrid>
      ) : (
        <EnhancedCard className="py-16">
          <div className="text-center">
            <Activity className="h-16 w-16 text-trading-muted mx-auto mb-4" />
            <div className="text-lg text-trading-muted mb-2">No symbols subscribed</div>
            <div className="text-sm text-trading-muted">
              Add symbols above to start receiving real-time market data
            </div>
          </div>
        </EnhancedCard>
      )}
    </div>
  );
};
