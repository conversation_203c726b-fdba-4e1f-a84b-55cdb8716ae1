
export interface PriceAnalysis {
  currentPrice: number;
  vwap: number;
  support: number;
  resistance: number;
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  volatility: number;
}

export interface PerformanceMetrics {
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  totalReturn: number;
  avgHoldingPeriod: number;
}

export interface RiskMetrics {
  valueAtRisk: number;
  expectedShortfall: number;
  beta: number;
  correlation: number;
  volatility: number;
}

export class RealMathService {
  // RSI Calculation
  static calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50;
    
    const changes = prices.slice(1).map((price, i) => price - prices[i]);
    const gains = changes.map(change => change > 0 ? change : 0);
    const losses = changes.map(change => change < 0 ? -change : 0);
    
    // Calculate initial averages
    let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;
    
    // Calculate subsequent averages using <PERSON>'s smoothing
    for (let i = period; i < gains.length; i++) {
      avgGain = (avgGain * (period - 1) + gains[i]) / period;
      avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
    }
    
    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  // MACD Calculation
  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): any {
    if (prices.length < slowPeriod) {
      return { macd: 0, signal: 0, histogram: 0 };
    }
    
    const emaFast = this.calculateEMA(prices, fastPeriod);
    const emaSlow = this.calculateEMA(prices, slowPeriod);
    
    const macdLine = emaFast[emaFast.length - 1] - emaSlow[emaSlow.length - 1];
    
    // For simplicity, using a basic signal calculation
    const signal = macdLine * 0.9; // Simplified signal line
    const histogram = macdLine - signal;
    
    return {
      macd: macdLine,
      signal: signal,
      histogram: histogram
    };
  }

  // EMA Calculation
  static calculateEMA(prices: number[], period: number): number[] {
    if (prices.length === 0) return [];
    
    const ema = [];
    const multiplier = 2 / (period + 1);
    
    // First EMA is SMA
    let sum = 0;
    for (let i = 0; i < Math.min(period, prices.length); i++) {
      sum += prices[i];
    }
    ema[period - 1] = sum / Math.min(period, prices.length);
    
    // Calculate subsequent EMAs
    for (let i = period; i < prices.length; i++) {
      ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
    }
    
    return ema.filter(val => val !== undefined);
  }

  // VWAP Calculation
  static calculateVWAP(prices: number[], volumes: number[]): number {
    if (prices.length !== volumes.length || prices.length === 0) return 0;
    
    let totalPriceVolume = 0;
    let totalVolume = 0;
    
    for (let i = 0; i < prices.length; i++) {
      totalPriceVolume += prices[i] * volumes[i];
      totalVolume += volumes[i];
    }
    
    return totalVolume > 0 ? totalPriceVolume / totalVolume : 0;
  }

  // Percentage Change Calculation
  static calculatePercentageChange(prices: number[]): number {
    if (prices.length < 2) return 0;
    const start = prices[0];
    const end = prices[prices.length - 1];
    return start > 0 ? ((end - start) / start) * 100 : 0;
  }

  // Comprehensive Price Analysis
  static analyzePriceData(prices: number[], highs: number[], lows: number[], volumes: number[]): PriceAnalysis {
    const currentPrice = prices[prices.length - 1] || 0;
    const rsi = this.calculateRSI(prices);
    const macd = this.calculateMACD(prices);
    const vwap = this.calculateVWAP(prices, volumes);
    const volatility = this.calculateVolatility(prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]));
    
    // Calculate support and resistance
    const recentHighs = highs.slice(-20);
    const recentLows = lows.slice(-20);
    const resistance = Math.max(...recentHighs);
    const support = Math.min(...recentLows);
    
    // Determine trend
    let trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    if (rsi > 50 && macd.histogram > 0) trend = 'BULLISH';
    else if (rsi < 50 && macd.histogram < 0) trend = 'BEARISH';
    
    return {
      currentPrice,
      vwap,
      support,
      resistance,
      rsi,
      macd,
      trend,
      volatility
    };
  }

  // Performance Metrics Calculation
  static calculatePerformanceMetrics(prices: number[], returns: number[]): PerformanceMetrics {
    const sharpeRatio = this.calculateSharpeRatio(returns);
    const maxDrawdown = this.calculateMaxDrawdown(prices);
    const profitFactor = this.calculateProfitFactor(returns.map(r => r * 1000)); // Convert to PnL
    
    // Calculate win rate
    const positiveReturns = returns.filter(r => r > 0).length;
    const winRate = returns.length > 0 ? positiveReturns / returns.length : 0;
    
    // Calculate total return
    const totalReturn = returns.reduce((sum, ret) => sum + ret, 0) * 100;
    
    // Estimate average holding period (in days)
    const avgHoldingPeriod = 5; // Simplified assumption
    
    return {
      sharpeRatio,
      maxDrawdown,
      winRate,
      profitFactor,
      totalReturn,
      avgHoldingPeriod
    };
  }

  // Risk Metrics Calculation
  static calculateRiskMetrics(returns: number[], marketReturns: number[]): RiskMetrics {
    const volatility = this.calculateVolatility(returns);
    const correlation = this.calculateCorrelation(returns, marketReturns);
    
    // Calculate Value at Risk (95% confidence)
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const varIndex = Math.floor(returns.length * 0.05);
    const valueAtRisk = sortedReturns[varIndex] || 0;
    
    // Calculate Expected Shortfall (average of worst 5%)
    const worstReturns = sortedReturns.slice(0, varIndex + 1);
    const expectedShortfall = worstReturns.length > 0 
      ? worstReturns.reduce((sum, ret) => sum + ret, 0) / worstReturns.length 
      : 0;
    
    // Calculate Beta
    const beta = this.calculateBeta(returns, marketReturns);
    
    return {
      valueAtRisk: Math.abs(valueAtRisk),
      expectedShortfall: Math.abs(expectedShortfall),
      beta,
      correlation,
      volatility
    };
  }

  // Beta Calculation
  static calculateBeta(returns: number[], marketReturns: number[]): number {
    if (returns.length !== marketReturns.length || returns.length < 2) return 1;
    
    const covariance = this.calculateCovariance(returns, marketReturns);
    const marketVariance = this.calculateVariance(marketReturns);
    
    return marketVariance > 0 ? covariance / marketVariance : 1;
  }

  // Covariance Calculation
  static calculateCovariance(returns1: number[], returns2: number[]): number {
    if (returns1.length !== returns2.length || returns1.length < 2) return 0;
    
    const mean1 = returns1.reduce((sum, ret) => sum + ret, 0) / returns1.length;
    const mean2 = returns2.reduce((sum, ret) => sum + ret, 0) / returns2.length;
    
    const covariance = returns1.reduce((sum, ret, i) => {
      return sum + (ret - mean1) * (returns2[i] - mean2);
    }, 0) / (returns1.length - 1);
    
    return covariance;
  }

  // Variance Calculation
  static calculateVariance(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1);
    
    return variance;
  }

  // Trend Analysis
  static analyzeTrend(prices: number[], period: number = 20): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    if (prices.length < period) return 'NEUTRAL';
    
    const recentPrices = prices.slice(-period);
    const sma = recentPrices.reduce((sum, price) => sum + price, 0) / period;
    const currentPrice = prices[prices.length - 1];
    
    const rsi = this.calculateRSI(prices);
    
    if (currentPrice > sma && rsi > 50) return 'BULLISH';
    if (currentPrice < sma && rsi < 50) return 'BEARISH';
    return 'NEUTRAL';
  }

  // Max Drawdown Calculation
  static calculateMaxDrawdown(portfolioValues: number[]): number {
    if (portfolioValues.length < 2) return 0;
    
    let maxDrawdown = 0;
    let peak = portfolioValues[0];
    
    for (let i = 1; i < portfolioValues.length; i++) {
      if (portfolioValues[i] > peak) {
        peak = portfolioValues[i];
      }
      
      const drawdown = (peak - portfolioValues[i]) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }

  // Sharpe Ratio Calculation
  static calculateSharpeRatio(returns: number[], riskFreeRate: number = 0.02): number {
    if (returns.length === 0) return 0;
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev === 0) return 0;
    
    const annualizedReturn = avgReturn * 252; // Assuming 252 trading days
    const annualizedStdDev = stdDev * Math.sqrt(252);
    
    return (annualizedReturn - riskFreeRate) / annualizedStdDev;
  }

  // Profit Factor Calculation
  static calculateProfitFactor(pnlValues: number[]): number {
    if (pnlValues.length === 0) return 0;
    
    const profits = pnlValues.filter(pnl => pnl > 0).reduce((sum, pnl) => sum + pnl, 0);
    const losses = Math.abs(pnlValues.filter(pnl => pnl < 0).reduce((sum, pnl) => sum + pnl, 0));
    
    if (losses === 0) return profits > 0 ? Infinity : 0;
    
    return profits / losses;
  }

  // Volatility Calculation
  static calculateVolatility(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1);
    
    return Math.sqrt(variance);
  }

  // Correlation Calculation
  static calculateCorrelation(returns1: number[], returns2: number[]): number {
    if (returns1.length !== returns2.length || returns1.length < 2) return 0;
    
    const n = returns1.length;
    const mean1 = returns1.reduce((sum, ret) => sum + ret, 0) / n;
    const mean2 = returns2.reduce((sum, ret) => sum + ret, 0) / n;
    
    let numerator = 0;
    let sumSq1 = 0;
    let sumSq2 = 0;
    
    for (let i = 0; i < n; i++) {
      const diff1 = returns1[i] - mean1;
      const diff2 = returns2[i] - mean2;
      
      numerator += diff1 * diff2;
      sumSq1 += diff1 * diff1;
      sumSq2 += diff2 * diff2;
    }
    
    const denominator = Math.sqrt(sumSq1 * sumSq2);
    
    if (denominator === 0) return 0;
    
    return numerator / denominator;
  }
}
