
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  FileText, 
  Globe, 
  Database, 
  Download,
  Upload,
  Search,
  Zap
} from "lucide-react";

interface ExtractionJob {
  id: string;
  source: string;
  type: 'pdf' | 'website' | 'document';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  extractedData: number;
  createdAt: string;
}

export const AIDataExtractionAgent = () => {
  const [url, setUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const extractionJobs: ExtractionJob[] = [
    {
      id: 'job-001',
      source: 'RBI Monetary Policy Report.pdf',
      type: 'pdf',
      status: 'completed',
      progress: 100,
      extractedData: 1247,
      createdAt: '2 hours ago'
    },
    {
      id: 'job-002',
      source: 'https://www.sebi.gov.in/reports',
      type: 'website',
      status: 'processing',
      progress: 67,
      extractedData: 892,
      createdAt: '1 hour ago'
    },
    {
      id: 'job-003',
      source: 'NSE Market Analysis Q4.pdf',
      type: 'pdf',
      status: 'completed',
      progress: 100,
      extractedData: 2156,
      createdAt: '30 minutes ago'
    },
    {
      id: 'job-004',
      source: 'https://economictimes.com/markets',
      type: 'website',
      status: 'failed',
      progress: 0,
      extractedData: 0,
      createdAt: '15 minutes ago'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 border-green-400';
      case 'processing': return 'text-blue-400 border-blue-400';
      case 'pending': return 'text-yellow-400 border-yellow-400';
      case 'failed': return 'text-red-400 border-red-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const handleFileUpload = () => {
    setIsProcessing(true);
    setUploadProgress(0);
    
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsProcessing(false);
          return 100;
        }
        return prev + Math.random() * 10;
      });
    }, 200);
  };

  const handleUrlExtraction = () => {
    if (!url) return;
    
    setIsProcessing(true);
    // Simulate extraction process
    setTimeout(() => {
      setIsProcessing(false);
      setUrl('');
    }, 3000);
  };

  return (
    <div className="space-y-6">
      {/* AI Agent Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            AI Data Extraction & Analysis Agent
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">4,295</div>
              <div className="text-sm text-trading-muted">Data Points Extracted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">156</div>
              <div className="text-sm text-trading-muted">Documents Processed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">23</div>
              <div className="text-sm text-trading-muted">ML Models Trained</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">94.2%</div>
              <div className="text-sm text-trading-muted">Extraction Accuracy</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="extract" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="extract" className="data-[state=active]:bg-trading-accent">
            <Upload className="h-4 w-4 mr-2" />
            Extract Data
          </TabsTrigger>
          <TabsTrigger value="jobs" className="data-[state=active]:bg-trading-accent">
            <Search className="h-4 w-4 mr-2" />
            Extraction Jobs
          </TabsTrigger>
          <TabsTrigger value="database" className="data-[state=active]:bg-trading-accent">
            <Database className="h-4 w-4 mr-2" />
            Data Storage
          </TabsTrigger>
          <TabsTrigger value="analysis" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            AI Analysis
          </TabsTrigger>
        </TabsList>

        <TabsContent value="extract">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* PDF Upload */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  PDF Document Upload
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-trading-border rounded-lg p-8 text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-trading-muted" />
                  <div className="text-trading-light mb-2">Drop PDF files here</div>
                  <div className="text-sm text-trading-muted mb-4">or click to browse</div>
                  <Button onClick={handleFileUpload} disabled={isProcessing}>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload PDF
                  </Button>
                </div>
                
                {isProcessing && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-light">Processing...</span>
                      <span className="text-blue-400">{uploadProgress.toFixed(1)}%</span>
                    </div>
                    <Progress value={uploadProgress} className="h-2" />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Website URL */}
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Website Data Extraction
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Input
                    placeholder="Enter website URL to extract data from..."
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    className="bg-trading-dark border-trading-border"
                  />
                  <Button 
                    onClick={handleUrlExtraction}
                    disabled={!url || isProcessing}
                    className="w-full"
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    Extract Website Data
                  </Button>
                </div>
                
                <div className="text-xs text-trading-muted">
                  <div className="mb-1">Supported sources:</div>
                  <div>• Financial reports & PDFs</div>
                  <div>• News websites & articles</div>
                  <div>• Regulatory filings</div>
                  <div>• Market data pages</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="jobs">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Extraction Job History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {extractionJobs.map((job, index) => (
                  <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {job.type === 'pdf' ? (
                          <FileText className="h-5 w-5 text-blue-400" />
                        ) : (
                          <Globe className="h-5 w-5 text-green-400" />
                        )}
                        <div>
                          <div className="text-trading-light font-medium">{job.source}</div>
                          <div className="text-xs text-trading-muted">{job.createdAt}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge variant="outline" className={getStatusColor(job.status)}>
                          {job.status.toUpperCase()}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          Export
                        </Button>
                      </div>
                    </div>
                    
                    {job.status === 'processing' && (
                      <div className="mb-3">
                        <div className="flex justify-between text-xs mb-1">
                          <span className="text-trading-muted">Progress</span>
                          <span className="text-blue-400">{job.progress}%</span>
                        </div>
                        <Progress value={job.progress} className="h-1" />
                      </div>
                    )}
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-trading-muted">Data Points</div>
                        <div className="text-trading-light font-medium">{job.extractedData}</div>
                      </div>
                      <div>
                        <div className="text-trading-muted">Type</div>
                        <div className="text-blue-400">{job.type.toUpperCase()}</div>
                      </div>
                      <div>
                        <div className="text-trading-muted">Status</div>
                        <div className={job.status === 'completed' ? 'text-green-400' : 'text-yellow-400'}>
                          {job.status}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Extracted Data Storage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <Database className="h-8 w-8 mx-auto mb-2 text-blue-400" />
                  <div className="text-lg font-bold text-blue-400">4,295</div>
                  <div className="text-sm text-trading-muted">Total Records</div>
                </div>
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-green-400" />
                  <div className="text-lg font-bold text-green-400">156</div>
                  <div className="text-sm text-trading-muted">Documents</div>
                </div>
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <Globe className="h-8 w-8 mx-auto mb-2 text-purple-400" />
                  <div className="text-lg font-bold text-purple-400">89</div>
                  <div className="text-sm text-trading-muted">Websites</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">AI-Powered Data Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-trading-dark rounded border border-green-500/30">
                  <div className="flex items-center mb-2">
                    <Brain className="h-5 w-5 text-green-400 mr-2" />
                    <span className="text-green-400 font-medium">Market Sentiment Analysis</span>
                  </div>
                  <div className="text-sm text-trading-light">
                    Current market sentiment: <span className="text-green-400">Bullish (78% confidence)</span>
                    <br />
                    Based on analysis of 47 recent financial reports and 156 news articles.
                  </div>
                </div>
                
                <div className="p-4 bg-trading-dark rounded border border-blue-500/30">
                  <div className="flex items-center mb-2">
                    <Zap className="h-5 w-5 text-blue-400 mr-2" />
                    <span className="text-blue-400 font-medium">Key Insights Extracted</span>
                  </div>
                  <div className="text-sm text-trading-light">
                    • RBI policy indicates potential rate cuts in Q2
                    <br />
                    • Banking sector showing strong earnings growth
                    <br />
                    • Technology stocks experiencing sector rotation
                  </div>
                </div>
                
                <div className="p-4 bg-trading-dark rounded border border-purple-500/30">
                  <div className="flex items-center mb-2">
                    <Database className="h-5 w-5 text-purple-400 mr-2" />
                    <span className="text-purple-400 font-medium">ML Model Training Data</span>
                  </div>
                  <div className="text-sm text-trading-light">
                    Extracted data has been processed and prepared for ML model training.
                    <br />
                    Ready for: Sentiment analysis, price prediction, and risk assessment models.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
