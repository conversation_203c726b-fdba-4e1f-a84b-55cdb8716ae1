
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Zap, TrendingUp, TrendingDown } from "lucide-react";
import { useState } from "react";
import { useMarketData } from "../data/MarketDataProvider";

interface QuickTradeWidgetProps {
  symbol: string;
  onTrade?: (order: any) => void;
}

export const QuickTradeWidget: React.FC<QuickTradeWidgetProps> = ({ 
  symbol, 
  onTrade 
}) => {
  const { data } = useMarketData();
  const [quantity, setQuantity] = useState<number>(1);
  const symbolData = data[symbol];

  const handleQuickBuy = () => {
    const order = {
      symbol,
      type: 'BUY',
      orderType: 'MARKET',
      quantity,
      price: symbolData?.price,
      timestamp: new Date()
    };
    
    onTrade?.(order);
    console.log('Quick Buy Order:', order);
  };

  const handleQuickSell = () => {
    const order = {
      symbol,
      type: 'SELL',
      orderType: 'MARKET',
      quantity,
      price: symbolData?.price,
      timestamp: new Date()
    };
    
    onTrade?.(order);
    console.log('Quick Sell Order:', order);
  };

  if (!symbolData) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="p-4">
          <div className="text-center text-trading-muted">
            Loading {symbol}...
          </div>
        </CardContent>
      </Card>
    );
  }

  const isPositive = symbolData.change >= 0;

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-trading-light flex items-center">
          <Zap className="h-4 w-4 mr-2" />
          Quick Trade - {symbol}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Price Display */}
        <div className="text-center">
          <div className="text-2xl font-bold text-trading-light">
            ₹{symbolData.price.toLocaleString()}
          </div>
          <div className={`text-sm flex items-center justify-center ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
            {isPositive ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
            ₹{Math.abs(symbolData.change).toFixed(2)} ({symbolData.changePercent.toFixed(2)}%)
          </div>
        </div>

        {/* Quantity Input */}
        <div>
          <label className="text-xs text-trading-muted">Quantity</label>
          <Input
            type="number"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
            className="bg-trading-dark border-trading-border text-center"
          />
        </div>

        {/* Order Value */}
        <div className="text-center p-2 bg-trading-dark rounded border">
          <div className="text-xs text-trading-muted">Order Value</div>
          <div className="text-lg font-medium text-trading-light">
            ₹{(symbolData.price * quantity).toLocaleString()}
          </div>
        </div>

        {/* Quick Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button 
            onClick={handleQuickBuy}
            className="bg-green-600 hover:bg-green-700 text-white"
            disabled={quantity <= 0}
          >
            <TrendingUp className="h-4 w-4 mr-1" />
            Quick Buy
          </Button>
          <Button 
            onClick={handleQuickSell}
            className="bg-red-600 hover:bg-red-700 text-white"
            disabled={quantity <= 0}
          >
            <TrendingDown className="h-4 w-4 mr-1" />
            Quick Sell
          </Button>
        </div>

        {/* Quick Quantity Buttons */}
        <div className="flex justify-center space-x-1">
          {[1, 5, 10, 25, 50, 100].map((qty) => (
            <Button
              key={qty}
              size="sm"
              variant="outline"
              className="text-xs px-2 py-1"
              onClick={() => setQuantity(qty)}
            >
              {qty}
            </Button>
          ))}
        </div>

        {/* Market Stats */}
        <div className="grid grid-cols-3 gap-2 text-xs text-center">
          <div>
            <div className="text-trading-muted">High</div>
            <div className="text-trading-light">₹{symbolData.high.toLocaleString()}</div>
          </div>
          <div>
            <div className="text-trading-muted">Low</div>
            <div className="text-trading-light">₹{symbolData.low.toLocaleString()}</div>
          </div>
          <div>
            <div className="text-trading-muted">Volume</div>
            <div className="text-trading-light">{(symbolData.volume / 1000000).toFixed(1)}M</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
