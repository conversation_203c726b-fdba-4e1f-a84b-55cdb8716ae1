
export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

export class RedisService {
  private config: RedisConfig;
  private isConnected: boolean = false;
  private cache: Map<string, { value: any; expiry?: number }> = new Map();

  constructor(config: RedisConfig) {
    this.config = config;
  }

  async connect(): Promise<boolean> {
    try {
      console.log(`Connecting to Redis at ${this.config.host}:${this.config.port}...`);
      
      // Simulate connection delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      this.isConnected = true;
      console.log('Redis connected successfully');
      return true;
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      return false;
    }
  }

  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Not connected to Redis');
    }

    const expiry = ttlSeconds ? Date.now() + (ttlSeconds * 1000) : undefined;
    this.cache.set(key, { value, expiry });
  }

  async get(key: string): Promise<any | null> {
    if (!this.isConnected) {
      throw new Error('Not connected to Redis');
    }

    const cached = this.cache.get(key);
    if (!cached) return null;

    if (cached.expiry && Date.now() > cached.expiry) {
      this.cache.delete(key);
      return null;
    }

    return cached.value;
  }

  async del(key: string): Promise<boolean> {
    if (!this.isConnected) {
      throw new Error('Not connected to Redis');
    }

    return this.cache.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    if (!this.isConnected) {
      throw new Error('Not connected to Redis');
    }

    const cached = this.cache.get(key);
    if (!cached) return false;

    if (cached.expiry && Date.now() > cached.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  isConnectedStatus(): boolean {
    return this.isConnected;
  }

  disconnect(): void {
    this.isConnected = false;
    this.cache.clear();
    console.log('Redis disconnected');
  }
}
