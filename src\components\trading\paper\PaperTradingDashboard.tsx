
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, TrendingDown, DollarSign, Target, Activity, Zap } from "lucide-react";
import { paperTradingService, PaperPortfolio } from '@/services/PaperTradingService';
import { PaperOrderEntry } from './PaperOrderEntry';
import { PaperPositionsView } from './PaperPositionsView';
import { PaperOrdersView } from './PaperOrdersView';

export const PaperTradingDashboard: React.FC = () => {
  const [portfolio, setPortfolio] = useState<PaperPortfolio | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const subscription = paperTradingService.getPortfolioStream().subscribe(setPortfolio);
    return () => subscription.unsubscribe();
  }, []);

  if (!portfolio) {
    return <div className="text-center py-8 text-trading-muted">Loading portfolio...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Total Portfolio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">
              ₹{portfolio.totalValue.toLocaleString()}
            </div>
            <div className="text-xs text-blue-400">Paper Trading</div>
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Total P&L</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${portfolio.totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {portfolio.totalPnL >= 0 ? '+' : ''}₹{portfolio.totalPnL.toLocaleString()}
            </div>
            <div className={`text-xs ${portfolio.totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {((portfolio.totalPnL / 1000000) * 100).toFixed(2)}%
            </div>
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Day P&L</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${portfolio.dayPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {portfolio.dayPnL >= 0 ? '+' : ''}₹{portfolio.dayPnL.toLocaleString()}
            </div>
            <div className="text-xs text-trading-muted">Today's Performance</div>
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Available Cash</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">
              ₹{portfolio.availableCash.toLocaleString()}
            </div>
            <div className="text-xs text-trading-muted">
              {portfolio.positions.length} Positions
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Asset Allocation */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Asset Allocation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-trading-dark rounded-lg">
              <div className="text-xl font-bold text-green-400">
                ₹{portfolio.equityValue.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Equity Positions</div>
              <div className="text-xs text-blue-400">
                {portfolio.totalValue > 0 ? ((portfolio.equityValue / portfolio.totalValue) * 100).toFixed(1) : 0}%
              </div>
            </div>
            
            <div className="text-center p-4 bg-trading-dark rounded-lg">
              <div className="text-xl font-bold text-purple-400">
                ₹{portfolio.optionsValue.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Options Positions</div>
              <div className="text-xs text-blue-400">
                {portfolio.totalValue > 0 ? ((portfolio.optionsValue / portfolio.totalValue) * 100).toFixed(1) : 0}%
              </div>
            </div>
            
            <div className="text-center p-4 bg-trading-dark rounded-lg">
              <div className="text-xl font-bold text-blue-400">
                ₹{portfolio.availableCash.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Available Cash</div>
              <div className="text-xs text-blue-400">
                {portfolio.totalValue > 0 ? ((portfolio.availableCash / portfolio.totalValue) * 100).toFixed(1) : 0}%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trading Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trade">Trade</TabsTrigger>
          <TabsTrigger value="positions">Positions</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-yellow-400" />
                  Intraday Positions
                </CardTitle>
              </CardHeader>
              <CardContent>
                {portfolio.positions.filter(p => p.strategy === 'INTRADAY').length === 0 ? (
                  <div className="text-center py-8 text-trading-muted">
                    <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No intraday positions</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {portfolio.positions.filter(p => p.strategy === 'INTRADAY').map((position) => (
                      <div key={position.id} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                        <div>
                          <div className="font-medium text-trading-light">{position.symbol}</div>
                          <div className="text-xs text-trading-muted">
                            {position.instrumentType} • Qty: {position.quantity}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-sm font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {position.pnl >= 0 ? '+' : ''}₹{position.pnl.toFixed(2)}
                          </div>
                          <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
                  Swing Positions
                </CardTitle>
              </CardHeader>
              <CardContent>
                {portfolio.positions.filter(p => p.strategy === 'SWING').length === 0 ? (
                  <div className="text-center py-8 text-trading-muted">
                    <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No swing positions</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {portfolio.positions.filter(p => p.strategy === 'SWING').map((position) => (
                      <div key={position.id} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                        <div>
                          <div className="font-medium text-trading-light">{position.symbol}</div>
                          <div className="text-xs text-trading-muted">
                            {position.instrumentType} • Qty: {position.quantity}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-sm font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {position.pnl >= 0 ? '+' : ''}₹{position.pnl.toFixed(2)}
                          </div>
                          <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {position.pnlPercent >= 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trade">
          <PaperOrderEntry />
        </TabsContent>

        <TabsContent value="positions">
          <PaperPositionsView positions={portfolio.positions} />
        </TabsContent>

        <TabsContent value="orders">
          <PaperOrdersView orders={portfolio.orders} />
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button 
              onClick={() => setActiveTab('trade')}
              className="bg-green-600 hover:bg-green-700"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Place Trade
            </Button>
            <Button 
              onClick={() => paperTradingService.resetPortfolio()}
              variant="outline"
            >
              Reset Portfolio
            </Button>
            <Button variant="outline">
              <Target className="h-4 w-4 mr-2" />
              Set Alerts
            </Button>
            <Button variant="outline">
              View History
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
