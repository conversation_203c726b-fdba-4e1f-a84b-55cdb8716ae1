
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { comprehensiveStrategyEngine } from '@/services/ComprehensiveStrategyEngine';
import { apiStructureService } from '@/services/APIStructureService';
import { TradingStrategy } from '@/types/strategies';
import { Play, Pause, TrendingUp, TrendingDown, Activity, DollarSign } from 'lucide-react';

interface StrategyPerformance {
  strategyId: string;
  totalSignals: number;
  winRate: number;
  totalPnL: number;
  sharpeRatio: number;
  maxDrawdown: number;
}

interface SignalData {
  strategyId: string;
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  price: number;
  confidence: number;
  timestamp: string;
}

export const RealStrategyDashboard: React.FC = () => {
  const [strategies, setStrategies] = useState<TradingStrategy[]>([]);
  const [performances, setPerformances] = useState<StrategyPerformance[]>([]);
  const [recentSignals, setRecentSignals] = useState<SignalData[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [isLoading, setIsLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState<any>(null);

  useEffect(() => {
    initializeDashboard();
    const interval = setInterval(updateDashboard, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const initializeDashboard = async () => {
    try {
      setIsLoading(true);
      
      // Initialize services
      await comprehensiveStrategyEngine.initialize();
      await apiStructureService.initialize();
      
      // Load initial data
      await updateDashboard();
      
      // Subscribe to real-time updates
      comprehensiveStrategyEngine.getSignalsStream().subscribe(signals => {
        setRecentSignals(signals.slice(-20)); // Keep last 20 signals
      });
      
      comprehensiveStrategyEngine.getPerformanceStream().subscribe(performances => {
        setPerformances(performances);
      });
      
    } catch (error) {
      console.error('Failed to initialize dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateDashboard = async () => {
    try {
      // Get all strategies
      const allStrategies = comprehensiveStrategyEngine.getAllStrategies();
      setStrategies(allStrategies);
      
      // Get system status
      const status = await apiStructureService.handleRequest('/status', 'GET');
      setSystemStatus(status.data);
      
    } catch (error) {
      console.error('Failed to update dashboard:', error);
    }
  };

  const handleStrategyToggle = async (strategyId: string, isActive: boolean) => {
    try {
      if (isActive) {
        await apiStructureService.handleRequest(`/strategies/${strategyId}/deactivate`, 'POST', {}, {}, 'dashboard');
      } else {
        const symbols = ['AAPL', 'GOOGL', 'MSFT']; // Default symbols
        await apiStructureService.handleRequest(`/strategies/${strategyId}/activate`, 'POST', { id: strategyId }, { symbols }, 'dashboard');
      }
      await updateDashboard();
    } catch (error) {
      console.error('Failed to toggle strategy:', error);
    }
  };

  const getFilteredStrategies = () => {
    if (selectedCategory === 'All') return strategies;
    return strategies.filter(s => s.category === selectedCategory);
  };

  const getPerformanceData = (strategyId: string) => {
    return performances.find(p => p.strategyId === strategyId) || {
      strategyId,
      totalSignals: 0,
      winRate: 0,
      totalPnL: 0,
      sharpeRatio: 0,
      maxDrawdown: 0
    };
  };

  const getRiskBadgeColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'Low': return 'bg-green-500';
      case 'Medium': return 'bg-yellow-500';
      case 'High': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getActiveStrategies = () => strategies.filter(s => s.isActive);
  const getTotalSignalsToday = () => recentSignals.filter(s => 
    new Date(s.timestamp).toDateString() === new Date().toDateString()
  ).length;
  const getTotalPnL = () => performances.reduce((sum, p) => sum + p.totalPnL, 0);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-400">Initializing Strategy Engine...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Active Strategies</p>
                <p className="text-2xl font-bold text-white">{getActiveStrategies().length}/{strategies.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Signals Today</p>
                <p className="text-2xl font-bold text-white">{getTotalSignalsToday()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">Total P&L</p>
                <p className={`text-2xl font-bold ${getTotalPnL() >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  ${getTotalPnL().toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-400">System Load</p>
                <p className="text-2xl font-bold text-white">{systemStatus?.systemLoad?.toFixed(1) || 0}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="All">All Strategies</TabsTrigger>
          <TabsTrigger value="Intraday">Intraday</TabsTrigger>
          <TabsTrigger value="Swing">Swing</TabsTrigger>
          <TabsTrigger value="Options">Options</TabsTrigger>
          <TabsTrigger value="Scalping">Scalping</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-4">
          {/* Strategy Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getFilteredStrategies().map((strategy) => {
              const performance = getPerformanceData(strategy.id);
              return (
                <Card key={strategy.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{strategy.name}</CardTitle>
                      <Badge className={getRiskBadgeColor(strategy.riskLevel)}>
                        {strategy.riskLevel}
                      </Badge>
                    </div>
                    <CardDescription>{strategy.id}</CardDescription>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Strategy Info */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Timeframe:</span>
                        <span className="text-white">{strategy.timeframe}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Indicators:</span>
                        <span className="text-white">{strategy.indicators.join(', ')}</span>
                      </div>
                    </div>

                    {/* Performance Metrics */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Win Rate:</span>
                        <span className="text-white">{performance.winRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={performance.winRate} className="h-2" />
                      
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Total Signals:</span>
                        <span className="text-white">{performance.totalSignals}</span>
                      </div>
                      
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">P&L:</span>
                        <span className={performance.totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}>
                          ${performance.totalPnL.toFixed(2)}
                        </span>
                      </div>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center justify-between pt-2">
                      <Button
                        variant={strategy.isActive ? "destructive" : "default"}
                        size="sm"
                        onClick={() => handleStrategyToggle(strategy.id, strategy.isActive)}
                        className="flex items-center gap-2"
                      >
                        {strategy.isActive ? (
                          <>
                            <Pause className="h-4 w-4" />
                            Stop
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4" />
                            Start
                          </>
                        )}
                      </Button>
                      
                      <div className="flex items-center gap-2">
                        {strategy.isActive && (
                          <div className="flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            <span className="text-xs text-green-400">Live</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Recent Signals */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Signals</CardTitle>
              <CardDescription>Latest trading signals from active strategies</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {recentSignals.slice(-10).map((signal, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant={signal.action === 'BUY' ? 'default' : 'destructive'}>
                        {signal.action}
                      </Badge>
                      <span className="font-medium">{signal.symbol}</span>
                      <span className="text-gray-400">${signal.price.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-400">{signal.confidence.toFixed(2)} confidence</span>
                      <span className="text-xs text-gray-500">
                        {new Date(signal.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
                {recentSignals.length === 0 && (
                  <p className="text-center text-gray-400 py-8">No signals generated yet</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
