
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Activity } from "lucide-react";
import { MarketHeatmap } from "../data/MarketHeatmap";
import { TradingAlerts } from "../data/TradingAlerts";

export const MarketStatus = () => {
  const marketData = [
    { symbol: "NIFTY", price: 24157.81, change: 0.68, volume: "2.1B" },
    { symbol: "SENSEX", price: 79243.18, change: 0.45, volume: "1.8B" },
    { symbol: "BANKNIFTY", price: 51234.56, change: -0.23, volume: "890M" },
    { symbol: "FINNIFTY", price: 23456.78, change: 1.12, volume: "456M" },
  ];

  const sectors = [
    { name: "Banking", change: 1.23, leaders: ["HDFC", "ICICI", "SBI"] },
    { name: "IT", change: -0.45, leaders: ["T<PERSON>", "INFY", "WIPRO"] },
    { name: "Auto", change: 2.1, leaders: ["TATA", "M&M", "BAJAJ"] },
    { name: "Pharma", change: 0.89, leaders: ["SUN", "CIPLA", "LUPIN"] },
  ];

  return (
    <div className="space-y-4">
      {/* Main Indices */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Market Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {marketData.map((market, index) => (
              <div key={index} className="text-center">
                <div className="text-sm text-trading-muted">{market.symbol}</div>
                <div className="text-lg font-bold text-trading-light">{market.price.toLocaleString()}</div>
                <div className={`text-sm flex items-center justify-center ${
                  market.change >= 0 ? "text-green-400" : "text-red-400"
                }`}>
                  {market.change >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                  {market.change > 0 ? "+" : ""}{market.change}%
                </div>
                <div className="text-xs text-trading-muted">Vol: {market.volume}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Market Heatmap */}
      <MarketHeatmap />

      {/* Sector Performance and Trading Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Sector Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {sectors.map((sector, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-trading-light">{sector.name}</span>
                    <Badge 
                      variant="outline" 
                      className={sector.change >= 0 ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}
                    >
                      {sector.change > 0 ? "+" : ""}{sector.change}%
                    </Badge>
                  </div>
                  <div className="text-xs text-trading-muted">
                    {sector.leaders.join(", ")}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <TradingAlerts />
      </div>
    </div>
  );
};
