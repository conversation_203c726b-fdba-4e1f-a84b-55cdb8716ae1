
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { RotateCcw, TrendingUp, TrendingDown, Target, Brain, Zap, ArrowLeft } from "lucide-react";

interface SectorRotationTrackerProps {
  onBack?: () => void;
  sectorData?: Array<{
    name: string;
    momentum: number;
    relativeStrength: number;
    flow: number;
    phase: string;
    trend: string;
    leaders: string[];
    prediction: string;
  }>;
  economicData?: {
    currentPhase: string;
    confidence: number;
    nextPhase: string;
    timeToTransition: string;
    indicators: {
      gdp: { value: number; trend: string };
      inflation: { value: number; trend: string };
      employment: { value: number; trend: string };
      consumption: { value: number; trend: string };
    };
  };
}

export const SectorRotationTracker = ({ 
  onBack, 
  sectorData = [],
  economicData 
}: SectorRotationTrackerProps) => {
  
  // Calculate sector momentum using relative strength and money flow
  const calculateSectorMomentum = (sectorPrice: number[], marketPrice: number[]) => {
    if (sectorPrice.length < 2 || marketPrice.length < 2) return 0;
    
    const sectorReturn = (sectorPrice[sectorPrice.length - 1] - sectorPrice[0]) / sectorPrice[0];
    const marketReturn = (marketPrice[marketPrice.length - 1] - marketPrice[0]) / marketPrice[0];
    
    return ((sectorReturn - marketReturn) / marketReturn) * 100;
  };

  // Calculate relative strength ratio
  const calculateRelativeStrength = (sectorPrice: number[], benchmarkPrice: number[]) => {
    if (sectorPrice.length === 0 || benchmarkPrice.length === 0) return 50;
    
    const sectorCurrent = sectorPrice[sectorPrice.length - 1];
    const sectorPrevious = sectorPrice[0];
    const benchmarkCurrent = benchmarkPrice[benchmarkPrice.length - 1];
    const benchmarkPrevious = benchmarkPrice[0];
    
    const sectorReturn = (sectorCurrent - sectorPrevious) / sectorPrevious;
    const benchmarkReturn = (benchmarkCurrent - benchmarkPrevious) / benchmarkPrevious;
    
    const relativeStrength = sectorReturn / benchmarkReturn;
    return Math.min(100, Math.max(0, relativeStrength * 50 + 50));
  };

  // Determine market cycle phase based on economic indicators
  const determineMarketPhase = (indicators: any) => {
    if (!indicators) return "Unknown";
    
    const { gdp, inflation, employment } = indicators;
    
    if (gdp.value > 6 && inflation.value < 4 && employment.value < 6) {
      return "Expansion";
    } else if (gdp.value > 4 && inflation.value > 5) {
      return "Peak";
    } else if (gdp.value < 2 && employment.value > 8) {
      return "Contraction";
    } else {
      return "Recovery";
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase.toLowerCase()) {
      case "expansion": case "growth": return "bg-green-600";
      case "momentum": case "leading": return "bg-blue-600";
      case "peak": return "bg-purple-600";
      case "recovery": return "bg-yellow-600";
      case "contraction": case "decline": return "bg-red-600";
      case "lagging": return "bg-gray-600";
      default: return "bg-gray-600";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-400" />;
      case "down": return <TrendingDown className="h-4 w-4 text-red-400" />;
      default: return <Target className="h-4 w-4 text-yellow-400" />;
    }
  };

  const currentPhase = economicData ? determineMarketPhase(economicData.indicators) : "Unknown";

  return (
    <div className="space-y-4">
      {/* Header with Back Button */}
      {onBack && (
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h2 className="text-xl font-bold text-trading-light">Sector Rotation Analysis</h2>
        </div>
      )}

      {/* Economic Cycle Overview */}
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <RotateCcw className="h-5 w-5 mr-2" />
            Economic Cycle Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          {economicData ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-sm text-trading-muted">Current Phase</div>
                <div className="text-lg font-bold text-blue-400">{currentPhase}</div>
                <div className="text-xs text-trading-muted">Confidence: {economicData.confidence}%</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">GDP Growth</div>
                <div className="text-lg font-bold text-green-400">{economicData.indicators.gdp.value}%</div>
                {getTrendIcon(economicData.indicators.gdp.trend)}
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">Inflation</div>
                <div className="text-lg font-bold text-yellow-400">{economicData.indicators.inflation.value}%</div>
                {getTrendIcon(economicData.indicators.inflation.trend)}
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">Employment</div>
                <div className="text-lg font-bold text-blue-400">{economicData.indicators.employment.value}%</div>
                {getTrendIcon(economicData.indicators.employment.trend)}
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-trading-muted">
              <RotateCcw className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No economic data available</p>
              <p className="text-xs mt-1">Connect to data feed for economic cycle analysis</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="sectors" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-trading-darker">
          <TabsTrigger value="sectors">Sector Analysis</TabsTrigger>
          <TabsTrigger value="relative">Relative Strength</TabsTrigger>
        </TabsList>

        <TabsContent value="sectors" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Target className="h-5 w-5 mr-2" />
                Sector Momentum & Flow Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] w-full">
                {sectorData.length > 0 ? (
                  <div className="space-y-3">
                    {sectorData.map((sector, index) => (
                      <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <span className="text-lg font-medium text-trading-light">{sector.name}</span>
                            <Badge className={getPhaseColor(sector.phase)}>
                              {sector.phase}
                            </Badge>
                            {getTrendIcon(sector.trend)}
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-trading-muted">Money Flow</div>
                            <div className={`text-lg font-bold ${sector.flow > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {sector.flow > 0 ? '+' : ''}₹{Math.abs(sector.flow)}Cr
                            </div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div className="text-sm text-trading-muted mb-1">Momentum Score</div>
                            <Progress value={Math.abs(sector.momentum)} className="h-2" />
                            <div className={`text-xs mt-1 ${sector.momentum > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {sector.momentum.toFixed(1)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-sm text-trading-muted mb-1">Relative Strength</div>
                            <Progress value={sector.relativeStrength} className="h-2" />
                            <div className="text-xs text-blue-400 mt-1">{sector.relativeStrength.toFixed(1)}</div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-xs text-trading-muted">Leaders</div>
                            <div className="text-sm text-trading-light">{sector.leaders.join(", ")}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-xs text-trading-muted">Analysis</div>
                            <div className="text-sm text-blue-400">{sector.prediction}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-trading-muted">
                    <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No sector data available</p>
                    <p className="text-xs mt-1">Connect to data feed for sector analysis</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="relative" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                Relative Strength Rankings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] w-full">
                {sectorData.length > 0 ? (
                  <div className="space-y-3">
                    {sectorData
                      .sort((a, b) => b.relativeStrength - a.relativeStrength)
                      .map((sector, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="text-trading-light font-medium">{sector.name}</span>
                          {getTrendIcon(sector.trend)}
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="text-sm text-trading-muted">RS Score</div>
                            <div className="text-lg font-bold text-blue-400">{sector.relativeStrength.toFixed(1)}</div>
                          </div>
                          <div className="w-32">
                            <Progress value={sector.relativeStrength} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-trading-muted">
                    <Zap className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No relative strength data available</p>
                    <p className="text-xs mt-1">Connect to data feed for RS analysis</p>
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Brain className="h-4 w-4 mr-2" />
          Calculate Rotation
        </Button>
        <Button variant="outline">
          <Target className="h-4 w-4 mr-2" />
          Export Analysis
        </Button>
      </div>
    </div>
  );
};
