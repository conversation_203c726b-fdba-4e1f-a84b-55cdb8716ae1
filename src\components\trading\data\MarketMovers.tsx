
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Eye, Brain } from "lucide-react";

interface StockData {
  symbol: string;
  price: string;
  change: string;
  changeValue: string;
  volume: string;
}

interface MarketMoversProps {
  topGainers?: StockData[];
  topLosers?: StockData[];
  isLoading?: boolean;
  onStockSelect?: (symbol: string) => void;
}

export const MarketMovers = ({ 
  topGainers = [], 
  topLosers = [], 
  isLoading = false,
  onStockSelect 
}: MarketMoversProps) => {
  const renderStockCard = (stock: StockData, isGainer: boolean) => (
    <Card 
      key={stock.symbol} 
      className="bg-trading-darker border-trading-border hover:border-trading-accent cursor-pointer transition-colors"
      onClick={() => onStockSelect?.(stock.symbol)}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="text-trading-light font-medium text-sm">{stock.symbol}</div>
          {isGainer ? (
            <TrendingUp className="h-4 w-4 text-green-400" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-400" />
          )}
        </div>
        <div className="text-lg font-bold text-trading-light mb-1">{stock.price}</div>
        <div className="flex items-center justify-between mb-2">
          <Badge 
            variant="outline" 
            className={isGainer ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}
          >
            {stock.change}
          </Badge>
          <span className="text-xs text-trading-muted">Vol: {stock.volume}</span>
        </div>
        <div className={`text-sm mb-3 ${isGainer ? "text-green-400" : "text-red-400"}`}>
          {stock.changeValue}
        </div>
        <div className="flex gap-1">
          <Button size="sm" variant="outline" className="text-xs flex-1">
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline" className="text-xs flex-1">
            <Brain className="h-3 w-3 mr-1" />
            AI
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderEmptyState = (title: string, icon: React.ReactNode) => (
    <div className="text-center py-8">
      {icon}
      <p className="text-trading-muted mt-2">{isLoading ? 'Loading market data...' : `No ${title.toLowerCase()} data available`}</p>
      {!isLoading && (
        <p className="text-sm text-trading-muted mt-1">Connect to market data feed to see live information</p>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Top Gainers */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            Top Gainers
          </CardTitle>
        </CardHeader>
        <CardContent>
          {topGainers.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {topGainers.map((stock) => renderStockCard(stock, true))}
            </div>
          ) : (
            renderEmptyState('Top Gainers', <TrendingUp className="h-12 w-12 mx-auto opacity-50" />)
          )}
        </CardContent>
      </Card>

      {/* Top Losers */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingDown className="h-5 w-5 mr-2 text-red-400" />
            Top Losers
          </CardTitle>
        </CardHeader>
        <CardContent>
          {topLosers.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              {topLosers.map((stock) => renderStockCard(stock, false))}
            </div>
          ) : (
            renderEmptyState('Top Losers', <TrendingDown className="h-12 w-12 mx-auto opacity-50" />)
          )}
        </CardContent>
      </Card>
    </div>
  );
};
