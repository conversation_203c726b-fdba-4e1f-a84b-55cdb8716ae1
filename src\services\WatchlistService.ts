
export interface WatchlistStock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  sector?: string;
  aiConfidence?: number;
  aiSignal?: 'BUY' | 'SELL' | 'HOLD';
  lastUpdated: Date;
}

export interface Watchlist {
  id: string;
  name: string;
  type: 'AI' | 'USER';
  description?: string;
  stocks: WatchlistStock[];
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
  tags?: string[];
}

export interface AIWatchlistCriteria {
  strategy: 'SWING' | 'INTRADAY' | 'MULTIBAGGER' | 'OPTIONS';
  minMarketCap?: number;
  maxMarketCap?: number;
  sectors?: string[];
  technicalIndicators?: string[];
  fundamentalMetrics?: string[];
}

class WatchlistService {
  private watchlists: Map<string, Watchlist> = new Map();
  private aiUpdateInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeDefaultWatchlists();
    this.startAIUpdates();
  }

  private initializeDefaultWatchlists() {
    // AI-generated watchlists
    const aiPicksWatchlist: Watchlist = {
      id: 'ai-picks',
      name: 'AI Top Picks',
      type: 'AI',
      description: 'Stocks selected by our AI algorithms for high potential returns',
      stocks: [
        {
          symbol: 'RELIANCE',
          name: 'Reliance Industries',
          price: 2523.45,
          change: 30.25,
          changePercent: 1.2,
          volume: 1250000,
          marketCap: 1700000000000,
          sector: 'Energy',
          aiConfidence: 92,
          aiSignal: 'BUY',
          lastUpdated: new Date()
        },
        {
          symbol: 'TCS',
          name: 'Tata Consultancy Services',
          price: 3845.30,
          change: 30.74,
          changePercent: 0.8,
          volume: 890000,
          marketCap: *************,
          sector: 'Technology',
          aiConfidence: 87,
          aiSignal: 'BUY',
          lastUpdated: new Date()
        },
        {
          symbol: 'HDFCBANK',
          name: 'HDFC Bank',
          price: 1654.75,
          change: -4.96,
          changePercent: -0.3,
          volume: 2100000,
          marketCap: *************,
          sector: 'Banking',
          aiConfidence: 84,
          aiSignal: 'HOLD',
          lastUpdated: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: true,
      tags: ['AI', 'Top Picks', 'High Confidence']
    };

    const swingWatchlist: Watchlist = {
      id: 'ai-swing',
      name: 'AI Swing Trades',
      type: 'AI',
      description: 'Medium-term swing trading opportunities identified by AI',
      stocks: [
        {
          symbol: 'TATAMOTORS',
          name: 'Tata Motors',
          price: 789.45,
          change: 26.87,
          changePercent: 3.4,
          volume: 3500000,
          marketCap: ************,
          sector: 'Automotive',
          aiConfidence: 89,
          aiSignal: 'BUY',
          lastUpdated: new Date()
        },
        {
          symbol: 'BAJFINANCE',
          name: 'Bajaj Finance',
          price: 6234.80,
          change: 112.23,
          changePercent: 1.8,
          volume: 450000,
          marketCap: 380000000000,
          sector: 'Financial Services',
          aiConfidence: 84,
          aiSignal: 'BUY',
          lastUpdated: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: true,
      tags: ['AI', 'Swing Trading', 'Medium Term']
    };

    // Default user watchlist
    const userWatchlist: Watchlist = {
      id: 'user-default',
      name: 'My Watchlist',
      type: 'USER',
      description: 'Personal stock watchlist',
      stocks: [
        {
          symbol: 'INFY',
          name: 'Infosys',
          price: 1789.20,
          change: 37.57,
          changePercent: 2.1,
          volume: 1800000,
          lastUpdated: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: false,
      tags: ['Personal']
    };

    this.watchlists.set(aiPicksWatchlist.id, aiPicksWatchlist);
    this.watchlists.set(swingWatchlist.id, swingWatchlist);
    this.watchlists.set(userWatchlist.id, userWatchlist);
  }

  private startAIUpdates() {
    // Update AI watchlists every 5 minutes
    this.aiUpdateInterval = setInterval(() => {
      this.updateAIWatchlists();
    }, 5 * 60 * 1000);
  }

  private updateAIWatchlists() {
    // Simulate AI updates
    this.watchlists.forEach((watchlist) => {
      if (watchlist.type === 'AI') {
        watchlist.stocks.forEach((stock) => {
          // Simulate price updates
          const changePercent = (Math.random() - 0.5) * 6; // -3% to +3%
          stock.changePercent = changePercent;
          stock.change = (stock.price * changePercent) / 100;
          stock.price += stock.change;
          stock.lastUpdated = new Date();
          
          // Update AI confidence
          if (stock.aiConfidence) {
            stock.aiConfidence = Math.max(60, Math.min(95, stock.aiConfidence + (Math.random() - 0.5) * 10));
          }
        });
        watchlist.updatedAt = new Date();
      }
    });
  }

  getAllWatchlists(): Watchlist[] {
    return Array.from(this.watchlists.values());
  }

  getWatchlistById(id: string): Watchlist | undefined {
    return this.watchlists.get(id);
  }

  getAIWatchlists(): Watchlist[] {
    return Array.from(this.watchlists.values()).filter(w => w.type === 'AI');
  }

  getUserWatchlists(): Watchlist[] {
    return Array.from(this.watchlists.values()).filter(w => w.type === 'USER');
  }

  createUserWatchlist(name: string, description?: string): Watchlist {
    const watchlist: Watchlist = {
      id: `user-${Date.now()}`,
      name,
      type: 'USER',
      description,
      stocks: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: false,
      tags: ['Personal']
    };

    this.watchlists.set(watchlist.id, watchlist);
    return watchlist;
  }

  addStockToWatchlist(watchlistId: string, stock: Omit<WatchlistStock, 'lastUpdated'>): boolean {
    const watchlist = this.watchlists.get(watchlistId);
    if (!watchlist || watchlist.type === 'AI') return false;

    const existingStock = watchlist.stocks.find(s => s.symbol === stock.symbol);
    if (existingStock) return false;

    watchlist.stocks.push({
      ...stock,
      lastUpdated: new Date()
    });
    watchlist.updatedAt = new Date();
    return true;
  }

  removeStockFromWatchlist(watchlistId: string, symbol: string): boolean {
    const watchlist = this.watchlists.get(watchlistId);
    if (!watchlist || watchlist.type === 'AI') return false;

    const index = watchlist.stocks.findIndex(s => s.symbol === symbol);
    if (index === -1) return false;

    watchlist.stocks.splice(index, 1);
    watchlist.updatedAt = new Date();
    return true;
  }

  deleteWatchlist(id: string): boolean {
    const watchlist = this.watchlists.get(id);
    if (!watchlist || watchlist.type === 'AI') return false;

    return this.watchlists.delete(id);
  }

  generateAIWatchlist(criteria: AIWatchlistCriteria): Watchlist {
    // Simulate AI stock selection based on criteria
    const stockPool = [
      { symbol: 'WIPRO', name: 'Wipro', sector: 'Technology', marketCap: 320000000000 },
      { symbol: 'LT', name: 'Larsen & Toubro', sector: 'Infrastructure', marketCap: 230000000000 },
      { symbol: 'ASIANPAINT', name: 'Asian Paints', sector: 'Consumer Goods', marketCap: ************ },
      { symbol: 'MARUTI', name: 'Maruti Suzuki', sector: 'Automotive', marketCap: 320000000000 },
      { symbol: 'BHARTIARTL', name: 'Bharti Airtel', sector: 'Telecom', marketCap: 450000000000 }
    ];

    const selectedStocks: WatchlistStock[] = stockPool
      .filter(stock => {
        if (criteria.sectors && !criteria.sectors.includes(stock.sector)) return false;
        if (criteria.minMarketCap && stock.marketCap < criteria.minMarketCap) return false;
        if (criteria.maxMarketCap && stock.marketCap > criteria.maxMarketCap) return false;
        return true;
      })
      .slice(0, 5)
      .map(stock => ({
        symbol: stock.symbol,
        name: stock.name,
        price: 1000 + Math.random() * 2000,
        change: (Math.random() - 0.5) * 100,
        changePercent: (Math.random() - 0.5) * 5,
        volume: Math.floor(Math.random() * 5000000),
        marketCap: stock.marketCap,
        sector: stock.sector,
        aiConfidence: 80 + Math.random() * 15,
        aiSignal: Math.random() > 0.5 ? 'BUY' : 'HOLD' as 'BUY' | 'SELL' | 'HOLD',
        lastUpdated: new Date()
      }));

    const watchlist: Watchlist = {
      id: `ai-${criteria.strategy.toLowerCase()}-${Date.now()}`,
      name: `AI ${criteria.strategy} Strategy`,
      type: 'AI',
      description: `AI-generated watchlist for ${criteria.strategy} strategy`,
      stocks: selectedStocks,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPublic: true,
      tags: ['AI', criteria.strategy, 'Generated']
    };

    this.watchlists.set(watchlist.id, watchlist);
    return watchlist;
  }

  destroy() {
    if (this.aiUpdateInterval) {
      clearInterval(this.aiUpdateInterval);
    }
  }
}

export const watchlistService = new WatchlistService();
