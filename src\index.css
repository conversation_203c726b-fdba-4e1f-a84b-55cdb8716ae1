
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Dark Theme - Deep Space Blue Base */
  --background: 210 100% 6%;
  --foreground: 213 27% 84%;
  --card: 210 100% 8%;
  --card-foreground: 213 27% 84%;
  --popover: 210 100% 8%;
  --popover-foreground: 213 27% 84%;
  --primary: 193 100% 50%;
  --primary-foreground: 210 100% 6%;
  --secondary: 210 40% 15%;
  --secondary-foreground: 213 27% 84%;
  --muted: 210 40% 15%;
  --muted-foreground: 213 13% 65%;
  --accent: 193 100% 50%;
  --accent-foreground: 210 100% 6%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --border: 210 40% 20%;
  --input: 210 40% 15%;
  --ring: 193 100% 50%;
  --radius: 0.75rem;
  
  /* Professional Trading Colors */
  --trading-dark: 210 100% 6%;
  --trading-darker: 210 100% 4%;
  --trading-light: 213 27% 84%;
  --trading-muted: 213 13% 65%;
  --trading-primary: 193 100% 50%;
  --trading-accent: 210 40% 15%;
  --trading-border: 210 40% 20%;
  --trading-success: 142 72% 29%;
  --trading-warning: 35 91% 56%;
  --trading-danger: 0 84% 60%;
  
  /* Glassmorphism Variables */
  --glass-background: hsla(210, 100%, 6%, 0.8);
  --glass-border: hsla(193, 100%, 50%, 0.2);
  --glass-shadow: 0 8px 32px hsla(193, 100%, 50%, 0.1);
}

.light {
  /* Light Theme - Pure White Base */
  --background: 0 0% 100%;
  --foreground: 210 40% 15%;
  --card: 0 0% 100%;
  --card-foreground: 210 40% 15%;
  --popover: 0 0% 100%;
  --popover-foreground: 210 40% 15%;
  --primary: 204 100% 50%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 210 40% 15%;
  --muted: 210 40% 96%;
  --muted-foreground: 210 10% 40%;
  --accent: 204 100% 50%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 98%;
  --border: 210 40% 88%;
  --input: 210 40% 96%;
  --ring: 204 100% 50%;
  
  /* Light Trading Colors */
  --trading-dark: 0 0% 100%;
  --trading-darker: 210 40% 98%;
  --trading-light: 210 40% 15%;
  --trading-muted: 210 10% 40%;
  --trading-primary: 204 100% 50%;
  --trading-accent: 210 40% 96%;
  --trading-border: 210 40% 88%;
  --trading-success: 142 72% 29%;
  --trading-warning: 35 91% 56%;
  --trading-danger: 0 84% 60%;
  
  /* Light Glassmorphism Variables */
  --glass-background: hsla(0, 0%, 100%, 0.8);
  --glass-border: hsla(204, 100%, 50%, 0.2);
  --glass-shadow: 0 8px 32px hsla(204, 100%, 50%, 0.1);
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  background: linear-gradient(135deg, hsl(var(--trading-dark)) 0%, hsl(var(--trading-darker)) 100%);
  color: hsl(var(--trading-light));
  min-height: 100vh;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional Glassmorphism Components */
.glass-card {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, hsl(var(--trading-primary)), transparent);
  opacity: 0.5;
}

.glass-navbar {
  background: var(--glass-background);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px hsla(var(--trading-primary) / 0.1);
}

/* Micro-animations */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px hsla(var(--trading-primary) / 0.3);
  }
  50% { 
    box-shadow: 0 0 40px hsla(var(--trading-primary) / 0.6);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-shimmer {
  position: relative;
  overflow: hidden;
}

.animate-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, hsla(var(--trading-primary) / 0.1), transparent);
  animation: shimmer 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

/* Skeleton Loading States */
.skeleton {
  background: linear-gradient(90deg, 
    hsla(var(--muted) / 0.2) 25%, 
    hsla(var(--muted) / 0.4) 50%, 
    hsla(var(--muted) / 0.2) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius);
}

.skeleton-text {
  height: 1rem;
  width: 100%;
}

.skeleton-title {
  height: 1.5rem;
  width: 60%;
}

.skeleton-avatar {
  height: 2.5rem;
  width: 2.5rem;
  border-radius: 50%;
}

/* Interactive Elements */
.interactive-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px hsla(var(--trading-primary) / 0.2);
}

.interactive-scale:hover {
  transform: scale(1.02);
}

.interactive-glow:hover {
  box-shadow: 0 0 20px hsla(var(--trading-primary) / 0.4);
}

/* Professional Trading Panel Styles */
.trading-panel {
  background: var(--glass-background);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--glass-shadow);
  border-radius: var(--radius);
}

.price-positive {
  color: hsl(var(--trading-success));
  text-shadow: 0 0 10px hsla(var(--trading-success) / 0.3);
}

.price-negative {
  color: hsl(var(--trading-danger));
  text-shadow: 0 0 10px hsla(var(--trading-danger) / 0.3);
}

.price-neutral {
  color: hsl(var(--trading-muted));
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsla(var(--muted) / 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, hsl(var(--trading-primary)), hsl(var(--accent)));
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, hsl(var(--accent)), hsl(var(--trading-primary)));
}

/* Accessibility Improvements */
.focus-visible {
  outline: 2px solid hsl(var(--trading-primary));
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 210 40% 30%;
    --glass-border: hsla(193, 100%, 50%, 0.5);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .glass-card, .trading-panel {
    background: white !important;
    border: 1px solid black !important;
    box-shadow: none !important;
  }
}
