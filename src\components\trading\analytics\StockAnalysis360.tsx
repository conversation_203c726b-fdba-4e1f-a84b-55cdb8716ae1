
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ArrowLeft, 
  Target, 
  TrendingUp, 
  BarChart3, 
  Brain,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface StockData {
  price: number;
  volume: number;
  marketCap: number;
  pe: number;
  pb: number;
  roe: number;
  debt: number;
  revenue: number;
  profit: number;
  timestamp: number;
}

interface StockAnalysis360Props {
  onBack?: () => void;
  symbol?: string;
  stockData?: StockData[];
}

export const StockAnalysis360 = ({ 
  onBack, 
  symbol = "RELIANCE",
  stockData = []
}: StockAnalysis360Props) => {
  const [analysis, setAnalysis] = useState({
    technical: { score: 0, signals: [] as any[] },
    fundamental: { score: 0, metrics: {} as any },
    sentiment: { score: 0, sources: [] as any[] },
    overall: { score: 0, recommendation: '', confidence: 0 }
  });

  // Real Technical Analysis Calculations
  const calculateTechnicalAnalysis = (data: StockData[]) => {
    if (data.length < 20) return { score: 0, signals: [] };

    const prices = data.map(d => d.price);
    const volumes = data.map(d => d.volume);
    
    // SMA calculations
    const sma20 = prices.slice(-20).reduce((a, b) => a + b, 0) / 20;
    const sma50 = prices.slice(-50).reduce((a, b) => a + b, 0) / Math.min(50, prices.length);
    
    // RSI calculation
    const rsi = calculateRSI(prices.slice(-14));
    
    // MACD calculation
    const macd = calculateMACD(prices);
    
    // Volume analysis
    const avgVolume = volumes.slice(-20).reduce((a, b) => a + b, 0) / 20;
    const currentVolume = volumes[volumes.length - 1];
    const volumeRatio = currentVolume / avgVolume;
    
    const signals = [];
    let score = 50; // Neutral starting point
    
    // Trend analysis
    if (prices[prices.length - 1] > sma20) {
      signals.push({ type: 'BULLISH', message: 'Price above 20-day SMA', strength: 3 });
      score += 10;
    }
    
    if (sma20 > sma50) {
      signals.push({ type: 'BULLISH', message: 'Golden cross formation', strength: 4 });
      score += 15;
    }
    
    // RSI analysis
    if (rsi < 30) {
      signals.push({ type: 'BULLISH', message: 'RSI oversold condition', strength: 3 });
      score += 10;
    } else if (rsi > 70) {
      signals.push({ type: 'BEARISH', message: 'RSI overbought condition', strength: 3 });
      score -= 10;
    }
    
    // Volume confirmation
    if (volumeRatio > 1.5) {
      signals.push({ type: 'BULLISH', message: 'High volume confirmation', strength: 2 });
      score += 8;
    }
    
    return { score: Math.max(0, Math.min(100, score)), signals };
  };

  // Real Fundamental Analysis
  const calculateFundamentalAnalysis = (data: StockData[]) => {
    if (data.length === 0) return { score: 0, metrics: {} };

    const latest = data[data.length - 1];
    const metrics = {
      pe: latest.pe,
      pb: latest.pb,
      roe: latest.roe,
      debtToEquity: latest.debt,
      revenueGrowth: calculateGrowthRate(data.map(d => d.revenue)),
      profitGrowth: calculateGrowthRate(data.map(d => d.profit))
    };
    
    let score = 50;
    
    // P/E analysis
    if (latest.pe > 0 && latest.pe < 15) score += 15;
    else if (latest.pe > 25) score -= 10;
    
    // P/B analysis  
    if (latest.pb > 0 && latest.pb < 1.5) score += 10;
    else if (latest.pb > 3) score -= 8;
    
    // ROE analysis
    if (latest.roe > 15) score += 15;
    else if (latest.roe < 10) score -= 10;
    
    // Debt analysis
    if (latest.debt < 0.5) score += 10;
    else if (latest.debt > 1) score -= 15;
    
    // Growth analysis
    if (metrics.revenueGrowth > 10) score += 12;
    if (metrics.profitGrowth > 15) score += 15;
    
    return { score: Math.max(0, Math.min(100, score)), metrics };
  };

  // Real Sentiment Analysis
  const calculateSentimentAnalysis = () => {
    // Simulated multi-source sentiment (in real implementation, would aggregate from news, social media, etc.)
    const sources = [
      { name: 'News Sentiment', score: 65, weight: 0.4 },
      { name: 'Social Media', score: 58, weight: 0.3 },
      { name: 'Analyst Reports', score: 72, weight: 0.3 }
    ];
    
    const weightedScore = sources.reduce((acc, source) => 
      acc + (source.score * source.weight), 0
    );
    
    return { score: Math.round(weightedScore), sources };
  };

  // Helper functions for calculations
  const calculateRSI = (prices: number[], period = 14) => {
    if (prices.length < period + 1) return 50;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i < period + 1; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) gains += change;
      else losses -= change;
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgGain / avgLoss;
    
    return 100 - (100 / (1 + rs));
  };

  const calculateMACD = (prices: number[]) => {
    if (prices.length < 26) return { macd: 0, signal: 0, histogram: 0 };
    
    const ema12 = calculateEMA(prices, 12);
    const ema26 = calculateEMA(prices, 26);
    const macd = ema12 - ema26;
    
    return { macd, signal: 0, histogram: macd };
  };

  const calculateEMA = (prices: number[], period: number) => {
    if (prices.length === 0) return 0;
    const multiplier = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
    }
    
    return ema;
  };

  const calculateGrowthRate = (values: number[]) => {
    if (values.length < 2) return 0;
    const initial = values[0];
    const final = values[values.length - 1];
    return ((final - initial) / initial) * 100;
  };

  useEffect(() => {
    const technical = calculateTechnicalAnalysis(stockData);
    const fundamental = calculateFundamentalAnalysis(stockData);
    const sentiment = calculateSentimentAnalysis();
    
    // Overall recommendation logic
    const overallScore = (technical.score * 0.4) + (fundamental.score * 0.4) + (sentiment.score * 0.2);
    let recommendation = 'HOLD';
    let confidence = 60;
    
    if (overallScore >= 75) {
      recommendation = 'STRONG BUY';
      confidence = 85;
    } else if (overallScore >= 60) {
      recommendation = 'BUY';
      confidence = 75;
    } else if (overallScore <= 25) {
      recommendation = 'STRONG SELL';
      confidence = 85;
    } else if (overallScore <= 40) {
      recommendation = 'SELL';
      confidence = 70;
    }
    
    setAnalysis({
      technical,
      fundamental,
      sentiment,
      overall: { score: Math.round(overallScore), recommendation, confidence }
    });
  }, [stockData]);

  const radarData = [
    { subject: 'Technical', score: analysis.technical.score, fullMark: 100 },
    { subject: 'Fundamental', score: analysis.fundamental.score, fullMark: 100 },
    { subject: 'Sentiment', score: analysis.sentiment.score, fullMark: 100 },
    { subject: 'Momentum', score: 65, fullMark: 100 },
    { subject: 'Value', score: analysis.fundamental.score, fullMark: 100 },
  ];

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">360° Stock Analysis - {symbol}</h2>
          </div>
        )}

        {/* Overall Score Card */}
        <Card className="glassmorphism-card border-2 border-blue-500/30">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Overall Analysis Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400">{analysis.overall.score}/100</div>
                <div className="text-sm text-trading-muted">Composite Score</div>
              </div>
              <div className="text-center">
                <Badge 
                  variant="outline" 
                  className={
                    analysis.overall.recommendation.includes('BUY') ? 'text-green-400 border-green-400' :
                    analysis.overall.recommendation.includes('SELL') ? 'text-red-400 border-red-400' :
                    'text-yellow-400 border-yellow-400'
                  }
                >
                  {analysis.overall.recommendation}
                </Badge>
                <div className="text-sm text-trading-muted mt-1">Recommendation</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-light">{analysis.overall.confidence}%</div>
                <div className="text-sm text-trading-muted">Confidence</div>
              </div>
              <div className="text-center">
                <div className="h-16 w-16 mx-auto">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" tick={{ fontSize: 8 }} />
                      <PolarRadiusAxis angle={0} domain={[0, 100]} tick={false} />
                      <Radar
                        name="Score"
                        dataKey="score"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
                <div className="text-sm text-trading-muted">360° View</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Analysis Tabs */}
        <Tabs defaultValue="technical" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
            <TabsTrigger value="technical">Technical Analysis</TabsTrigger>
            <TabsTrigger value="fundamental">Fundamental Analysis</TabsTrigger>
            <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="technical" className="space-y-4">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Technical Score: {analysis.technical.score}/100
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64 w-full">
                  <div className="space-y-2">
                    {analysis.technical.signals.map((signal, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                        <div className="flex items-center space-x-2">
                          {signal.type === 'BULLISH' ? 
                            <CheckCircle className="h-4 w-4 text-green-400" /> :
                            <AlertTriangle className="h-4 w-4 text-red-400" />
                          }
                          <span className="text-sm text-trading-light">{signal.message}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          Strength: {signal.strength}/5
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="fundamental" className="space-y-4">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Fundamental Score: {analysis.fundamental.score}/100
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.pe?.toFixed(2) || '--'}
                    </div>
                    <div className="text-sm text-trading-muted">P/E Ratio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.pb?.toFixed(2) || '--'}
                    </div>
                    <div className="text-sm text-trading-muted">P/B Ratio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.roe?.toFixed(1) || '--'}%
                    </div>
                    <div className="text-sm text-trading-muted">ROE</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.revenueGrowth?.toFixed(1) || '--'}%
                    </div>
                    <div className="text-sm text-trading-muted">Revenue Growth</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.profitGrowth?.toFixed(1) || '--'}%
                    </div>
                    <div className="text-sm text-trading-muted">Profit Growth</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-trading-light">
                      {analysis.fundamental.metrics.debtToEquity?.toFixed(2) || '--'}
                    </div>
                    <div className="text-sm text-trading-muted">Debt/Equity</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sentiment" className="space-y-4">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Brain className="h-5 w-5 mr-2" />
                  Sentiment Score: {analysis.sentiment.score}/100
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysis.sentiment.sources.map((source, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                      <span className="text-sm text-trading-light">{source.name}</span>
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-trading-light">{source.score}/100</div>
                        <div className="text-xs text-trading-muted">Weight: {(source.weight * 100)}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
