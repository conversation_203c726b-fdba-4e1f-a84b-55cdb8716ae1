
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Brain, TrendingUp, Target, Zap } from "lucide-react";

interface AnalyticsModel {
  name: string;
  confidence?: number;
  signal?: 'BUY' | 'SELL' | 'NEUTRAL';
  impact?: 'High' | 'Medium' | 'Low';
}

interface AdvancedAnalyticsProps {
  intradayAnalytics?: AnalyticsModel[];
  swingAnalytics?: AnalyticsModel[];
  multibaggerAnalytics?: AnalyticsModel[];
  optionsAnalytics?: AnalyticsModel[];
  totalAnalytics?: number;
  avgConfidence?: number;
  activeSignals?: number;
  successRate?: number;
  buySignals?: number;
  sellSignals?: number;
  neutralSignals?: number;
}

export const AdvancedAnalytics = ({
  intradayAnalytics = [],
  swingAnalytics = [],
  multibaggerAnalytics = [],
  optionsAnalytics = [],
  totalAnalytics = 0,
  avgConfidence = 0,
  activeSignals = 0,
  successRate = 0,
  buySignals = 0,
  sellSignals = 0,
  neutralSignals = 0
}: AdvancedAnalyticsProps) => {
  const getSignalColor = (signal?: string) => {
    switch (signal) {
      case "BUY": return "text-green-400 border-green-400";
      case "SELL": return "text-red-400 border-red-400";
      default: return "text-yellow-400 border-yellow-400";
    }
  };

  const renderAnalyticsGrid = (analytics: AnalyticsModel[]) => {
    if (analytics.length === 0) {
      return (
        <div className="text-center py-8">
          <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-trading-muted">No analytics data available</p>
          <p className="text-sm text-trading-muted mt-1">Connect to data feed to see real-time analytics</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {analytics.map((analytic, index) => (
          <Card key={index} className="bg-trading-dark border-trading-border">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-trading-light">{analytic.name}</h4>
                {analytic.signal && (
                  <Badge variant="outline" className={getSignalColor(analytic.signal)}>
                    {analytic.signal}
                  </Badge>
                )}
              </div>
              <div className="flex items-center justify-between">
                <div className="text-xs text-trading-muted">
                  Confidence: <span className="text-blue-400">{analytic.confidence || '--'}%</span>
                </div>
                <div className="text-xs text-trading-muted">
                  Impact: <span className={
                    analytic.impact === "High" ? "text-red-400" : 
                    analytic.impact === "Medium" ? "text-yellow-400" : 
                    "text-green-400"
                  }>
                    {analytic.impact || '--'}
                  </span>
                </div>
              </div>
              {analytic.confidence && (
                <div className="mt-2">
                  <div className="w-full bg-trading-darker rounded-full h-1">
                    <div 
                      className="bg-blue-400 h-1 rounded-full" 
                      style={{ width: `${analytic.confidence}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            AI Analytics Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Active Analytics</div>
              <div className="text-2xl font-bold text-blue-400">{totalAnalytics || '--'}</div>
              <div className="text-xs text-green-400">
                {totalAnalytics > 0 ? 'All Systems Operational' : 'No Data'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Avg Confidence</div>
              <div className="text-2xl font-bold text-green-400">
                {avgConfidence ? `${avgConfidence.toFixed(1)}%` : '--'}
              </div>
              <div className="text-xs text-trading-muted">Real-time data</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Active Signals</div>
              <div className="text-2xl font-bold text-yellow-400">{activeSignals || '--'}</div>
              <div className="text-xs text-trading-muted">
                {buySignals || 0} BUY • {sellSignals || 0} SELL • {neutralSignals || 0} NEUTRAL
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Success Rate</div>
              <div className="text-2xl font-bold text-green-400">
                {successRate ? `${successRate.toFixed(1)}%` : '--'}
              </div>
              <div className="text-xs text-trading-muted">Historical performance</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="intraday" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="intraday" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Intraday
          </TabsTrigger>
          <TabsTrigger value="swing" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Swing
          </TabsTrigger>
          <TabsTrigger value="multibagger" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Multibagger
          </TabsTrigger>
          <TabsTrigger value="options" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            Options
          </TabsTrigger>
        </TabsList>

        <TabsContent value="intraday">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">
                Intraday AI Analytics ({intradayAnalytics.length} Models)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderAnalyticsGrid(intradayAnalytics)}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="swing">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">
                Swing Trading Analytics ({swingAnalytics.length} Models)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderAnalyticsGrid(swingAnalytics)}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="multibagger">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">
                Multibagger Investment Analytics ({multibaggerAnalytics.length} Models)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderAnalyticsGrid(multibaggerAnalytics)}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="options">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">
                Options Analytics ({optionsAnalytics.length} Models)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderAnalyticsGrid(optionsAnalytics)}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
