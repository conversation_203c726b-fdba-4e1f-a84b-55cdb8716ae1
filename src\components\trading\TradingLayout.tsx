
import { Header } from "./layout/Header";
import { Sidebar } from "./layout/Sidebar";
import { MainContent } from "./layout/MainContent";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import { MarketDataProvider } from "./data/MarketDataProvider";
import { PaperTradingProvider } from "./providers/PaperTradingProvider";
import { useEffect, useState } from "react";
import { AppInitializer } from "../../services/AppInitializer";
import { RefreshCw } from "lucide-react";

export const TradingLayout = () => {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState("dashboard");
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const initializer = AppInitializer.getInstance();
        await initializer.initialize();
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setInitError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setIsInitializing(false);
      }
    };

    initializeApp();
  }, []);

  const handleNavigate = (viewId: string) => {
    setActiveView(viewId);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  if (isInitializing) {
    return (
      <div className="min-h-screen bg-trading-dark text-trading-light flex items-center justify-center">
        <div className="text-center space-y-4">
          <RefreshCw className="h-12 w-12 animate-spin text-blue-400 mx-auto" />
          <div className="text-xl font-semibold">Initializing Trading Platform...</div>
          <div className="text-trading-muted">Loading AI agents, data pipelines, and trading systems</div>
        </div>
      </div>
    );
  }

  if (initError) {
    return (
      <div className="min-h-screen bg-trading-dark text-trading-light flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-xl font-semibold text-red-400">Initialization Failed</div>
          <div className="text-trading-muted">{initError}</div>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <MarketDataProvider>
      <PaperTradingProvider>
        <div className="min-h-screen bg-trading-dark text-trading-light">
          <Header />
          <div className="flex h-[calc(100vh-64px)]">
            {/* Desktop Layout */}
            <div className="hidden lg:flex w-full">
              <ResizablePanelGroup direction="horizontal">
                <ResizablePanel defaultSize={20} minSize={15} maxSize={25}>
                  <Sidebar onNavigate={handleNavigate} activeView={activeView} />
                </ResizablePanel>
                <ResizableHandle className="w-1 bg-trading-border hover:bg-trading-accent" />
                <ResizablePanel defaultSize={80}>
                  <MainContent 
                    activeView={activeView} 
                    onViewChange={handleNavigate}
                    sidebarCollapsed={sidebarCollapsed}
                    onToggleSidebar={toggleSidebar}
                  />
                </ResizablePanel>
              </ResizablePanelGroup>
            </div>

            {/* Mobile Layout */}
            <div className="flex lg:hidden w-full relative">
              {/* Mobile Sidebar Overlay */}
              {!sidebarCollapsed && (
                <div className="absolute inset-0 bg-black/50 z-40" onClick={toggleSidebar} />
              )}
              
              {/* Mobile Sidebar */}
              <div className={`fixed top-16 left-0 h-[calc(100vh-64px)] w-64 z-50 transform transition-transform duration-300 ${
                sidebarCollapsed ? '-translate-x-full' : 'translate-x-0'
              }`}>
                <Sidebar onNavigate={handleNavigate} activeView={activeView} />
              </div>

              {/* Main Content */}
              <div className="flex-1">
                <MainContent 
                  activeView={activeView} 
                  onViewChange={handleNavigate}
                  sidebarCollapsed={sidebarCollapsed}
                  onToggleSidebar={toggleSidebar}
                />
              </div>
            </div>
          </div>
        </div>
      </PaperTradingProvider>
    </MarketDataProvider>
  );
};
