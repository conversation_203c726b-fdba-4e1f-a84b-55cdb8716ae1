
export interface CompressionConfig {
  enableTickCompression: boolean;
  tickRetentionDays: number;
  enableAggregation: boolean;
  aggregationIntervals: string[]; // ['1m', '5m', '1h', '1d']
}

export interface AggregatedData {
  symbol: string;
  interval: string;
  timestamp: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  vwap: number;
  count: number;
}

export class DataCompressionService {
  private config: CompressionConfig;
  private aggregationBuffers: Map<string, any[]> = new Map();

  constructor(config: CompressionConfig) {
    this.config = config;
    this.startAggregationLoop();
  }

  private startAggregationLoop(): void {
    if (!this.config.enableAggregation) return;

    // Process aggregations every minute
    setInterval(() => {
      this.processAggregations();
    }, 60000);
  }

  addTickForAggregation(tick: any): void {
    if (!this.config.enableAggregation) return;

    for (const interval of this.config.aggregationIntervals) {
      const key = `${tick.symbol}_${interval}`;
      
      if (!this.aggregationBuffers.has(key)) {
        this.aggregationBuffers.set(key, []);
      }
      
      this.aggregationBuffers.get(key)!.push(tick);
    }
  }

  private processAggregations(): void {
    for (const [key, ticks] of this.aggregationBuffers) {
      if (ticks.length === 0) continue;

      const [symbol, interval] = key.split('_');
      const aggregated = this.aggregateTicks(ticks, symbol, interval);
      
      if (aggregated) {
        console.log(`Aggregated ${ticks.length} ticks for ${symbol} ${interval}:`, aggregated);
        
        // Clear processed ticks
        this.aggregationBuffers.set(key, []);
      }
    }
  }

  private aggregateTicks(ticks: any[], symbol: string, interval: string): AggregatedData | null {
    if (ticks.length === 0) return null;

    const sortedTicks = ticks.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    const open = sortedTicks[0].ltp;
    const close = sortedTicks[sortedTicks.length - 1].ltp;
    const high = Math.max(...sortedTicks.map(t => t.ltp));
    const low = Math.min(...sortedTicks.map(t => t.ltp));
    const volume = sortedTicks.reduce((sum, t) => sum + (t.volume || 0), 0);
    const vwap = sortedTicks.reduce((sum, t) => sum + (t.vwap || t.ltp), 0) / sortedTicks.length;

    return {
      symbol,
      interval,
      timestamp: this.getIntervalTimestamp(sortedTicks[0].timestamp, interval),
      open,
      high,
      low,
      close,
      volume,
      vwap,
      count: sortedTicks.length
    };
  }

  private getIntervalTimestamp(timestamp: string, interval: string): string {
    const date = new Date(timestamp);
    
    switch (interval) {
      case '1m':
        date.setSeconds(0, 0);
        break;
      case '5m':
        date.setMinutes(Math.floor(date.getMinutes() / 5) * 5, 0, 0);
        break;
      case '1h':
        date.setMinutes(0, 0, 0);
        break;
      case '1d':
        date.setHours(0, 0, 0, 0);
        break;
    }
    
    return date.toISOString();
  }

  compressHistoricalData(data: any[]): any[] {
    if (!this.config.enableTickCompression) return data;

    // Simple compression: remove redundant data points
    const compressed = [];
    let lastValue = null;
    
    for (const item of data) {
      if (!lastValue || this.hasSignificantChange(lastValue, item)) {
        compressed.push(item);
        lastValue = item;
      }
    }

    return compressed;
  }

  private hasSignificantChange(prev: any, current: any): boolean {
    const priceChange = Math.abs(current.ltp - prev.ltp) / prev.ltp;
    const volumeChange = Math.abs(current.volume - prev.volume) / Math.max(prev.volume, 1);
    
    return priceChange > 0.001 || volumeChange > 0.1; // 0.1% price or 10% volume change
  }

  getCompressionStats(): {
    bufferedTicks: number;
    intervals: string[];
    compressionEnabled: boolean;
  } {
    const bufferedTicks = Array.from(this.aggregationBuffers.values())
      .reduce((sum, buffer) => sum + buffer.length, 0);

    return {
      bufferedTicks,
      intervals: this.config.aggregationIntervals,
      compressionEnabled: this.config.enableTickCompression
    };
  }
}
