
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, DollarSign, TrendingUp, Users, Activity } from "lucide-react";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface AccumulationData {
  timestamp: number;
  price: number;
  volume: number;
  buyVolume: number;
  sellVolume: number;
  blockDeals: number;
  bulkDeals: number;
  institutionalFlow: number;
}

interface SmartMoneySignal {
  type: 'ACCUMULATION' | 'DISTRIBUTION' | 'MARKUP' | 'MARKDOWN';
  strength: number;
  confidence: number;
  timestamp: number;
  description: string;
}

interface AccumulationTrackerProps {
  onBack?: () => void;
  symbol?: string;
  accumulationData?: AccumulationData[];
}

export const AccumulationTracker = ({ 
  onBack, 
  symbol = "RELIANCE",
  accumulationData = []
}: AccumulationTrackerProps) => {
  const [wycoffPhase, setWycoffPhase] = useState<string>('NEUTRAL');
  const [smartMoneySignals, setSmartMoneySignals] = useState<SmartMoneySignal[]>([]);
  const [accumulationMetrics, setAccumulationMetrics] = useState({
    accumulationIndex: 0,
    distributionIndex: 0,
    smartMoneyFlow: 0,
    institutionalActivity: 0,
    volumeProfile: 0
  });

  // Real Wyckoff Accumulation/Distribution Analysis
  const analyzeWycoffPhase = (data: AccumulationData[]) => {
    if (data.length < 20) return 'NEUTRAL';

    const recent = data.slice(-20);
    const priceChange = (recent[recent.length - 1].price - recent[0].price) / recent[0].price;
    const volumeProfile = calculateVolumeProfile(recent);
    const smartMoneyFlow = calculateSmartMoneyFlow(recent);
    
    // Phase A: Stopping Action
    if (Math.abs(priceChange) < 0.02 && volumeProfile > 1.5) {
      return smartMoneyFlow > 0 ? 'ACCUMULATION_PHASE_A' : 'DISTRIBUTION_PHASE_A';
    }
    
    // Phase B: Building Cause
    if (Math.abs(priceChange) < 0.05 && volumeProfile > 1.2) {
      return smartMoneyFlow > 0 ? 'ACCUMULATION_PHASE_B' : 'DISTRIBUTION_PHASE_B';
    }
    
    // Phase C: Test/Spring
    if (priceChange < -0.03 && volumeProfile < 0.8 && smartMoneyFlow > 0) {
      return 'ACCUMULATION_PHASE_C';
    }
    
    // Phase D: Evidence of Demand
    if (priceChange > 0.02 && volumeProfile > 1.3 && smartMoneyFlow > 0) {
      return 'ACCUMULATION_PHASE_D';
    }
    
    // Phase E: Markup
    if (priceChange > 0.05 && smartMoneyFlow > 0) {
      return 'MARKUP_PHASE';
    }
    
    return 'NEUTRAL';
  };

  // Calculate Volume Profile (Current vs Average)
  const calculateVolumeProfile = (data: AccumulationData[]) => {
    if (data.length === 0) return 0;
    
    const currentVolume = data[data.length - 1].volume;
    const avgVolume = data.reduce((sum, d) => sum + d.volume, 0) / data.length;
    
    return avgVolume > 0 ? currentVolume / avgVolume : 0;
  };

  // Calculate Smart Money Flow using Price-Volume Divergence
  const calculateSmartMoneyFlow = (data: AccumulationData[]) => {
    if (data.length < 10) return 0;
    
    let smartFlow = 0;
    
    for (let i = 1; i < data.length; i++) {
      const priceChange = data[i].price - data[i - 1].price;
      const volumeRatio = data[i].volume / data[i - 1].volume;
      
      // On Balance Volume concept
      if (priceChange > 0) {
        smartFlow += data[i].volume;
      } else if (priceChange < 0) {
        smartFlow -= data[i].volume;
      }
      
      // Institutional flow consideration
      smartFlow += data[i].institutionalFlow * 0.5;
      
      // Block deals weight
      smartFlow += data[i].blockDeals * 1000;
    }
    
    return smartFlow / data.length;
  };

  // Real Accumulation Index Calculation
  const calculateAccumulationIndex = (data: AccumulationData[]) => {
    if (data.length < 5) return 0;
    
    let accIndex = 0;
    
    for (let i = 1; i < data.length; i++) {
      const priceChange = data[i].price - data[i - 1].price;
      const volume = data[i].volume;
      const buyPressure = data[i].buyVolume / (data[i].buyVolume + data[i].sellVolume);
      
      // Accumulation occurs when:
      // 1. Price is stable or rising slightly with high volume
      // 2. Buy volume > Sell volume
      // 3. Institutional buying detected
      
      if (priceChange >= 0 && buyPressure > 0.6) {
        accIndex += volume * buyPressure;
      } else if (priceChange < 0 && buyPressure > 0.7) {
        // Smart money buying on dips
        accIndex += volume * buyPressure * 1.5;
      }
      
      // Subtract for distribution signs
      if (priceChange <= 0 && buyPressure < 0.4) {
        accIndex -= volume * (1 - buyPressure);
      }
    }
    
    return accIndex / data.length;
  };

  // Calculate Distribution Index
  const calculateDistributionIndex = (data: AccumulationData[]) => {
    if (data.length < 5) return 0;
    
    let distIndex = 0;
    
    for (let i = 1; i < data.length; i++) {
      const priceChange = data[i].price - data[i - 1].price;
      const volume = data[i].volume;
      const sellPressure = data[i].sellVolume / (data[i].buyVolume + data[i].sellVolume);
      
      // Distribution occurs when:
      // 1. Price is rising but with high selling pressure
      // 2. Sell volume > Buy volume on up moves
      // 3. Institutional selling detected
      
      if (priceChange > 0 && sellPressure > 0.6) {
        // Selling into strength
        distIndex += volume * sellPressure * 1.5;
      } else if (priceChange >= 0 && sellPressure > 0.5) {
        distIndex += volume * sellPressure;
      }
      
      // Subtract for accumulation signs
      if (priceChange < 0 && sellPressure < 0.4) {
        distIndex -= volume * (1 - sellPressure);
      }
    }
    
    return distIndex / data.length;
  };

  // Detect Smart Money Signals
  const detectSmartMoneySignals = (data: AccumulationData[]) => {
    if (data.length < 10) return [];
    
    const signals: SmartMoneySignal[] = [];
    const lookback = 5;
    
    for (let i = lookback; i < data.length; i++) {
      const window = data.slice(i - lookback, i + 1);
      const current = data[i];
      
      const priceChange = (current.price - window[0].price) / window[0].price;
      const volumeRatio = current.volume / (window.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / lookback);
      const buyRatio = current.buyVolume / (current.buyVolume + current.sellVolume);
      
      // Accumulation Signal
      if (Math.abs(priceChange) < 0.02 && volumeRatio > 1.5 && buyRatio > 0.65) {
        signals.push({
          type: 'ACCUMULATION',
          strength: Math.min(100, volumeRatio * buyRatio * 50),
          confidence: Math.min(95, volumeRatio * 30 + buyRatio * 50),
          timestamp: current.timestamp,
          description: 'High volume accumulation with strong buying pressure'
        });
      }
      
      // Distribution Signal
      if (priceChange > 0.01 && volumeRatio > 1.3 && buyRatio < 0.35) {
        signals.push({
          type: 'DISTRIBUTION',
          strength: Math.min(100, volumeRatio * (1 - buyRatio) * 50),
          confidence: Math.min(95, volumeRatio * 30 + (1 - buyRatio) * 50),
          timestamp: current.timestamp,
          description: 'Distribution detected - selling into strength'
        });
      }
      
      // Markup Signal
      if (priceChange > 0.03 && buyRatio > 0.6 && current.institutionalFlow > 0) {
        signals.push({
          type: 'MARKUP',
          strength: Math.min(100, priceChange * 500 + buyRatio * 30),
          confidence: Math.min(90, priceChange * 300 + current.institutionalFlow * 20),
          timestamp: current.timestamp,
          description: 'Markup phase - strong institutional buying with price momentum'
        });
      }
      
      // Markdown Signal
      if (priceChange < -0.03 && buyRatio < 0.4 && current.institutionalFlow < 0) {
        signals.push({
          type: 'MARKDOWN',
          strength: Math.min(100, Math.abs(priceChange) * 500 + (1 - buyRatio) * 30),
          confidence: Math.min(90, Math.abs(priceChange) * 300 + Math.abs(current.institutionalFlow) * 20),
          timestamp: current.timestamp,
          description: 'Markdown phase - institutional selling with price decline'
        });
      }
    }
    
    return signals.slice(-10); // Return latest 10 signals
  };

  useEffect(() => {
    if (accumulationData.length > 0) {
      const phase = analyzeWycoffPhase(accumulationData);
      const signals = detectSmartMoneySignals(accumulationData);
      
      const accIndex = calculateAccumulationIndex(accumulationData);
      const distIndex = calculateDistributionIndex(accumulationData);
      const smartFlow = calculateSmartMoneyFlow(accumulationData);
      const volProfile = calculateVolumeProfile(accumulationData);
      
      // Calculate institutional activity
      const institutionalActivity = accumulationData.slice(-10).reduce((sum, d) => 
        sum + Math.abs(d.institutionalFlow) + d.blockDeals + d.bulkDeals, 0) / 10;
      
      setWycoffPhase(phase);
      setSmartMoneySignals(signals);
      setAccumulationMetrics({
        accumulationIndex: accIndex,
        distributionIndex: distIndex,
        smartMoneyFlow: smartFlow,
        institutionalActivity,
        volumeProfile: volProfile
      });
    }
  }, [accumulationData]);

  const chartData = accumulationData.slice(-30).map(d => ({
    time: new Date(d.timestamp).toLocaleDateString(),
    price: d.price,
    volume: d.volume,
    buyVolume: d.buyVolume,
    sellVolume: d.sellVolume,
    institutionalFlow: d.institutionalFlow
  }));

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">Accumulation Tracker - {symbol}</h2>
          </div>
        )}

        {/* Wyckoff Phase Analysis */}
        <Card className="glassmorphism-card border-2 border-purple-500/30">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Smart Money Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <Badge variant="outline" className="text-purple-400 border-purple-400">
                  {wycoffPhase.replace(/_/g, ' ')}
                </Badge>
                <div className="text-sm text-trading-muted mt-1">Wyckoff Phase</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-400">
                  {accumulationMetrics.accumulationIndex.toFixed(0)}
                </div>
                <div className="text-sm text-trading-muted">Accumulation Index</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-400">
                  {accumulationMetrics.distributionIndex.toFixed(0)}
                </div>
                <div className="text-sm text-trading-muted">Distribution Index</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-light">
                  {accumulationMetrics.institutionalActivity.toFixed(0)}
                </div>
                <div className="text-sm text-trading-muted">Institutional Activity</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Price and Volume Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light">Price vs Smart Money Flow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {chartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                      <YAxis yAxisId="price" stroke="#9ca3af" fontSize={12} />
                      <YAxis yAxisId="flow" orientation="right" stroke="#9ca3af" fontSize={12} />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1f2937', 
                          border: '1px solid #374151',
                          borderRadius: '8px' 
                        }}
                      />
                      <Line 
                        yAxisId="price"
                        type="monotone" 
                        dataKey="price" 
                        stroke="#3b82f6" 
                        strokeWidth={2} 
                        dot={false}
                        name="Price"
                      />
                      <Line 
                        yAxisId="flow"
                        type="monotone" 
                        dataKey="institutionalFlow" 
                        stroke="#10b981" 
                        strokeWidth={2} 
                        dot={false}
                        name="Institutional Flow"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center text-trading-muted">
                    <div className="text-center">
                      <DollarSign className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No accumulation data available</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light">Buy vs Sell Volume</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {chartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                      <YAxis stroke="#9ca3af" fontSize={12} />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1f2937', 
                          border: '1px solid #374151',
                          borderRadius: '8px' 
                        }}
                      />
                      <Bar dataKey="buyVolume" fill="#10b981" name="Buy Volume" />
                      <Bar dataKey="sellVolume" fill="#ef4444" name="Sell Volume" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center text-trading-muted">
                    <div className="text-center">
                      <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No volume data available</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Smart Money Signals */}
        <Card className="glassmorphism-card">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Smart Money Signals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64 w-full">
              {smartMoneySignals.length > 0 ? (
                <div className="space-y-2">
                  {smartMoneySignals.map((signal, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded">
                      <div className="flex items-center space-x-3">
                        <Badge 
                          variant="outline" 
                          className={
                            signal.type === 'ACCUMULATION' ? 'text-green-400 border-green-400' :
                            signal.type === 'DISTRIBUTION' ? 'text-red-400 border-red-400' :
                            signal.type === 'MARKUP' ? 'text-blue-400 border-blue-400' :
                            'text-orange-400 border-orange-400'
                          }
                        >
                          {signal.type}
                        </Badge>
                        <div>
                          <div className="text-sm text-trading-light">{signal.description}</div>
                          <div className="text-xs text-trading-muted">
                            {new Date(signal.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-purple-400">
                          Confidence: {signal.confidence.toFixed(0)}%
                        </div>
                        <div className="text-xs text-trading-muted">
                          Strength: {signal.strength.toFixed(0)}/100
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-trading-muted">
                  <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No smart money signals detected</p>
                  <p className="text-xs mt-1">Signals will appear with sufficient volume data</p>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Accumulation Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light text-sm">Smart Money Flow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  accumulationMetrics.smartMoneyFlow > 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {accumulationMetrics.smartMoneyFlow > 0 ? '+' : ''}
                  {accumulationMetrics.smartMoneyFlow.toFixed(0)}
                </div>
                <div className="text-xs text-trading-muted mt-1">
                  {accumulationMetrics.smartMoneyFlow > 0 ? 'Net Buying' : 'Net Selling'}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light text-sm">Volume Profile</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  accumulationMetrics.volumeProfile > 1 ? 'text-blue-400' : 'text-gray-400'
                }`}>
                  {accumulationMetrics.volumeProfile.toFixed(2)}x
                </div>
                <div className="text-xs text-trading-muted mt-1">
                  vs Average Volume
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light text-sm">Phase Strength</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {Math.abs(accumulationMetrics.accumulationIndex - accumulationMetrics.distributionIndex).toFixed(0)}
                </div>
                <div className="text-xs text-trading-muted mt-1">
                  {accumulationMetrics.accumulationIndex > accumulationMetrics.distributionIndex ? 
                    'Accumulation Dominant' : 'Distribution Dominant'}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  );
};
