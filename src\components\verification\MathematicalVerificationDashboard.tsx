
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, XCircle, AlertTriangle, Calculator, TrendingUp, Shield } from "lucide-react";
import { MathematicalVerificationService, MathematicalAudit, VerificationResult } from "@/services/MathematicalVerificationService";

export const MathematicalVerificationDashboard: React.FC = () => {
  const [audit, setAudit] = useState<MathematicalAudit | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runAudit = async () => {
    setIsRunning(true);
    try {
      const result = await MathematicalVerificationService.runFullAudit();
      setAudit(result);
    } catch (error) {
      console.error('Audit failed:', error);
    }
    setIsRunning(false);
  };

  useEffect(() => {
    // Run audit on component mount
    runAudit();
  }, []);

  const getStatusIcon = (status: VerificationResult['status']) => {
    switch (status) {
      case 'PASS':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'FAIL':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'WARNING':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: VerificationResult['status']) => {
    const variants = {
      PASS: 'default',
      FAIL: 'destructive',
      WARNING: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status]} className="ml-2">
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center text-trading-light">
            <Calculator className="h-6 w-6 mr-2" />
            Mathematical Functions Verification
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm text-trading-muted">
              Comprehensive audit of all mathematical calculations and algorithms
            </div>
            <Button 
              onClick={runAudit} 
              disabled={isRunning}
              className="interactive-hover"
            >
              {isRunning ? 'Running Audit...' : 'Run Full Audit'}
            </Button>
          </div>

          {audit && (
            <div className="grid grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-trading-dark rounded-lg">
                <div className="text-2xl font-bold text-trading-light">{audit.totalTests}</div>
                <div className="text-sm text-trading-muted">Total Tests</div>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded-lg">
                <div className="text-2xl font-bold text-green-400">{audit.passed}</div>
                <div className="text-sm text-trading-muted">Passed</div>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded-lg">
                <div className="text-2xl font-bold text-red-400">{audit.failed}</div>
                <div className="text-sm text-trading-muted">Failed</div>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded-lg">
                <div className="text-2xl font-bold text-yellow-400">{audit.warnings}</div>
                <div className="text-sm text-trading-muted">Warnings</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {audit && (
        <Tabs defaultValue="summary" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="technical">Technical Indicators</TabsTrigger>
            <TabsTrigger value="financial">Financial Calculations</TabsTrigger>
          </TabsList>

          <TabsContent value="summary">
            <Card className="glass-card">
              <CardContent className="pt-6">
                <Alert className={`mb-4 ${audit.failed === 0 ? 'border-green-500' : 'border-red-500'}`}>
                  <AlertDescription>
                    <pre className="whitespace-pre-wrap text-sm">{audit.summary}</pre>
                  </AlertDescription>
                </Alert>

                <div className="space-y-3">
                  {audit.results.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded-lg">
                      <div className="flex items-center">
                        {getStatusIcon(result.status)}
                        <span className="ml-3 font-medium text-trading-light">{result.function}</span>
                        {getStatusBadge(result.status)}
                      </div>
                      <div className="text-sm text-trading-muted max-w-md text-right">
                        {result.message}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="technical">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center text-trading-light">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Technical Indicators Verification
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {audit.results
                    .filter(r => ['SMA Calculation', 'EMA Calculation', 'RSI Calculation', 'Volatility Calculation', 'Correlation Calculation'].includes(r.function))
                    .map((result, index) => (
                      <div key={index} className="p-4 bg-trading-dark rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getStatusIcon(result.status)}
                            <span className="ml-3 font-medium text-trading-light">{result.function}</span>
                          </div>
                          {getStatusBadge(result.status)}
                        </div>
                        <div className="text-sm text-trading-muted mb-2">{result.message}</div>
                        {result.expectedValue !== undefined && (
                          <div className="text-xs text-trading-muted">
                            Expected: {result.expectedValue} | Actual: {result.actualValue}
                          </div>
                        )}
                        {result.error && (
                          <div className="text-xs text-red-400 mt-2">Error: {result.error}</div>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="financial">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center text-trading-light">
                  <Shield className="h-5 w-5 mr-2" />
                  Financial & Risk Calculations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {audit.results
                    .filter(r => ['P&L Calculation', 'Risk Calculation (VaR)', 'Black-Scholes Options Pricing', 'Data Integrity Check'].includes(r.function))
                    .map((result, index) => (
                      <div key={index} className="p-4 bg-trading-dark rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            {getStatusIcon(result.status)}
                            <span className="ml-3 font-medium text-trading-light">{result.function}</span>
                          </div>
                          {getStatusBadge(result.status)}
                        </div>
                        <div className="text-sm text-trading-muted mb-2">{result.message}</div>
                        {result.expectedValue !== undefined && (
                          <div className="text-xs text-trading-muted">
                            Expected: {result.expectedValue} | Actual: {result.actualValue}
                          </div>
                        )}
                        {result.error && (
                          <div className="text-xs text-red-400 mt-2">Error: {result.error}</div>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};
