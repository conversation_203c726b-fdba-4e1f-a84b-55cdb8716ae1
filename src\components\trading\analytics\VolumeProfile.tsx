
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Activity, TrendingUp, Target, Volume2 } from "lucide-react";

interface VolumeProfileData {
  priceLevel: number;
  volume: number;
  buyVolume: number;
  sellVolume: number;
  percentage: number;
  isPOC: boolean; // Point of Control
  isVAH: boolean; // Value Area High
  isVAL: boolean; // Value Area Low
}

interface VolumeProfileProps {
  symbol?: string;
  period?: string;
  tradeData?: Array<{ price: number; volume: number; timestamp: number; side?: 'buy' | 'sell' }>;
}

export const VolumeProfile = ({ 
  symbol = "NIFTY", 
  period = "Session",
  tradeData = []
}: VolumeProfileProps) => {
  const [profileData, setProfileData] = useState<VolumeProfileData[]>([]);
  const [pocPrice, setPocPrice] = useState<number>(0);
  const [valueAreaHigh, setValueAreaHigh] = useState<number>(0);
  const [valueAreaLow, setValueAreaLow] = useState<number>(0);
  const [totalVolume, setTotalVolume] = useState<number>(0);

  // Real Volume Profile calculation using Price-Volume analysis
  const calculateVolumeProfile = (trades: Array<{ price: number; volume: number; side?: 'buy' | 'sell' }>) => {
    if (trades.length === 0) return [];

    // Determine price range and tick size
    const prices = trades.map(t => t.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    
    // Use dynamic tick size based on price range and instrument
    let tickSize: number;
    if (priceRange > 1000) {
      tickSize = 5; // ₹5 tick for large ranges
    } else if (priceRange > 100) {
      tickSize = 1; // ₹1 tick for medium ranges
    } else {
      tickSize = 0.25; // ₹0.25 tick for small ranges
    }

    // Create price level buckets
    const volumeMap = new Map<number, { volume: number; buyVolume: number; sellVolume: number }>();
    
    trades.forEach(trade => {
      const priceLevel = Math.floor(trade.price / tickSize) * tickSize;
      const existing = volumeMap.get(priceLevel) || { volume: 0, buyVolume: 0, sellVolume: 0 };
      
      existing.volume += trade.volume;
      if (trade.side === 'buy') {
        existing.buyVolume += trade.volume;
      } else if (trade.side === 'sell') {
        existing.sellVolume += trade.volume;
      } else {
        // If side is unknown, distribute 50-50
        existing.buyVolume += trade.volume * 0.5;
        existing.sellVolume += trade.volume * 0.5;
      }
      
      volumeMap.set(priceLevel, existing);
    });

    // Calculate total volume
    const totalVol = Array.from(volumeMap.values()).reduce((sum, v) => sum + v.volume, 0);
    setTotalVolume(totalVol);

    // Convert to array and sort by price
    let profileArray = Array.from(volumeMap.entries())
      .map(([priceLevel, data]) => ({
        priceLevel,
        volume: data.volume,
        buyVolume: data.buyVolume,
        sellVolume: data.sellVolume,
        percentage: totalVol > 0 ? (data.volume / totalVol) * 100 : 0,
        isPOC: false,
        isVAH: false,
        isVAL: false
      }))
      .sort((a, b) => a.priceLevel - b.priceLevel);

    if (profileArray.length === 0) return [];

    // Find Point of Control (price level with highest volume)
    const pocIndex = profileArray.reduce((maxIndex, current, index) => 
      current.volume > profileArray[maxIndex].volume ? index : maxIndex, 0);
    
    profileArray[pocIndex].isPOC = true;
    setPocPrice(profileArray[pocIndex].priceLevel);

    // Calculate Value Area (70% of total volume around POC)
    const valueAreaThreshold = totalVol * 0.7;
    let currentValueAreaVolume = profileArray[pocIndex].volume;
    let expandUp = pocIndex + 1;
    let expandDown = pocIndex - 1;

    // Expand value area symmetrically around POC
    while (currentValueAreaVolume < valueAreaThreshold && (expandUp < profileArray.length || expandDown >= 0)) {
      const upVolume = expandUp < profileArray.length ? profileArray[expandUp].volume : 0;
      const downVolume = expandDown >= 0 ? profileArray[expandDown].volume : 0;

      if (upVolume >= downVolume && expandUp < profileArray.length) {
        currentValueAreaVolume += upVolume;
        expandUp++;
      } else if (expandDown >= 0) {
        currentValueAreaVolume += downVolume;
        expandDown--;
      } else {
        break;
      }
    }

    // Mark Value Area High and Low
    if (expandUp - 1 < profileArray.length && expandUp - 1 > pocIndex) {
      profileArray[expandUp - 1].isVAH = true;
      setValueAreaHigh(profileArray[expandUp - 1].priceLevel);
    }
    if (expandDown + 1 >= 0 && expandDown + 1 < pocIndex) {
      profileArray[expandDown + 1].isVAL = true;
      setValueAreaLow(profileArray[expandDown + 1].priceLevel);
    }

    return profileArray;
  };

  // Calculate Volume Imbalance at each price level
  const calculateVolumeImbalance = (profile: VolumeProfileData[]) => {
    return profile.map(level => ({
      ...level,
      imbalance: level.buyVolume + level.sellVolume > 0 
        ? (level.buyVolume - level.sellVolume) / (level.buyVolume + level.sellVolume) 
        : 0
    }));
  };

  // Detect significant volume clusters
  const detectVolumeClusters = (profile: VolumeProfileData[], minClusterSize: number = 3) => {
    const clusters = [];
    let currentCluster = [];
    
    const avgVolume = profile.reduce((sum, p) => sum + p.volume, 0) / profile.length;
    const highVolumeThreshold = avgVolume * 1.5;
    
    for (let i = 0; i < profile.length; i++) {
      if (profile[i].volume > highVolumeThreshold) {
        currentCluster.push(profile[i]);
      } else {
        if (currentCluster.length >= minClusterSize) {
          clusters.push(currentCluster);
        }
        currentCluster = [];
      }
    }
    
    if (currentCluster.length >= minClusterSize) {
      clusters.push(currentCluster);
    }
    
    return clusters;
  };

  useEffect(() => {
    if (tradeData && tradeData.length > 0) {
      const calculatedProfile = calculateVolumeProfile(tradeData);
      setProfileData(calculatedProfile);
    }
  }, [tradeData, symbol, period]);

  const maxVolume = profileData.length > 0 ? Math.max(...profileData.map(p => p.volume)) : 0;
  const buyVolumeTotal = profileData.reduce((sum, p) => sum + p.buyVolume, 0);
  const sellVolumeTotal = profileData.reduce((sum, p) => sum + p.sellVolume, 0);

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-trading-light flex items-center">
            <Volume2 className="h-5 w-5 mr-2" />
            Volume Profile - {symbol}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Key Levels */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
            <div className="text-center p-3 bg-trading-dark rounded">
              <div className="text-sm text-trading-muted">Point of Control</div>
              <div className="text-lg font-bold text-yellow-400">
                {pocPrice > 0 ? `₹${pocPrice.toFixed(2)}` : '--'}
              </div>
              <div className="text-xs text-trading-muted">Highest Volume</div>
            </div>
            <div className="text-center p-3 bg-trading-dark rounded">
              <div className="text-sm text-trading-muted">Value Area High</div>
              <div className="text-lg font-bold text-red-400">
                {valueAreaHigh > 0 ? `₹${valueAreaHigh.toFixed(2)}` : '--'}
              </div>
              <div className="text-xs text-trading-muted">70% Volume Upper</div>
            </div>
            <div className="text-center p-3 bg-trading-dark rounded">
              <div className="text-sm text-trading-muted">Value Area Low</div>
              <div className="text-lg font-bold text-green-400">
                {valueAreaLow > 0 ? `₹${valueAreaLow.toFixed(2)}` : '--'}
              </div>
              <div className="text-xs text-trading-muted">70% Volume Lower</div>
            </div>
          </div>

          {/* Volume Profile Chart */}
          <div className="h-80">
            {profileData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={profileData}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, bottom: 20, left: 60 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    type="number" 
                    stroke="#9ca3af" 
                    fontSize={12}
                    tickFormatter={(value) => `${(value / 1000).toFixed(1)}K`}
                  />
                  <YAxis 
                    type="category" 
                    dataKey="priceLevel" 
                    stroke="#9ca3af" 
                    fontSize={12}
                    tickFormatter={(value) => `₹${value.toFixed(0)}`}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                    formatter={(value: number, name: string) => [
                      `${(value / 1000).toFixed(1)}K`,
                      name === 'volume' ? 'Volume' : name
                    ]}
                    labelFormatter={(value) => `Price: ₹${parseFloat(value).toFixed(2)}`}
                  />
                  <Bar dataKey="volume" name="Volume">
                    {profileData.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={
                          entry.isPOC ? '#fbbf24' : 
                          entry.isVAH ? '#ef4444' : 
                          entry.isVAL ? '#10b981' : 
                          '#3b82f6'
                        }
                        opacity={0.8}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-trading-muted">
                <div className="text-center">
                  <Volume2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No trade data available</p>
                  <p className="text-xs mt-1">Connect to data feed to see volume profile</p>
                </div>
              </div>
            )}
          </div>

          {/* Profile Controls */}
          <div className="flex gap-2 flex-wrap">
            <Button size="sm" variant="outline" disabled={profileData.length === 0}>
              <Activity className="h-4 w-4 mr-1" />
              Session Profile
            </Button>
            <Button size="sm" variant="outline" disabled={profileData.length === 0}>
              <TrendingUp className="h-4 w-4 mr-1" />
              Weekly Profile
            </Button>
            <Button size="sm" variant="outline" disabled={profileData.length === 0}>
              <Target className="h-4 w-4 mr-1" />
              Custom Range
            </Button>
          </div>

          {/* Volume Statistics */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
            <div className="text-center">
              <div className="text-trading-muted">Total Volume</div>
              <div className="font-medium text-trading-light">
                {totalVolume > 0 ? `${(totalVolume / 1000000).toFixed(2)}M` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-trading-muted">Buy Volume</div>
              <div className="font-medium text-green-400">
                {buyVolumeTotal > 0 ? `${(buyVolumeTotal / 1000000).toFixed(2)}M` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-trading-muted">Sell Volume</div>
              <div className="font-medium text-red-400">
                {sellVolumeTotal > 0 ? `${(sellVolumeTotal / 1000000).toFixed(2)}M` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-trading-muted">Max Level</div>
              <div className="font-medium text-yellow-400">
                {maxVolume > 0 ? `${(maxVolume / 1000).toFixed(1)}K` : '--'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
