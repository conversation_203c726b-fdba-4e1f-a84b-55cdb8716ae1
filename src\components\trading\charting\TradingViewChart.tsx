
import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON><PERSON>, ColorType, IChartApi, ISeriesApi, CandlestickData, HistogramData } from 'lightweight-charts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export interface ChartData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

interface TradingViewChartProps {
  data: ChartData[];
  symbol: string;
  height?: number;
}

export const TradingViewChart: React.FC<TradingViewChartProps> = ({ 
  data, 
  symbol, 
  height = 400 
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!chartContainerRef.current) return;

    try {
      // Create the chart
      const chart = createChart(chartContainerRef.current, {
        layout: {
          background: { type: ColorType.Solid, color: '#1a1a1a' },
          textColor: '#d1d5db',
        },
        grid: {
          vertLines: { color: '#374151' },
          horzLines: { color: '#374151' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#485563',
        },
        timeScale: {
          borderColor: '#485563',
        },
        width: chartContainerRef.current.clientWidth,
        height: height,
      });

      chartRef.current = chart;

      // Add candlestick series
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      });

      candlestickSeriesRef.current = candlestickSeries;

      // Add volume series
      const volumeSeries = chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });

      volumeSeriesRef.current = volumeSeries;

      // Process and set data
      if (data && data.length > 0) {
        const candlestickData: CandlestickData[] = data.map(item => ({
          time: item.time,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
        }));

        const volumeData: HistogramData[] = data
          .filter(item => item.volume !== undefined)
          .map(item => ({
            time: item.time,
            value: item.volume!,
            color: item.close >= item.open ? '#26a69a40' : '#ef535040',
          }));

        candlestickSeries.setData(candlestickData);
        if (volumeData.length > 0) {
          volumeSeries.setData(volumeData);
        }
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chart) {
          chart.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);
      
      setIsLoading(false);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chart) {
          chart.remove();
        }
      };
    } catch (error) {
      console.error('Error creating TradingView chart:', error);
      setIsLoading(false);
    }
  }, [data, height]);

  // Update data when props change
  useEffect(() => {
    if (!candlestickSeriesRef.current || !data || data.length === 0) return;

    try {
      const candlestickData: CandlestickData[] = data.map(item => ({
        time: item.time,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }));

      const volumeData: HistogramData[] = data
        .filter(item => item.volume !== undefined)
        .map(item => ({
          time: item.time,
          value: item.volume!,
          color: item.close >= item.open ? '#26a69a40' : '#ef535040',
        }));

      candlestickSeriesRef.current.setData(candlestickData);
      
      if (volumeSeriesRef.current && volumeData.length > 0) {
        volumeSeriesRef.current.setData(volumeData);
      }
    } catch (error) {
      console.error('Error updating chart data:', error);
    }
  }, [data]);

  if (isLoading) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">{symbol} Chart</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center" style={{ height: height }}>
            <div className="text-trading-muted">Loading chart...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <CardTitle className="text-trading-light">{symbol} Chart</CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={chartContainerRef} 
          style={{ width: '100%', height: height }}
          className="rounded"
        />
      </CardContent>
    </Card>
  );
};
