
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Brain, TrendingUp, MessageSquare, Globe, BarChart3 } from "lucide-react";
import { Line<PERSON>hart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface SentimentData {
  timestamp: number;
  newsScore: number;
  socialScore: number;
  technicalScore: number;
  fundamentalScore: number;
  overallSentiment: number;
  volumeWeighted: boolean;
}

interface NewsItem {
  title: string;
  sentiment: number;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  source: string;
  timestamp: number;
}

interface SentimentEngineProps {
  onBack?: () => void;
  symbol?: string;
  sentimentData?: SentimentData[];
  newsData?: NewsItem[];
}

export const SentimentEngine = ({ 
  onBack, 
  symbol = "RELIANCE",
  sentimentData = [],
  newsData = []
}: SentimentEngineProps) => {
  const [aggregatedSentiment, setAggregatedSentiment] = useState({
    overall: 0,
    trend: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL',
    confidence: 0,
    volatility: 0
  });

  const [sentimentBreakdown, setSentimentBreakdown] = useState({
    news: 0,
    social: 0,
    technical: 0,
    fundamental: 0
  });

  // Real sentiment aggregation algorithm
  const calculateAggregatedSentiment = (data: SentimentData[]) => {
    if (data.length === 0) return { overall: 50, trend: 'NEUTRAL' as const, confidence: 0, volatility: 0 };

    const recent = data.slice(-10); // Last 10 data points
    
    // Weighted average calculation with recency bias
    let weightedSum = 0;
    let weightSum = 0;
    
    recent.forEach((item, index) => {
      const recencyWeight = Math.pow(1.1, index); // More weight to recent data
      const volumeWeight = item.volumeWeighted ? 1.2 : 1.0;
      const weight = recencyWeight * volumeWeight;
      
      weightedSum += item.overallSentiment * weight;
      weightSum += weight;
    });
    
    const overall = weightSum > 0 ? weightedSum / weightSum : 50;
    
    // Trend calculation based on sentiment direction
    const recentTrend = recent.slice(-3);
    const trendSlope = calculateTrendSlope(recentTrend.map(d => d.overallSentiment));
    
    let trend: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    if (overall > 60 && trendSlope > 0) trend = 'BULLISH';
    else if (overall < 40 && trendSlope < 0) trend = 'BEARISH';
    
    // Confidence based on consistency
    const sentimentStdDev = calculateStandardDeviation(recent.map(d => d.overallSentiment));
    const confidence = Math.max(0, Math.min(100, 100 - sentimentStdDev * 2));
    
    // Volatility calculation
    const volatility = sentimentStdDev;
    
    return { overall, trend, confidence, volatility };
  };

  // Calculate sentiment breakdown by source
  const calculateSentimentBreakdown = (data: SentimentData[]) => {
    if (data.length === 0) return { news: 50, social: 50, technical: 50, fundamental: 50 };

    const recent = data.slice(-5);
    
    const news = recent.reduce((sum, d) => sum + d.newsScore, 0) / recent.length;
    const social = recent.reduce((sum, d) => sum + d.socialScore, 0) / recent.length;
    const technical = recent.reduce((sum, d) => sum + d.technicalScore, 0) / recent.length;
    const fundamental = recent.reduce((sum, d) => sum + d.fundamentalScore, 0) / recent.length;
    
    return { news, social, technical, fundamental };
  };

  // Real NLP-like sentiment scoring for news
  const analyzeNewsSentiment = (newsItems: NewsItem[]) => {
    if (newsItems.length === 0) return 50;

    const recent = newsItems.slice(-20); // Last 20 news items
    
    let weightedScore = 0;
    let totalWeight = 0;
    
    recent.forEach((item) => {
      // Weight by impact and recency
      const impactWeight = item.impact === 'HIGH' ? 3 : item.impact === 'MEDIUM' ? 2 : 1;
      const recencyWeight = Math.exp(-(Date.now() - item.timestamp) / (24 * 60 * 60 * 1000)); // Decay over days
      const weight = impactWeight * recencyWeight;
      
      weightedScore += item.sentiment * weight;
      totalWeight += weight;
    });
    
    return totalWeight > 0 ? weightedScore / totalWeight : 50;
  };

  // Sentiment momentum calculation
  const calculateSentimentMomentum = (data: SentimentData[]) => {
    if (data.length < 5) return 0;

    const recent = data.slice(-5);
    const older = data.slice(-10, -5);
    
    if (older.length === 0) return 0;
    
    const recentAvg = recent.reduce((sum, d) => sum + d.overallSentiment, 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + d.overallSentiment, 0) / older.length;
    
    return recentAvg - olderAvg;
  };

  // Helper functions
  const calculateTrendSlope = (values: number[]) => {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = n * (n - 1) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, x) => sum + x * y, 0);
    const sumXX = n * (n - 1) * (2 * n - 1) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  };

  const calculateStandardDeviation = (values: number[]) => {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  };

  // Sentiment volatility bands
  const calculateSentimentBands = (data: SentimentData[], periods: number = 10) => {
    if (data.length < periods) return data;
    
    return data.map((item, index) => {
      if (index < periods - 1) return item;
      
      const window = data.slice(index - periods + 1, index + 1);
      const avg = window.reduce((sum, d) => sum + d.overallSentiment, 0) / periods;
      const stdDev = calculateStandardDeviation(window.map(d => d.overallSentiment));
      
      return {
        ...item,
        upperBand: avg + stdDev * 2,
        lowerBand: avg - stdDev * 2,
        average: avg
      };
    });
  };

  useEffect(() => {
    if (sentimentData.length > 0) {
      const aggregated = calculateAggregatedSentiment(sentimentData);
      const breakdown = calculateSentimentBreakdown(sentimentData);
      
      setAggregatedSentiment(aggregated);
      setSentimentBreakdown(breakdown);
    }
  }, [sentimentData]);

  const chartData = sentimentData.slice(-30).map(d => ({
    time: new Date(d.timestamp).toLocaleDateString(),
    overall: d.overallSentiment,
    news: d.newsScore,
    social: d.socialScore,
    technical: d.technicalScore,
    fundamental: d.fundamentalScore
  }));

  const sentimentDistribution = [
    { name: 'News', value: sentimentBreakdown.news, fill: '#3b82f6' },
    { name: 'Social', value: sentimentBreakdown.social, fill: '#10b981' },
    { name: 'Technical', value: sentimentBreakdown.technical, fill: '#f59e0b' },
    { name: 'Fundamental', value: sentimentBreakdown.fundamental, fill: '#ef4444' }
  ];

  const radarData = [
    { subject: 'News', score: sentimentBreakdown.news, fullMark: 100 },
    { subject: 'Social', score: sentimentBreakdown.social, fullMark: 100 },
    { subject: 'Technical', score: sentimentBreakdown.technical, fullMark: 100 },
    { subject: 'Fundamental', score: sentimentBreakdown.fundamental, fullMark: 100 }
  ];

  const momentum = calculateSentimentMomentum(sentimentData);

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">Sentiment Engine - {symbol}</h2>
          </div>
        )}

        {/* Overall Sentiment Dashboard */}
        <Card className="glassmorphism-card border-2 border-purple-500/30">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Brain className="h-5 w-5 mr-2" />
              Multi-Source Sentiment Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${
                  aggregatedSentiment.overall > 60 ? 'text-green-400' :
                  aggregatedSentiment.overall < 40 ? 'text-red-400' : 'text-yellow-400'
                }`}>
                  {aggregatedSentiment.overall.toFixed(0)}
                </div>
                <div className="text-sm text-trading-muted">Overall Score</div>
              </div>
              <div className="text-center">
                <Badge 
                  variant="outline" 
                  className={
                    aggregatedSentiment.trend === 'BULLISH' ? 'text-green-400 border-green-400' :
                    aggregatedSentiment.trend === 'BEARISH' ? 'text-red-400 border-red-400' :
                    'text-yellow-400 border-yellow-400'
                  }
                >
                  {aggregatedSentiment.trend}
                </Badge>
                <div className="text-sm text-trading-muted mt-1">Trend</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {aggregatedSentiment.confidence.toFixed(0)}%
                </div>
                <div className="text-sm text-trading-muted">Confidence</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-bold ${
                  momentum > 0 ? 'text-green-400' : momentum < 0 ? 'text-red-400' : 'text-gray-400'
                }`}>
                  {momentum > 0 ? '+' : ''}{momentum.toFixed(1)}
                </div>
                <div className="text-sm text-trading-muted">Momentum</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-400">
                  {aggregatedSentiment.volatility.toFixed(1)}
                </div>
                <div className="text-sm text-trading-muted">Volatility</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sentiment Analysis Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sources">Sources</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="news">News Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light">Sentiment Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    {chartData.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={chartData}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                          <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                          <YAxis domain={[0, 100]} stroke="#9ca3af" fontSize={12} />
                          <Tooltip 
                            contentStyle={{ 
                              backgroundColor: '#1f2937', 
                              border: '1px solid #374151',
                              borderRadius: '8px' 
                            }}
                          />
                          <Line 
                            type="monotone" 
                            dataKey="overall" 
                            stroke="#8b5cf6" 
                            strokeWidth={3} 
                            dot={false}
                            name="Overall Sentiment"
                          />
                          <Line 
                            type="monotone" 
                            dataKey="news" 
                            stroke="#3b82f6" 
                            strokeWidth={2} 
                            dot={false}
                            name="News"
                          />
                          <Line 
                            type="monotone" 
                            dataKey="social" 
                            stroke="#10b981" 
                            strokeWidth={2} 
                            dot={false}
                            name="Social"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="h-full flex items-center justify-center text-trading-muted">
                        <div className="text-center">
                          <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No sentiment data available</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light">Sentiment Radar</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={radarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" tick={{ fontSize: 12 }} />
                        <PolarRadiusAxis angle={0} domain={[0, 100]} tick={false} />
                        <Radar
                          name="Sentiment"
                          dataKey="score"
                          stroke="#8b5cf6"
                          fill="#8b5cf6"
                          fillOpacity={0.3}
                          strokeWidth={2}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sources" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    News Sentiment
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    sentimentBreakdown.news > 60 ? 'text-green-400' :
                    sentimentBreakdown.news < 40 ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {sentimentBreakdown.news.toFixed(0)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Financial News</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Social Sentiment
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    sentimentBreakdown.social > 60 ? 'text-green-400' :
                    sentimentBreakdown.social < 40 ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {sentimentBreakdown.social.toFixed(0)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Social Media</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Technical Sentiment
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    sentimentBreakdown.technical > 60 ? 'text-green-400' :
                    sentimentBreakdown.technical < 40 ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {sentimentBreakdown.technical.toFixed(0)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Technical Analysis</div>
                </CardContent>
              </Card>

              <Card className="glassmorphism-card">
                <CardHeader>
                  <CardTitle className="text-trading-light text-sm flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Fundamental Sentiment
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className={`text-2xl font-bold ${
                    sentimentBreakdown.fundamental > 60 ? 'text-green-400' :
                    sentimentBreakdown.fundamental < 40 ? 'text-red-400' : 'text-yellow-400'
                  }`}>
                    {sentimentBreakdown.fundamental.toFixed(0)}
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Fundamental Data</div>
                </CardContent>
              </Card>
            </div>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Source Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={sentimentDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value.toFixed(0)}`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {sentimentDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light">Multi-Source Sentiment Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  {chartData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                        <YAxis domain={[0, 100]} stroke="#9ca3af" fontSize={12} />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1f2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px' 
                          }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="news" 
                          stroke="#3b82f6" 
                          strokeWidth={2} 
                          dot={false}
                          name="News Sentiment"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="social" 
                          stroke="#10b981" 
                          strokeWidth={2} 
                          dot={false}
                          name="Social Sentiment"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="technical" 
                          stroke="#f59e0b" 
                          strokeWidth={2} 
                          dot={false}
                          name="Technical Sentiment"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="fundamental" 
                          stroke="#ef4444" 
                          strokeWidth={2} 
                          dot={false}
                          name="Fundamental Sentiment"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center text-trading-muted">
                      <div className="text-center">
                        <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No trend data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="news" className="space-y-6">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Recent News Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64 w-full">
                  {newsData.length > 0 ? (
                    <div className="space-y-2">
                      {newsData.slice(-10).map((news, index) => (
                        <div key={index} className="p-3 bg-trading-dark rounded">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="text-sm text-trading-light font-medium">
                                {news.title}
                              </div>
                              <div className="text-xs text-trading-muted mt-1">
                                {news.source} • {new Date(news.timestamp).toLocaleString()}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2 ml-3">
                              <Badge 
                                variant="outline" 
                                className={
                                  news.sentiment > 60 ? 'text-green-400 border-green-400' :
                                  news.sentiment < 40 ? 'text-red-400 border-red-400' :
                                  'text-yellow-400 border-yellow-400'
                                }
                              >
                                {news.sentiment.toFixed(0)}
                              </Badge>
                              <Badge 
                                variant="outline"
                                className={
                                  news.impact === 'HIGH' ? 'text-red-400 border-red-400' :
                                  news.impact === 'MEDIUM' ? 'text-yellow-400 border-yellow-400' :
                                  'text-gray-400 border-gray-400'
                                }
                              >
                                {news.impact}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-trading-muted">
                      <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No news data available</p>
                      <p className="text-xs mt-1">Connect to news feed for sentiment analysis</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
