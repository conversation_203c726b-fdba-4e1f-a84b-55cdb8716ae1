
export interface AlertConfig {
  enabled: boolean;
  soundEnabled: boolean;
  pushEnabled: boolean;
  emailEnabled: boolean;
}

export interface Alert {
  id: string;
  type: 'PRICE' | 'VOLUME' | 'TECHNICAL' | 'NEWS';
  symbol: string;
  condition: string;
  value: number;
  triggered: boolean;
  timestamp: string;
}

export interface SystemAlert {
  component: string;
  status: 'ONLINE' | 'OFFLINE' | 'ERROR' | 'WARNING';
  message: string;
}

export interface ExecutionAlert {
  orderId: string;
  symbol: string;
  status: string;
  quantity: number;
  price: number;
  pnl?: number;
  reason?: string;
  message: string;
}

export interface SignalAlert {
  symbol: string;
  action: string;
  price: number;
  confidence: number;
  agent: string;
  reasoning: string;
  strategy: string;
}

export interface RiskAlert {
  type: 'POSITION_RISK' | 'PORTFOLIO_RISK' | 'MARGIN_CALL' | 'DRAWDOWN';
  level: number;
  threshold: number;
  recommendations?: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  message: string;
}

export class AlertService {
  private config: AlertConfig = {
    enabled: true,
    soundEnabled: true,
    pushEnabled: false,
    emailEnabled: false
  };
  
  private alerts: Alert[] = [];
  private alertHistory: Alert[] = [];

  static async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  addAlert(alert: Omit<Alert, 'id' | 'triggered' | 'timestamp'>): string {
    const newAlert: Alert = {
      ...alert,
      id: Date.now().toString(),
      triggered: false,
      timestamp: new Date().toISOString()
    };
    
    this.alerts.push(newAlert);
    return newAlert.id;
  }

  removeAlert(id: string): boolean {
    const index = this.alerts.findIndex(a => a.id === id);
    if (index !== -1) {
      this.alerts.splice(index, 1);
      return true;
    }
    return false;
  }

  triggerAlert(id: string): void {
    const alert = this.alerts.find(a => a.id === id);
    if (alert && !alert.triggered) {
      alert.triggered = true;
      this.alertHistory.push({ ...alert });
      
      if (this.config.soundEnabled) {
        this.playAlertSound();
      }
      
      if (this.config.pushEnabled) {
        this.sendPushNotification(alert);
      }
    }
  }

  private playAlertSound(): void {
    try {
      const audio = new Audio('/alert-sound.mp3');
      audio.play().catch(console.error);
    } catch (error) {
      console.error('Error playing alert sound:', error);
    }
  }

  private sendPushNotification(alert: Alert): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`Trading Alert: ${alert.symbol}`, {
        body: `${alert.type}: ${alert.condition}`,
        icon: '/trading-icon.png'
      });
    }
  }

  getPendingAlerts(): Alert[] {
    return this.alerts.filter(a => !a.triggered);
  }

  getAlertHistory(): Alert[] {
    return this.alertHistory;
  }

  updateConfig(config: Partial<AlertConfig>): void {
    this.config = { ...this.config, ...config };
  }

  getConfig(): AlertConfig {
    return { ...this.config };
  }

  async sendSystemAlert(alert: SystemAlert): Promise<void> {
    const message = `[${alert.component}] ${alert.status}: ${alert.message}`;
    console.log('System Alert:', message);
    
    if (this.config.pushEnabled) {
      this.sendPushNotification({
        type: 'TECHNICAL',
        symbol: 'SYSTEM',
        condition: alert.status,
        value: 0,
        id: Date.now().toString(),
        triggered: true,
        timestamp: new Date().toISOString()
      });
    }
  }

  async sendExecutionAlert(alert: ExecutionAlert): Promise<void> {
    console.log('Execution Alert:', alert.message);
    
    if (this.config.pushEnabled) {
      this.sendPushNotification({
        type: 'TECHNICAL',
        symbol: alert.symbol,
        condition: alert.status,
        value: 0,
        id: Date.now().toString(),
        triggered: true,
        timestamp: new Date().toISOString()
      });
    }
  }

  async sendSignalAlert(alert: SignalAlert): Promise<void> {
    const message = `[${alert.strategy}] ${alert.symbol} ${alert.action} signal with ${alert.confidence}% confidence`;
    console.log('Signal Alert:', message);
    
    if (this.config.pushEnabled) {
      this.sendPushNotification({
        type: 'TECHNICAL',
        symbol: alert.symbol,
        condition: alert.action,
        value: alert.confidence,
        id: Date.now().toString(),
        triggered: true,
        timestamp: new Date().toISOString()
      });
    }
  }

  async sendRiskAlert(alert: RiskAlert): Promise<void> {
    console.log('Risk Alert:', alert.message);
    
    if (this.config.pushEnabled) {
      this.sendPushNotification({
        type: 'TECHNICAL',
        symbol: 'RISK',
        condition: alert.type,
        value: 0,
        id: Date.now().toString(),
        triggered: true,
        timestamp: new Date().toISOString()
      });
    }
  }
}
