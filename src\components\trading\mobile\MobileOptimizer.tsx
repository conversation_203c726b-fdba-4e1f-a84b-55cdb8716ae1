
import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Smartphone, Tablet, Monitor, Zap, TrendingUp } from "lucide-react";

interface MobileStat {
  label: string;
  value: string;
  color: string;
}

interface Position {
  symbol: string;
  qty: number;
  pnl: string;
  pnlPct: string;
}

interface Signal {
  symbol: string;
  action: string;
  confidence: string;
  agent: string;
}

interface MobileOptimizerProps {
  mobileStats?: MobileStat[];
  positions?: Position[];
  signals?: Signal[];
  isLoading?: boolean;
}

export const MobileOptimizer = ({
  mobileStats = [],
  positions = [],
  signals = [],
  isLoading = false
}: MobileOptimizerProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < 768);
      setOrientation(window.innerWidth < window.innerHeight ? 'portrait' : 'landscape');
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  const quickActions = [
    { label: "Buy", color: "bg-green-600", icon: "📈" },
    { label: "Sell", color: "bg-red-600", icon: "📉" },
    { label: "Positions", color: "bg-blue-600", icon: "📊" },
    { label: "Alerts", color: "bg-yellow-600", icon: "🔔" }
  ];

  const defaultStats = [
    { label: "P&L", value: "--", color: "text-trading-muted" },
    { label: "Positions", value: "--", color: "text-trading-muted" },
    { label: "Alerts", value: "--", color: "text-trading-muted" }
  ];

  const statsToShow = mobileStats.length > 0 ? mobileStats : defaultStats;

  return (
    <div className={`space-y-4 ${isMobile ? 'p-2' : 'p-6'}`}>
      {/* Device Detection */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className={isMobile ? 'pb-2' : ''}>
          <CardTitle className={`text-trading-light flex items-center ${isMobile ? 'text-sm' : ''}`}>
            {isMobile ? <Smartphone className="h-4 w-4 mr-2" /> : <Monitor className="h-5 w-5 mr-2" />}
            {isMobile ? 'Mobile View' : 'Desktop View'}
            <Badge className="ml-2 bg-blue-600">
              {orientation.charAt(0).toUpperCase() + orientation.slice(1)}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className={isMobile ? 'p-3' : ''}>
          <div className={`grid ${isMobile ? 'grid-cols-3' : 'grid-cols-4'} gap-2`}>
            {statsToShow.map((stat, index) => (
              <div key={index} className="text-center">
                <div className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold ${stat.color}`}>
                  {isLoading ? '...' : stat.value}
                </div>
                <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-trading-muted`}>
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Mobile Quick Actions */}
      {isMobile && (
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-3">
            <div className="grid grid-cols-4 gap-2">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  className={`${action.color} h-16 flex flex-col items-center justify-center text-white hover:opacity-80`}
                  disabled={isLoading}
                >
                  <div className="text-lg mb-1">{action.icon}</div>
                  <div className="text-xs">{action.label}</div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Responsive Tabs */}
      <Tabs defaultValue="positions" className="space-y-4">
        <TabsList className={`grid w-full ${isMobile ? 'grid-cols-2' : 'grid-cols-4'} bg-trading-darker`}>
          <TabsTrigger value="positions" className={`data-[state=active]:bg-trading-accent ${isMobile ? 'text-xs' : ''}`}>
            <TrendingUp className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} mr-1`} />
            Positions
          </TabsTrigger>
          <TabsTrigger value="signals" className={`data-[state=active]:bg-trading-accent ${isMobile ? 'text-xs' : ''}`}>
            <Zap className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} mr-1`} />
            Signals
          </TabsTrigger>
          {!isMobile && (
            <>
              <TabsTrigger value="charts" className="data-[state=active]:bg-trading-accent">
                Charts
              </TabsTrigger>
              <TabsTrigger value="analytics" className="data-[state=active]:bg-trading-accent">
                Analytics
              </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="positions">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader className={isMobile ? 'pb-2' : ''}>
              <CardTitle className={`text-trading-light ${isMobile ? 'text-sm' : ''}`}>
                Live Positions
              </CardTitle>
            </CardHeader>
            <CardContent className={isMobile ? 'p-3' : ''}>
              {positions.length > 0 ? (
                <div className="space-y-2">
                  {positions.map((position, index) => (
                    <div key={index} className={`flex items-center justify-between p-2 bg-trading-dark rounded border border-trading-border ${isMobile ? 'text-sm' : ''}`}>
                      <div>
                        <div className="font-medium text-trading-light">{position.symbol}</div>
                        <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-trading-muted`}>
                          Qty: {position.qty}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-green-400 font-medium">{position.pnl}</div>
                        <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-green-400`}>
                          {position.pnlPct}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">
                    {isLoading ? 'Loading positions...' : 'No active positions'}
                  </p>
                  <p className="text-sm text-trading-muted mt-1">
                    {isLoading ? 'Fetching position data...' : 'Your trading positions will appear here'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="signals">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader className={isMobile ? 'pb-2' : ''}>
              <CardTitle className={`text-trading-light ${isMobile ? 'text-sm' : ''}`}>
                AI Signals
              </CardTitle>
            </CardHeader>
            <CardContent className={isMobile ? 'p-3' : ''}>
              {signals.length > 0 ? (
                <div className="space-y-2">
                  {signals.map((signal, index) => (
                    <div key={index} className={`flex items-center justify-between p-2 bg-trading-dark rounded border border-trading-border ${isMobile ? 'text-sm' : ''}`}>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={signal.action === "BUY" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {signal.action}
                        </Badge>
                        <div>
                          <div className="font-medium text-trading-light">{signal.symbol}</div>
                          <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-trading-muted`}>
                            {signal.agent} • {signal.confidence}
                          </div>
                        </div>
                      </div>
                      <Button size={isMobile ? "sm" : "default"} variant="outline" disabled={isLoading}>
                        Trade
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">
                    {isLoading ? 'Loading signals...' : 'No active signals'}
                  </p>
                  <p className="text-sm text-trading-muted mt-1">
                    {isLoading ? 'Fetching AI signals...' : 'AI trading signals will appear here'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {!isMobile && (
          <>
            <TabsContent value="charts">
              <Card className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <CardTitle className="text-trading-light">Trading Charts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 bg-trading-dark rounded flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-trading-muted">Chart Component (Desktop Only)</div>
                      <div className="text-sm text-trading-muted mt-1">Real-time charts will load here</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics">
              <Card className="bg-trading-darker border-trading-border">
                <CardHeader>
                  <CardTitle className="text-trading-light">Performance Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-trading-muted">--</div>
                      <div className="text-sm text-trading-muted">Total Returns</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-trading-muted">--</div>
                      <div className="text-sm text-trading-muted">Sharpe Ratio</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* Mobile-specific footer navigation */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 bg-trading-darker border-t border-trading-border p-2">
          <div className="grid grid-cols-5 gap-1">
            {[
              { label: "Home", icon: "🏠" },
              { label: "Trade", icon: "📊" },
              { label: "Portfolio", icon: "💼" },
              { label: "Alerts", icon: "🔔" },
              { label: "More", icon: "⋯" }
            ].map((item, index) => (
              <Button
                key={index}
                variant="ghost"
                className="flex flex-col items-center justify-center h-12 text-xs text-trading-light"
                disabled={isLoading}
              >
                <div className="text-lg mb-1">{item.icon}</div>
                {item.label}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
