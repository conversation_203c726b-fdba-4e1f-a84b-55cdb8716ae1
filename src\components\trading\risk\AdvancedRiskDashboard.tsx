
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Shield, 
  AlertTriangle, 
  TrendingDown, 
  BarChart3,
  Target,
  Activity
} from "lucide-react";
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

interface RiskMetrics {
  portfolioValue: number;
  dayPnL: number;
  totalRisk: number;
  maxDrawdown: number;
  sharpeRatio: number;
  riskUtilization: number;
  varDaily: number;
  stressTest: number;
}

interface PositionRisk {
  symbol: string;
  exposure: number;
  risk: number;
  riskPercent: number;
  status: 'low' | 'moderate' | 'high';
}

interface SectorExposure {
  sector: string;
  value: number;
  amount: number;
  color: string;
}

interface DrawdownData {
  date: string;
  value: number;
}

interface AdvancedRiskDashboardProps {
  riskMetrics?: RiskMetrics;
  positionRisks?: PositionRisk[];
  sectorExposure?: SectorExposure[];
  drawdownHistory?: DrawdownData[];
  isLoading?: boolean;
}

export const AdvancedRiskDashboard = ({
  riskMetrics,
  positionRisks = [],
  sectorExposure = [],
  drawdownHistory = [],
  isLoading = false
}: AdvancedRiskDashboardProps) => {
  const [riskProfile, setRiskProfile] = useState("moderate");

  const getRiskColor = (status: string) => {
    switch (status) {
      case "low": return "text-green-400 border-green-400";
      case "moderate": return "text-yellow-400 border-yellow-400";
      case "high": return "text-red-400 border-red-400";
      default: return "text-gray-400 border-gray-400";
    }
  };

  const renderEmptyState = (title: string, description: string, icon: React.ReactNode) => (
    <div className="text-center py-12">
      {icon}
      <div className="text-lg text-trading-muted mb-2">
        {isLoading ? 'Loading...' : title}
      </div>
      <div className="text-sm text-trading-muted">
        {isLoading ? 'Fetching risk data...' : description}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Risk Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Risk Management Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-400">
                {riskMetrics ? `₹${Math.abs(riskMetrics.dayPnL).toLocaleString()}` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Day P&L</div>
              {riskMetrics && (
                <div className="text-xs text-red-400">
                  {((riskMetrics.dayPnL / riskMetrics.portfolioValue) * 100).toFixed(2)}%
                </div>
              )}
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">
                {riskMetrics ? `₹${riskMetrics.totalRisk.toLocaleString()}` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Total Risk</div>
              {riskMetrics && (
                <div className="text-xs text-yellow-400">
                  {((riskMetrics.totalRisk / riskMetrics.portfolioValue) * 100).toFixed(1)}%
                </div>
              )}
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-400">
                {riskMetrics ? `${riskMetrics.maxDrawdown}%` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Max Drawdown</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {riskMetrics ? riskMetrics.sharpeRatio : '--'}
              </div>
              <div className="text-sm text-trading-muted">Sharpe Ratio</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {riskMetrics ? `₹${Math.abs(riskMetrics.varDaily).toLocaleString()}` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Daily VaR</div>
              <div className="text-xs text-blue-400">95% Confidence</div>
            </div>
          </div>

          {riskMetrics && (
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-trading-light">Risk Utilization</span>
                <span className="text-yellow-400">{riskMetrics.riskUtilization}%</span>
              </div>
              <Progress value={riskMetrics.riskUtilization} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="positions" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="positions" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Position Risk
          </TabsTrigger>
          <TabsTrigger value="sector" className="data-[state=active]:bg-trading-accent">
            <BarChart3 className="h-4 w-4 mr-2" />
            Sector Risk
          </TabsTrigger>
          <TabsTrigger value="drawdown" className="data-[state=active]:bg-trading-accent">
            <TrendingDown className="h-4 w-4 mr-2" />
            Drawdown
          </TabsTrigger>
          <TabsTrigger value="stress" className="data-[state=active]:bg-trading-accent">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Stress Test
          </TabsTrigger>
        </TabsList>

        <TabsContent value="positions">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Position Risk Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {positionRisks.length > 0 ? (
                <div className="space-y-4">
                  {positionRisks.map((position, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-center">
                        <div>
                          <div className="font-semibold text-trading-light">{position.symbol}</div>
                          <Badge variant="outline" className={`text-xs ${getRiskColor(position.status)}`}>
                            {position.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Exposure</div>
                          <div className="text-trading-light">₹{position.exposure.toLocaleString()}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Risk Amount</div>
                          <div className="text-red-400">₹{position.risk.toLocaleString()}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-trading-muted">Risk %</div>
                          <div className="text-red-400">{position.riskPercent}%</div>
                        </div>
                        <div>
                          <Progress value={position.riskPercent * 10} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Position Risk Data',
                  'Position risk analysis will appear when positions are available',
                  <Target className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sector">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Sector Exposure</CardTitle>
              </CardHeader>
              <CardContent>
                {sectorExposure.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={sectorExposure}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={120}
                        dataKey="value"
                      >
                        {sectorExposure.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [`${value}%`, 'Exposure']} />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  renderEmptyState(
                    'No Sector Data',
                    'Sector exposure chart will appear when portfolio data is available',
                    <BarChart3 className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                  )
                )}
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Sector Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                {sectorExposure.length > 0 ? (
                  <div className="space-y-3">
                    {sectorExposure.map((sector, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: sector.color }}
                          ></div>
                          <span className="text-trading-light">{sector.sector}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-trading-light">{sector.value}%</div>
                          <div className="text-xs text-trading-muted">₹{sector.amount.toLocaleString()}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  renderEmptyState(
                    'No Sector Breakdown',
                    'Sector breakdown will appear when portfolio data is available',
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 text-trading-muted opacity-50" />
                  )
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="drawdown">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Drawdown Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              {drawdownHistory.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={drawdownHistory}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="date" stroke="#9ca3af" />
                    <YAxis stroke="#9ca3af" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                      formatter={(value: number) => [`${value}%`, 'Drawdown']}
                    />
                    <Bar dataKey="value" fill="#ef4444" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                renderEmptyState(
                  'No Drawdown Data',
                  'Drawdown analysis will appear when historical performance data is available',
                  <TrendingDown className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stress">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Stress Testing Scenarios</CardTitle>
            </CardHeader>
            <CardContent>
              {renderEmptyState(
                'No Stress Test Results',
                'Stress testing scenarios will appear when portfolio analysis is available',
                <AlertTriangle className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
