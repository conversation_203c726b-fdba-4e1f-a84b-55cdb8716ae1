
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, TrendingUp, Target, AlertCircle, CheckCircle } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';

interface PriceData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface BreakoutPattern {
  type: 'RESISTANCE_BREAKOUT' | 'SUPPORT_BREAKDOWN' | 'RETEST_SUCCESS' | 'RETEST_FAILURE';
  level: number;
  timestamp: number;
  strength: number;
  volume_confirmation: boolean;
  probability: number;
}

interface BreakoutRetestDetectorProps {
  onBack?: () => void;
  symbol?: string;
  priceData?: PriceData[];
}

export const BreakoutRetestDetector = ({ 
  onBack, 
  symbol = "NIFTY",
  priceData = []
}: BreakoutRetestDetectorProps) => {
  const [patterns, setPatterns] = useState<BreakoutPattern[]>([]);
  const [keyLevels, setKeyLevels] = useState<{ support: number[], resistance: number[] }>({ support: [], resistance: [] });
  const [currentAnalysis, setCurrentAnalysis] = useState({
    trend: 'NEUTRAL',
    nearestLevel: 0,
    breakoutProbability: 0,
    retestProbability: 0
  });

  // Real breakout detection algorithm
  const detectBreakouts = (data: PriceData[]) => {
    if (data.length < 50) return [];

    const detectedPatterns: BreakoutPattern[] = [];
    const window = 20; // Look-back period for level identification
    
    for (let i = window; i < data.length - 5; i++) {
      const current = data[i];
      const previous = data.slice(i - window, i);
      const future = data.slice(i + 1, i + 6); // Look ahead for confirmation
      
      // Find potential resistance levels
      const highs = previous.map(p => p.high).sort((a, b) => b - a);
      const resistanceLevel = findLevel(highs.slice(0, 5)); // Top 5 highs
      
      // Find potential support levels  
      const lows = previous.map(p => p.low).sort((a, b) => a - b);
      const supportLevel = findLevel(lows.slice(0, 5)); // Bottom 5 lows
      
      // Check for resistance breakout
      if (resistanceLevel && current.close > resistanceLevel && 
          previous.slice(-5).every(p => p.high <= resistanceLevel)) {
        
        const volumeConfirmation = current.volume > calculateAverageVolume(previous.slice(-10));
        const strength = calculateBreakoutStrength(current, resistanceLevel, previous);
        
        detectedPatterns.push({
          type: 'RESISTANCE_BREAKOUT',
          level: resistanceLevel,
          timestamp: current.timestamp,
          strength,
          volume_confirmation: volumeConfirmation,
          probability: calculateBreakoutProbability(current, resistanceLevel, previous, true)
        });
      }
      
      // Check for support breakdown
      if (supportLevel && current.close < supportLevel && 
          previous.slice(-5).every(p => p.low >= supportLevel)) {
        
        const volumeConfirmation = current.volume > calculateAverageVolume(previous.slice(-10));
        const strength = calculateBreakoutStrength(current, supportLevel, previous);
        
        detectedPatterns.push({
          type: 'SUPPORT_BREAKDOWN',
          level: supportLevel,
          timestamp: current.timestamp,
          strength,
          volume_confirmation: volumeConfirmation,
          probability: calculateBreakoutProbability(current, supportLevel, previous, false)
        });
      }
      
      // Check for retest patterns
      const recentBreakout = detectedPatterns.slice(-1)[0];
      if (recentBreakout && Math.abs(current.timestamp - recentBreakout.timestamp) <= 5) {
        const isRetestSuccess = checkRetestSuccess(current, recentBreakout, future);
        
        detectedPatterns.push({
          type: isRetestSuccess ? 'RETEST_SUCCESS' : 'RETEST_FAILURE',
          level: recentBreakout.level,
          timestamp: current.timestamp,
          strength: recentBreakout.strength,
          volume_confirmation: current.volume > calculateAverageVolume(previous.slice(-5)),
          probability: isRetestSuccess ? 85 : 25
        });
      }
    }
    
    return detectedPatterns.slice(-10); // Return latest 10 patterns
  };

  // Helper function to find consensus level from price array
  const findLevel = (prices: number[]): number | null => {
    if (prices.length < 3) return null;
    
    const tolerance = 0.005; // 0.5% tolerance
    const groups: number[][] = [];
    
    prices.forEach(price => {
      let addedToGroup = false;
      for (let group of groups) {
        if (Math.abs(price - group[0]) / group[0] <= tolerance) {
          group.push(price);
          addedToGroup = true;
          break;
        }
      }
      if (!addedToGroup) {
        groups.push([price]);
      }
    });
    
    // Find the group with most prices (strongest level)
    const strongestGroup = groups.reduce((max, group) => 
      group.length > max.length ? group : max, []);
    
    return strongestGroup.length >= 2 ? 
      strongestGroup.reduce((a, b) => a + b, 0) / strongestGroup.length : null;
  };

  // Calculate average volume
  const calculateAverageVolume = (data: PriceData[]) => {
    return data.reduce((sum, p) => sum + p.volume, 0) / data.length;
  };

  // Calculate breakout strength based on price movement and volume
  const calculateBreakoutStrength = (current: PriceData, level: number, previous: PriceData[]) => {
    const priceMove = Math.abs(current.close - level) / level;
    const volumeRatio = current.volume / calculateAverageVolume(previous.slice(-10));
    const momentum = calculateMomentum(previous.slice(-5));
    
    const strength = (priceMove * 30) + (volumeRatio * 25) + (momentum * 45);
    return Math.min(100, Math.max(0, strength));
  };

  // Calculate momentum using rate of change
  const calculateMomentum = (data: PriceData[]) => {
    if (data.length < 2) return 0;
    
    const roc = (data[data.length - 1].close - data[0].close) / data[0].close;
    return Math.abs(roc) * 100;
  };

  // Calculate breakout probability
  const calculateBreakoutProbability = (
    current: PriceData, 
    level: number, 
    previous: PriceData[], 
    isUpward: boolean
  ) => {
    const volumeConfirmation = current.volume > calculateAverageVolume(previous.slice(-10));
    const priceDistance = Math.abs(current.close - level) / level;
    const momentum = calculateMomentum(previous.slice(-5));
    
    let probability = 40; // Base probability
    
    if (volumeConfirmation) probability += 25;
    if (priceDistance > 0.01) probability += 15; // Significant move
    if (momentum > 2) probability += 20; // Strong momentum
    
    return Math.min(95, Math.max(5, probability));
  };

  // Check if retest is successful
  const checkRetestSuccess = (current: PriceData, breakout: BreakoutPattern, future: PriceData[]) => {
    if (future.length === 0) return false;
    
    const tolerance = 0.002; // 0.2% tolerance
    const isNearLevel = Math.abs(current.close - breakout.level) / breakout.level <= tolerance;
    
    if (!isNearLevel) return false;
    
    // Check if price bounces in the expected direction
    if (breakout.type === 'RESISTANCE_BREAKOUT') {
      return future.some(f => f.close > current.close);
    } else {
      return future.some(f => f.close < current.close);
    }
  };

  // Identify key support and resistance levels
  const identifyKeyLevels = (data: PriceData[]) => {
    if (data.length < 20) return { support: [], resistance: [] };
    
    const highs = data.map(p => p.high);
    const lows = data.map(p => p.low);
    
    // Find pivot highs and lows
    const pivotHighs = findPivotPoints(highs, true);
    const pivotLows = findPivotPoints(lows, false);
    
    // Cluster similar levels
    const resistanceLevels = clusterLevels(pivotHighs);
    const supportLevels = clusterLevels(pivotLows);
    
    return {
      support: supportLevels.slice(0, 5), // Top 5 support levels
      resistance: resistanceLevels.slice(0, 5) // Top 5 resistance levels
    };
  };

  // Find pivot points (local maxima/minima)
  const findPivotPoints = (prices: number[], isHigh: boolean) => {
    const pivots: number[] = [];
    const lookback = 5;
    
    for (let i = lookback; i < prices.length - lookback; i++) {
      const window = prices.slice(i - lookback, i + lookback + 1);
      const center = window[lookback];
      
      if (isHigh) {
        if (window.every(p => p <= center)) {
          pivots.push(center);
        }
      } else {
        if (window.every(p => p >= center)) {
          pivots.push(center);
        }
      }
    }
    
    return pivots;
  };

  // Cluster similar price levels
  const clusterLevels = (levels: number[]) => {
    const tolerance = 0.01; // 1% tolerance
    const clusters: number[] = [];
    
    levels.sort((a, b) => a - b);
    
    let currentCluster = [levels[0]];
    
    for (let i = 1; i < levels.length; i++) {
      if (Math.abs(levels[i] - currentCluster[0]) / currentCluster[0] <= tolerance) {
        currentCluster.push(levels[i]);
      } else {
        if (currentCluster.length >= 2) {
          clusters.push(currentCluster.reduce((a, b) => a + b, 0) / currentCluster.length);
        }
        currentCluster = [levels[i]];
      }
    }
    
    if (currentCluster.length >= 2) {
      clusters.push(currentCluster.reduce((a, b) => a + b, 0) / currentCluster.length);
    }
    
    return clusters;
  };

  useEffect(() => {
    if (priceData.length > 0) {
      const detectedPatterns = detectBreakouts(priceData);
      const levels = identifyKeyLevels(priceData);
      
      setPatterns(detectedPatterns);
      setKeyLevels(levels);
      
      // Calculate current analysis
      const currentPrice = priceData[priceData.length - 1].close;
      const allLevels = [...levels.support, ...levels.resistance].sort((a, b) => 
        Math.abs(a - currentPrice) - Math.abs(b - currentPrice)
      );
      
      const nearestLevel = allLevels[0] || currentPrice;
      const trend = currentPrice > nearestLevel ? 'BULLISH' : 'BEARISH';
      
      setCurrentAnalysis({
        trend,
        nearestLevel,
        breakoutProbability: detectedPatterns.length > 0 ? 
          detectedPatterns[detectedPatterns.length - 1].probability : 50,
        retestProbability: 65 // Default retest probability
      });
    }
  }, [priceData]);

  const chartData = priceData.slice(-50).map(d => ({
    time: new Date(d.timestamp).toLocaleTimeString(),
    price: d.close,
    volume: d.volume
  }));

  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        {/* Header */}
        {onBack && (
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h2 className="text-xl font-bold text-trading-light">Breakout Retest Detector - {symbol}</h2>
          </div>
        )}

        {/* Current Analysis */}
        <Card className="glassmorphism-card">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Current Market Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <Badge variant="outline" className={
                  currentAnalysis.trend === 'BULLISH' ? 'text-green-400 border-green-400' : 
                  'text-red-400 border-red-400'
                }>
                  {currentAnalysis.trend}
                </Badge>
                <div className="text-sm text-trading-muted mt-1">Current Trend</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-light">
                  ₹{currentAnalysis.nearestLevel.toFixed(2)}
                </div>
                <div className="text-sm text-trading-muted">Nearest Level</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-400">
                  {currentAnalysis.breakoutProbability.toFixed(0)}%
                </div>
                <div className="text-sm text-trading-muted">Breakout Probability</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-400">
                  {currentAnalysis.retestProbability.toFixed(0)}%
                </div>
                <div className="text-sm text-trading-muted">Retest Probability</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Price Chart with Levels */}
        <Card className="glassmorphism-card">
          <CardHeader>
            <CardTitle className="text-trading-light">Price Action with Key Levels</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              {chartData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="time" stroke="#9ca3af" fontSize={12} />
                    <YAxis stroke="#9ca3af" fontSize={12} />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px' 
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="price" 
                      stroke="#3b82f6" 
                      strokeWidth={2} 
                      dot={false}
                    />
                    {keyLevels.resistance.map((level, index) => (
                      <ReferenceLine 
                        key={`resistance-${index}`}
                        y={level} 
                        stroke="#ef4444" 
                        strokeDasharray="5 5"
                        label={{ value: `R${index + 1}`, position: "right" }}
                      />
                    ))}
                    {keyLevels.support.map((level, index) => (
                      <ReferenceLine 
                        key={`support-${index}`}
                        y={level} 
                        stroke="#10b981" 
                        strokeDasharray="5 5"
                        label={{ value: `S${index + 1}`, position: "right" }}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-full flex items-center justify-center text-trading-muted">
                  <div className="text-center">
                    <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No price data available</p>
                    <p className="text-xs mt-1">Connect to data feed to detect breakouts</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Detected Patterns */}
        <Card className="glassmorphism-card">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Detected Breakout Patterns
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64 w-full">
              {patterns.length > 0 ? (
                <div className="space-y-2">
                  {patterns.map((pattern, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded">
                      <div className="flex items-center space-x-3">
                        {pattern.type.includes('SUCCESS') ? 
                          <CheckCircle className="h-4 w-4 text-green-400" /> :
                          pattern.type.includes('FAILURE') ?
                          <AlertCircle className="h-4 w-4 text-red-400" /> :
                          <TrendingUp className="h-4 w-4 text-blue-400" />
                        }
                        <div>
                          <div className="text-sm font-medium text-trading-light">
                            {pattern.type.replace(/_/g, ' ')}
                          </div>
                          <div className="text-xs text-trading-muted">
                            Level: ₹{pattern.level.toFixed(2)} | 
                            {pattern.volume_confirmation ? ' Volume Confirmed' : ' Low Volume'}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-blue-400">
                          {pattern.probability.toFixed(0)}%
                        </div>
                        <div className="text-xs text-trading-muted">
                          Strength: {pattern.strength.toFixed(0)}/100
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-trading-muted">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No breakout patterns detected</p>
                  <p className="text-xs mt-1">Patterns will appear with sufficient price data</p>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Key Levels Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light text-green-400">Support Levels</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {keyLevels.support.slice(0, 5).map((level, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm text-trading-muted">S{index + 1}</span>
                    <span className="text-sm font-medium text-trading-light">₹{level.toFixed(2)}</span>
                  </div>
                ))}
                {keyLevels.support.length === 0 && (
                  <div className="text-center text-trading-muted text-sm">No support levels identified</div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light text-red-400">Resistance Levels</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {keyLevels.resistance.slice(0, 5).map((level, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm text-trading-muted">R{index + 1}</span>
                    <span className="text-sm font-medium text-trading-light">₹{level.toFixed(2)}</span>
                  </div>
                ))}
                {keyLevels.resistance.length === 0 && (
                  <div className="text-center text-trading-muted text-sm">No resistance levels identified</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  );
};
