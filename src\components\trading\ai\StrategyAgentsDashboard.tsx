
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Brain, Users, Activity, TrendingUp, Zap, Target, DollarSign } from "lucide-react";
import { ALL_STRATEGIES } from "@/data/strategies";
import { StrategyAgent } from "./agents/StrategyAgent";

interface PerformanceData {
  trades: number;
  winRate: number;
  pnl: number;
  confidence: number;
}

interface StrategyAgentsDashboardProps {
  activeAgents?: number;
  totalSignals?: number;
  avgSuccessRate?: number;
  totalPnL?: number;
  strategyPerformance?: Record<string, PerformanceData>;
}

export const StrategyAgentsDashboard = ({
  activeAgents = 0,
  totalSignals = 0,
  avgSuccessRate = 0,
  totalPnL = 0,
  strategyPerformance = {}
}: StrategyAgentsDashboardProps) => {
  const [masterActive, setMasterActive] = useState(false);
  const [agentStates, setAgentStates] = useState(
    ALL_STRATEGIES.reduce((acc, strategy) => {
      acc[strategy.id] = false;
      return acc;
    }, {} as Record<string, boolean>)
  );

  const handleAgentToggle = (strategyId: string, active: boolean) => {
    setAgentStates(prev => ({ ...prev, [strategyId]: active }));
  };

  const handleMasterToggle = (active: boolean) => {
    setMasterActive(active);
    const newStates = ALL_STRATEGIES.reduce((acc, strategy) => {
      acc[strategy.id] = active;
      return acc;
    }, {} as Record<string, boolean>);
    setAgentStates(newStates);
  };

  const priceActionAgents = ALL_STRATEGIES.filter(s => s.category === 'Price Action');
  const optionsAgents = ALL_STRATEGIES.filter(s => s.category === 'Options');
  const swingAgents = ALL_STRATEGIES.filter(s => s.category === 'Swing');
  const intradayAgents = ALL_STRATEGIES.filter(s => s.category === 'Intraday');
  const scalpingAgents = ALL_STRATEGIES.filter(s => s.category === 'Scalping');

  return (
    <div className="space-y-6">
      {/* Master Control */}
      <Card className="bg-trading-darker border-trading-border border-2 border-blue-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-trading-light flex items-center">
              <Brain className="h-5 w-5 mr-2 text-blue-400" />
              Strategy Agent Command Center
            </CardTitle>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-trading-light">Master Control</span>
                <Switch checked={masterActive} onCheckedChange={handleMasterToggle} />
              </div>
              <Badge variant="outline" className={masterActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                {masterActive ? "All Active" : "All Inactive"}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{activeAgents}</div>
              <div className="text-sm text-trading-muted">Active Agents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{avgSuccessRate.toFixed(1)}%</div>
              <div className="text-sm text-trading-muted">Avg Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-trading-light">{totalSignals.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">Total Signals</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">₹{totalPnL.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">Total P&L</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy Agents by Category */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6 bg-trading-darker">
          <TabsTrigger value="all" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            All ({ALL_STRATEGIES.length})
          </TabsTrigger>
          <TabsTrigger value="price-action" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            Price Action ({priceActionAgents.length})
          </TabsTrigger>
          <TabsTrigger value="options" className="data-[state=active]:bg-trading-accent">
            <DollarSign className="h-4 w-4 mr-2" />
            Options ({optionsAgents.length})
          </TabsTrigger>
          <TabsTrigger value="swing" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Swing ({swingAgents.length})
          </TabsTrigger>
          <TabsTrigger value="intraday" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Intraday ({intradayAgents.length})
          </TabsTrigger>
          <TabsTrigger value="scalping" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Scalping ({scalpingAgents.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {ALL_STRATEGIES.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="price-action">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {priceActionAgents.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="options">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {optionsAgents.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="swing">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {swingAgents.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="intraday">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {intradayAgents.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="scalping">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
            {scalpingAgents.map((strategy) => (
              <StrategyAgent
                key={strategy.id}
                strategy={strategy}
                isActive={agentStates[strategy.id]}
                onToggle={(active) => handleAgentToggle(strategy.id, active)}
                performance={strategyPerformance[strategy.id]}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
