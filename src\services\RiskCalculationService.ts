
export interface PortfolioRiskMetrics {
  portfolioValue: number;
  totalRisk: number;
  dailyVaR: number;
  expectedShortfall: number;
  maxDrawdown: number;
  sharpeRatio: number;
  betaToMarket: number;
  correlationMatrix: Record<string, Record<string, number>>;
  riskContribution: Record<string, number>;
}

export interface PositionRisk {
  symbol: string;
  quantity: number;
  marketValue: number;
  weight: number;
  volatility: number;
  beta: number;
  var95: number;
  var99: number;
  expectedShortfall: number;
  marginOfSafety: number;
}

export interface DrawdownAnalysis {
  currentDrawdown: number;
  maxDrawdown: number;
  averageDrawdown: number;
  drawdownDuration: number;
  recoveryTime: number;
  underwaterPeriods: Array<{start: Date, end: Date, depth: number}>;
}

export class RiskCalculationService {
  
  // Calculate Value at Risk (VaR) using historical simulation
  static calculateVaR(returns: number[], confidenceLevel: number = 0.95): number {
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const index = Math.floor((1 - confidenceLevel) * sortedReturns.length);
    return Math.abs(sortedReturns[index] || 0);
  }

  // Calculate Expected Shortfall (Conditional VaR)
  static calculateExpectedShortfall(returns: number[], confidenceLevel: number = 0.95): number {
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const varIndex = Math.floor((1 - confidenceLevel) * sortedReturns.length);
    const tailReturns = sortedReturns.slice(0, varIndex);
    
    if (tailReturns.length === 0) return 0;
    
    const averageTailLoss = tailReturns.reduce((sum, ret) => sum + ret, 0) / tailReturns.length;
    return Math.abs(averageTailLoss);
  }

  // Calculate Maximum Drawdown
  static calculateMaxDrawdown(portfolioValues: number[]): DrawdownAnalysis {
    let maxDrawdown = 0;
    let currentDrawdown = 0;
    let peak = portfolioValues[0];
    let drawdownStart = 0;
    let maxDrawdownStart = 0;
    let maxDrawdownEnd = 0;
    let inDrawdown = false;
    
    const underwaterPeriods: Array<{start: Date, end: Date, depth: number}> = [];
    let currentDrawdownStart = 0;
    
    for (let i = 1; i < portfolioValues.length; i++) {
      if (portfolioValues[i] > peak) {
        // New peak
        if (inDrawdown) {
          // End of drawdown period
          underwaterPeriods.push({
            start: new Date(Date.now() - (i - currentDrawdownStart) * 24 * 60 * 60 * 1000),
            end: new Date(Date.now() - (i - 1) * 24 * 60 * 60 * 1000),
            depth: currentDrawdown
          });
          inDrawdown = false;
        }
        peak = portfolioValues[i];
        currentDrawdown = 0;
      } else {
        // Potential drawdown
        if (!inDrawdown) {
          inDrawdown = true;
          currentDrawdownStart = i;
        }
        
        currentDrawdown = (peak - portfolioValues[i]) / peak * 100;
        
        if (currentDrawdown > maxDrawdown) {
          maxDrawdown = currentDrawdown;
          maxDrawdownStart = currentDrawdownStart;
          maxDrawdownEnd = i;
        }
      }
    }
    
    const averageDrawdown = underwaterPeriods.length > 0 
      ? underwaterPeriods.reduce((sum, period) => sum + period.depth, 0) / underwaterPeriods.length 
      : 0;
    
    return {
      currentDrawdown,
      maxDrawdown,
      averageDrawdown,
      drawdownDuration: maxDrawdownEnd - maxDrawdownStart,
      recoveryTime: portfolioValues.length - maxDrawdownEnd,
      underwaterPeriods
    };
  }

  // Calculate Sharpe Ratio
  static calculateSharpeRatio(returns: number[], riskFreeRate: number = 0.02): number {
    const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const excessReturns = returns.map(ret => ret - riskFreeRate / 252); // Daily risk-free rate
    const volatility = this.calculateVolatility(excessReturns);
    
    return volatility !== 0 ? (averageReturn - riskFreeRate / 252) / volatility : 0;
  }

  // Calculate Volatility (Standard Deviation)
  static calculateVolatility(returns: number[]): number {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  // Calculate Beta to Market
  static calculateBeta(assetReturns: number[], marketReturns: number[]): number {
    if (assetReturns.length !== marketReturns.length) return 1;
    
    const assetMean = assetReturns.reduce((sum, ret) => sum + ret, 0) / assetReturns.length;
    const marketMean = marketReturns.reduce((sum, ret) => sum + ret, 0) / marketReturns.length;
    
    let covariance = 0;
    let marketVariance = 0;
    
    for (let i = 0; i < assetReturns.length; i++) {
      covariance += (assetReturns[i] - assetMean) * (marketReturns[i] - marketMean);
      marketVariance += Math.pow(marketReturns[i] - marketMean, 2);
    }
    
    covariance /= assetReturns.length;
    marketVariance /= marketReturns.length;
    
    return marketVariance !== 0 ? covariance / marketVariance : 1;
  }

  // Calculate Correlation Matrix
  static calculateCorrelationMatrix(returnsMatrix: Record<string, number[]>): Record<string, Record<string, number>> {
    const symbols = Object.keys(returnsMatrix);
    const correlationMatrix: Record<string, Record<string, number>> = {};
    
    symbols.forEach(symbol1 => {
      correlationMatrix[symbol1] = {};
      symbols.forEach(symbol2 => {
        if (symbol1 === symbol2) {
          correlationMatrix[symbol1][symbol2] = 1;
        } else {
          correlationMatrix[symbol1][symbol2] = this.calculateCorrelation(
            returnsMatrix[symbol1], 
            returnsMatrix[symbol2]
          );
        }
      });
    });
    
    return correlationMatrix;
  }

  // Calculate Correlation between two assets
  static calculateCorrelation(returns1: number[], returns2: number[]): number {
    if (returns1.length !== returns2.length || returns1.length === 0) return 0;
    
    const mean1 = returns1.reduce((sum, ret) => sum + ret, 0) / returns1.length;
    const mean2 = returns2.reduce((sum, ret) => sum + ret, 0) / returns2.length;
    
    let numerator = 0;
    let sum1Sq = 0;
    let sum2Sq = 0;
    
    for (let i = 0; i < returns1.length; i++) {
      const diff1 = returns1[i] - mean1;
      const diff2 = returns2[i] - mean2;
      
      numerator += diff1 * diff2;
      sum1Sq += diff1 * diff1;
      sum2Sq += diff2 * diff2;
    }
    
    const denominator = Math.sqrt(sum1Sq * sum2Sq);
    return denominator !== 0 ? numerator / denominator : 0;
  }

  // Calculate Position Risk Metrics
  static calculatePositionRisk(
    symbol: string,
    quantity: number,
    price: number,
    returns: number[],
    marketReturns: number[]
  ): PositionRisk {
    const marketValue = quantity * price;
    const volatility = this.calculateVolatility(returns);
    const beta = this.calculateBeta(returns, marketReturns);
    const var95 = this.calculateVaR(returns, 0.95) * marketValue;
    const var99 = this.calculateVaR(returns, 0.99) * marketValue;
    const expectedShortfall = this.calculateExpectedShortfall(returns, 0.95) * marketValue;
    
    // Simple margin of safety calculation
    const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const marginOfSafety = averageReturn > 0 ? (averageReturn - volatility) / averageReturn * 100 : 0;
    
    return {
      symbol,
      quantity,
      marketValue,
      weight: 0, // To be calculated at portfolio level
      volatility: volatility * 100, // Convert to percentage
      beta,
      var95,
      var99,
      expectedShortfall,
      marginOfSafety
    };
  }

  // Calculate Portfolio Risk Metrics
  static calculatePortfolioRisk(
    positions: PositionRisk[],
    portfolioReturns: number[],
    marketReturns: number[]
  ): PortfolioRiskMetrics {
    const portfolioValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0);
    
    // Update position weights
    positions.forEach(pos => {
      pos.weight = pos.marketValue / portfolioValue * 100;
    });
    
    const totalRisk = Math.sqrt(
      positions.reduce((sum, pos) => sum + Math.pow(pos.var95, 2), 0)
    );
    
    const dailyVaR = this.calculateVaR(portfolioReturns, 0.95) * portfolioValue;
    const expectedShortfall = this.calculateExpectedShortfall(portfolioReturns, 0.95) * portfolioValue;
    const drawdownAnalysis = this.calculateMaxDrawdown(
      portfolioReturns.map((ret, i) => portfolioValue * (1 + ret))
    );
    const sharpeRatio = this.calculateSharpeRatio(portfolioReturns);
    const betaToMarket = this.calculateBeta(portfolioReturns, marketReturns);
    
    // Risk contribution calculation (simplified)
    const riskContribution: Record<string, number> = {};
    positions.forEach(pos => {
      riskContribution[pos.symbol] = (pos.var95 / totalRisk) * 100;
    });
    
    return {
      portfolioValue,
      totalRisk,
      dailyVaR,
      expectedShortfall,
      maxDrawdown: drawdownAnalysis.maxDrawdown,
      sharpeRatio,
      betaToMarket,
      correlationMatrix: {}, // Would need return data for each position
      riskContribution
    };
  }

  // Kelly Criterion for Position Sizing
  static calculateKellyCriterion(winRate: number, averageWin: number, averageLoss: number): number {
    if (averageLoss === 0) return 0;
    const winLossRatio = averageWin / averageLoss;
    return (winRate * winLossRatio - (1 - winRate)) / winLossRatio;
  }

  // Monte Carlo Simulation for Portfolio Risk
  static monteCarloPortfolioSimulation(
    positions: PositionRisk[],
    simulations: number = 10000,
    timeHorizon: number = 252
  ): {
    worstCase: number,
    bestCase: number,
    median: number,
    var95: number,
    var99: number,
    probabilityOfLoss: number
  } {
    const results: number[] = [];
    
    for (let i = 0; i < simulations; i++) {
      let portfolioReturn = 0;
      
      positions.forEach(pos => {
        // Generate random return based on normal distribution
        const randomReturn = this.generateNormalRandom(0, pos.volatility / 100 / Math.sqrt(timeHorizon));
        portfolioReturn += (pos.weight / 100) * randomReturn;
      });
      
      results.push(portfolioReturn);
    }
    
    results.sort((a, b) => a - b);
    
    return {
      worstCase: results[0],
      bestCase: results[results.length - 1],
      median: results[Math.floor(results.length / 2)],
      var95: Math.abs(results[Math.floor(results.length * 0.05)]),
      var99: Math.abs(results[Math.floor(results.length * 0.01)]),
      probabilityOfLoss: results.filter(r => r < 0).length / results.length * 100
    };
  }

  // Generate normal random number (Box-Muller transform)
  private static generateNormalRandom(mean: number = 0, stdDev: number = 1): number {
    let u = 0, v = 0;
    while(u === 0) u = Math.random(); // Converting [0,1) to (0,1)
    while(v === 0) v = Math.random();
    
    const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return z * stdDev + mean;
  }
}
