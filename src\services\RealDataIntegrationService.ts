import { RealMathService } from './RealMathService';

export interface RealMarketDataConfig {
  alphaVantageApiKey?: string;
  polygonApiKey?: string;
  finnhubApiKey?: string;
  provider: 'alphavantage' | 'polygon' | 'finnhub' | 'mock';
}

export interface RealMarketDataPoint {
  symbol: string;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  source: string;
}

export interface BacktestResult {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  trades: TradeResult[];
}

export interface TradeResult {
  symbol: string;
  entryDate: number;
  exitDate: number;
  entryPrice: number;
  exitPrice: number;
  quantity: number;
  side: 'BUY' | 'SELL';
  pnl: number;
  pnlPercent: number;
  holdingPeriod: number;
}

export class RealDataIntegrationService {
  private realDataConfig: RealMarketDataConfig;
  private historicalCache: Map<string, RealMarketDataPoint[]> = new Map();
  private performanceCache: Map<string, BacktestResult> = new Map();

  constructor(config: RealMarketDataConfig) {
    this.realDataConfig = config;
  }

  // Public method to get base price for symbol
  public getBasePriceForSymbol(symbol: string): number {
    const basePrices: { [key: string]: number } = {
      'AAPL': 150,
      'GOOGL': 2500,
      'MSFT': 300,
      'TSLA': 800,
      'NFLX': 400,
      'AMZN': 3200,
      'NVDA': 450,
      'META': 320,
      'RELIANCE': 2400,
      'TCS': 3500,
      'HDFCBANK': 1600,
      'INFY': 1400,
      'ICICIBANK': 800,
      'HINDUNILVR': 2600,
      'ITC': 450,
      'SBIN': 550,
      'BHARTIARTL': 850,
      'KOTAKBANK': 1800,
      'LT': 2200,
      'ASIANPAINT': 3200
    };
    return basePrices[symbol] || 100;
  }

  // Real market data fetching from external APIs
  async fetchRealMarketData(symbol: string, period: '1d' | '5d' | '1mo' | '3mo' | '6mo' | '1y' = '1mo'): Promise<RealMarketDataPoint[]> {
    try {
      const cacheKey = `${symbol}_${period}`;
      const cached = this.historicalCache.get(cacheKey);
      
      if (cached && this.isCacheValid(cached)) {
        return cached;
      }

      let data: RealMarketDataPoint[] = [];

      switch (this.realDataConfig.provider) {
        case 'alphavantage':
          data = await this.fetchFromAlphaVantage(symbol, period);
          break;
        case 'polygon':
          data = await this.fetchFromPolygon(symbol, period);
          break;
        case 'finnhub':
          data = await this.fetchFromFinnhub(symbol, period);
          break;
        default:
          data = await this.generateRealisticMockData(symbol, period);
      }

      this.historicalCache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error fetching real market data for ${symbol}:`, error);
      return await this.generateRealisticMockData(symbol, period);
    }
  }

  private async fetchFromAlphaVantage(symbol: string, period: string): Promise<RealMarketDataPoint[]> {
    if (!this.realDataConfig.alphaVantageApiKey) {
      throw new Error('Alpha Vantage API key not configured');
    }

    const response = await fetch(
      `https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=${symbol}&apikey=${this.realDataConfig.alphaVantageApiKey}`
    );

    if (!response.ok) {
      throw new Error(`Alpha Vantage API error: ${response.statusText}`);
    }

    const data = await response.json();
    const timeSeries = data['Time Series (Daily)'];

    if (!timeSeries) {
      throw new Error('Invalid data format from Alpha Vantage');
    }

    return Object.entries(timeSeries).map(([date, values]: [string, any]) => ({
      symbol,
      timestamp: new Date(date).getTime(),
      open: parseFloat(values['1. open']),
      high: parseFloat(values['2. high']),
      low: parseFloat(values['3. low']),
      close: parseFloat(values['4. close']),
      volume: parseInt(values['5. volume']),
      source: 'alphavantage'
    })).reverse(); // Reverse to get chronological order
  }

  private async fetchFromPolygon(symbol: string, period: string): Promise<RealMarketDataPoint[]> {
    if (!this.realDataConfig.polygonApiKey) {
      throw new Error('Polygon API key not configured');
    }

    const endDate = new Date().toISOString().split('T')[0];
    const startDate = this.getStartDate(period);

    const response = await fetch(
      `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/1/day/${startDate}/${endDate}?apikey=${this.realDataConfig.polygonApiKey}`
    );

    if (!response.ok) {
      throw new Error(`Polygon API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.results) {
      throw new Error('Invalid data format from Polygon');
    }

    return data.results.map((item: any) => ({
      symbol,
      timestamp: item.t,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v,
      source: 'polygon'
    }));
  }

  private async fetchFromFinnhub(symbol: string, period: string): Promise<RealMarketDataPoint[]> {
    if (!this.realDataConfig.finnhubApiKey) {
      throw new Error('Finnhub API key not configured');
    }

    const endTime = Math.floor(Date.now() / 1000);
    const startTime = this.getStartTimestamp(period);

    const response = await fetch(
      `https://finnhub.io/api/v1/stock/candle?symbol=${symbol}&resolution=D&from=${startTime}&to=${endTime}&token=${this.realDataConfig.finnhubApiKey}`
    );

    if (!response.ok) {
      throw new Error(`Finnhub API error: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.s !== 'ok' || !data.c) {
      throw new Error('Invalid data format from Finnhub');
    }

    return data.t.map((timestamp: number, index: number) => ({
      symbol,
      timestamp: timestamp * 1000,
      open: data.o[index],
      high: data.h[index],
      low: data.l[index],
      close: data.c[index],
      volume: data.v[index],
      source: 'finnhub'
    }));
  }

  private async generateRealisticMockData(symbol: string, period: string): Promise<RealMarketDataPoint[]> {
    const days = this.getPeriodDays(period);
    const data: RealMarketDataPoint[] = [];
    const basePrice = this.getBasePriceForSymbol(symbol);
    
    let currentPrice = basePrice;
    const now = Date.now();

    for (let i = days; i >= 0; i--) {
      const timestamp = now - (i * 24 * 60 * 60 * 1000);
      
      // Generate realistic OHLCV data
      const volatility = 0.02; // 2% daily volatility
      const trendFactor = (Math.random() - 0.5) * 0.001; // Small trend
      const randomFactor = (Math.random() - 0.5) * volatility;
      
      const open = currentPrice;
      const change = currentPrice * (trendFactor + randomFactor);
      const close = Math.max(open + change, basePrice * 0.5);
      
      const high = Math.max(open, close) * (1 + Math.random() * 0.01);
      const low = Math.min(open, close) * (1 - Math.random() * 0.01);
      const volume = Math.floor(1000000 + Math.random() * 5000000);

      data.push({
        symbol,
        timestamp,
        open,
        high,
        low,
        close,
        volume,
        source: 'mock'
      });

      currentPrice = close;
    }

    return data;
  }

  // Real backtesting with historical data
  async performBacktest(
    symbol: string, 
    strategy: 'swing' | 'intraday' | 'scalping',
    period: '1mo' | '3mo' | '6mo' | '1y' = '6mo'
  ): Promise<BacktestResult> {
    try {
      const cacheKey = `${symbol}_${strategy}_${period}`;
      const cached = this.performanceCache.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      const historicalData = await this.fetchRealMarketData(symbol, period);
      const trades = await this.simulateStrategy(historicalData, strategy);
      
      const result = this.calculateBacktestResults(trades);
      this.performanceCache.set(cacheKey, result);
      
      return result;
    } catch (error) {
      console.error(`Backtest failed for ${symbol}:`, error);
      return this.generateMockBacktestResult();
    }
  }

  private async simulateStrategy(data: RealMarketDataPoint[], strategy: string): Promise<TradeResult[]> {
    const trades: TradeResult[] = [];
    let position: { price: number; quantity: number; timestamp: number } | null = null;

    for (let i = 20; i < data.length - 1; i++) {
      const current = data[i];
      const prices = data.slice(i - 20, i + 1).map(d => d.close);
      
      // Calculate technical indicators
      const rsi = RealMathService.calculateRSI(prices);
      const macd = RealMathService.calculateMACD(prices);
      const ema20 = RealMathService.calculateEMA(prices, 20);
      const currentEma = ema20[ema20.length - 1];

      // Strategy-specific entry/exit logic
      const shouldBuy = this.getEntrySignal(strategy, current, rsi, macd, currentEma);
      const shouldSell = this.getExitSignal(strategy, current, rsi, macd, currentEma);

      if (!position && shouldBuy) {
        // Enter position
        position = {
          price: current.close,
          quantity: 100, // Fixed quantity for simulation
          timestamp: current.timestamp
        };
      } else if (position && shouldSell) {
        // Exit position
        const pnl = (current.close - position.price) * position.quantity;
        const pnlPercent = ((current.close - position.price) / position.price) * 100;
        const holdingPeriod = current.timestamp - position.timestamp;

        trades.push({
          symbol: current.symbol,
          entryDate: position.timestamp,
          exitDate: current.timestamp,
          entryPrice: position.price,
          exitPrice: current.close,
          quantity: position.quantity,
          side: 'BUY',
          pnl,
          pnlPercent,
          holdingPeriod
        });

        position = null;
      }
    }

    return trades;
  }

  private getEntrySignal(strategy: string, data: RealMarketDataPoint, rsi: number, macd: any, ema: number): boolean {
    switch (strategy) {
      case 'swing':
        return rsi < 30 && macd.histogram > 0 && data.close > ema;
      case 'intraday':
        return rsi > 50 && macd.macd > macd.signal && data.close > ema;
      case 'scalping':
        return macd.histogram > 0 && data.close > data.open;
      default:
        return false;
    }
  }

  private getExitSignal(strategy: string, data: RealMarketDataPoint, rsi: number, macd: any, ema: number): boolean {
    switch (strategy) {
      case 'swing':
        return rsi > 70 || macd.histogram < 0;
      case 'intraday':
        return rsi > 70 || data.close < ema;
      case 'scalping':
        return macd.histogram < 0 || data.close < data.open;
      default:
        return false;
    }
  }

  private calculateBacktestResults(trades: TradeResult[]): BacktestResult {
    if (trades.length === 0) {
      return this.generateMockBacktestResult();
    }

    const winningTrades = trades.filter(t => t.pnl > 0).length;
    const losingTrades = trades.filter(t => t.pnl < 0).length;
    const winRate = winningTrades / trades.length;
    
    const totalReturn = trades.reduce((sum, t) => sum + t.pnlPercent, 0);
    const returns = trades.map(t => t.pnlPercent / 100);
    const portfolioValues = this.calculatePortfolioValues(trades);
    
    const maxDrawdown = RealMathService.calculateMaxDrawdown(portfolioValues);
    const sharpeRatio = RealMathService.calculateSharpeRatio(returns);
    const profitFactor = RealMathService.calculateProfitFactor(trades.map(t => t.pnl));

    return {
      totalTrades: trades.length,
      winningTrades,
      losingTrades,
      winRate,
      totalReturn,
      maxDrawdown,
      sharpeRatio,
      profitFactor,
      trades
    };
  }

  private calculatePortfolioValues(trades: TradeResult[]): number[] {
    const values = [100000]; // Starting capital
    let current = 100000;

    trades.forEach(trade => {
      current += trade.pnl;
      values.push(current);
    });

    return values;
  }

  private generateMockBacktestResult(): BacktestResult {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalReturn: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      profitFactor: 0,
      trades: []
    };
  }

  private isCacheValid(data: RealMarketDataPoint[]): boolean {
    if (data.length === 0) return false;
    const lastTimestamp = Math.max(...data.map(d => d.timestamp));
    const now = Date.now();
    return (now - lastTimestamp) < 24 * 60 * 60 * 1000; // 24 hours
  }

  private getStartDate(period: string): string {
    const now = new Date();
    const days = this.getPeriodDays(period);
    const startDate = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000));
    return startDate.toISOString().split('T')[0];
  }

  private getStartTimestamp(period: string): number {
    const days = this.getPeriodDays(period);
    return Math.floor((Date.now() - (days * 24 * 60 * 60 * 1000)) / 1000);
  }

  private getPeriodDays(period: string): number {
    switch (period) {
      case '1d': return 1;
      case '5d': return 5;
      case '1mo': return 30;
      case '3mo': return 90;
      case '6mo': return 180;
      case '1y': return 365;
      default: return 30;
    }
  }

  // Real performance metrics calculation
  async calculateRealPerformanceMetrics(symbol: string): Promise<any> {
    try {
      const backtestResult = await this.performBacktest(symbol, 'swing', '6mo');
      
      return {
        totalReturn: backtestResult.totalReturn,
        winRate: backtestResult.winRate * 100,
        maxDrawdown: backtestResult.maxDrawdown * 100,
        sharpeRatio: backtestResult.sharpeRatio,
        profitFactor: backtestResult.profitFactor,
        totalTrades: backtestResult.totalTrades,
        avgHoldingPeriod: backtestResult.trades.length > 0 
          ? backtestResult.trades.reduce((sum, t) => sum + t.holdingPeriod, 0) / backtestResult.trades.length / (24 * 60 * 60 * 1000)
          : 0,
        lastUpdated: Date.now()
      };
    } catch (error) {
      console.error(`Error calculating performance metrics for ${symbol}:`, error);
      return {
        totalReturn: 0,
        winRate: 0,
        maxDrawdown: 0,
        sharpeRatio: 0,
        profitFactor: 0,
        totalTrades: 0,
        avgHoldingPeriod: 0,
        lastUpdated: Date.now()
      };
    }
  }

  // Data validation for incoming market data
  validateMarketData(data: any): boolean {
    return data && 
           typeof data.symbol === 'string' &&
           typeof data.close === 'number' && data.close > 0 &&
           typeof data.volume === 'number' && data.volume >= 0 &&
           typeof data.timestamp === 'number' && data.timestamp > 0;
  }

  // Error handling for API failures
  handleAPIError(error: Error, context: string): void {
    console.error(`API Error in ${context}:`, error);
    
    if (error.message.includes('API key') || error.message.includes('rate limit')) {
      console.warn('Falling back to mock data due to API issues');
    }
  }
}
