
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Shield, AlertTriangle, TrendingDown, Activity } from "lucide-react";

interface RiskMetric {
  metric: string;
  value: string;
  status: 'Normal' | 'Warning' | 'Critical';
  threshold: string;
}

interface RiskAlert {
  type: string;
  message: string;
  severity: 'Low' | 'Medium' | 'High';
  time: string;
}

interface RiskLimit {
  parameter: string;
  current: string;
  limit: string;
  status: 'OK' | 'Warning' | 'Breach';
}

interface StressTest {
  scenario: string;
  impact: string;
  probability: string;
}

interface RiskManagementProps {
  riskMetrics?: RiskMetric[];
  activeAlerts?: RiskAlert[];
  riskLimits?: RiskLimit[];
  stressTests?: StressTest[];
  isLoading?: boolean;
  onEmergencyStop?: () => void;
}

export const RiskManagement = ({
  riskMetrics = [],
  activeAlerts = [],
  riskLimits = [],
  stressTests = [],
  isLoading = false,
  onEmergencyStop
}: RiskManagementProps) => {
  const renderEmptyState = (title: string, description: string, icon: React.ReactNode) => (
    <div className="text-center py-8">
      {icon}
      <p className="text-trading-muted mt-2">{isLoading ? 'Loading...' : title}</p>
      <p className="text-sm text-trading-muted mt-1">{isLoading ? 'Fetching risk data...' : description}</p>
    </div>
  );

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Real-time Risk Management System
          </CardTitle>
        </CardHeader>
        <CardContent>
          {riskMetrics.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              {riskMetrics.map((metric, index) => (
                <div key={index} className="text-center p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="text-sm text-trading-muted">{metric.metric}</div>
                  <div className="text-xl font-bold text-trading-light">{metric.value}</div>
                  <div className="flex items-center justify-center space-x-2 mt-1">
                    <Badge variant="outline" className={
                      metric.status === "Normal" ? "text-green-400 border-green-400" : 
                      metric.status === "Warning" ? "text-yellow-400 border-yellow-400" : 
                      "text-red-400 border-red-400"
                    }>
                      {metric.status}
                    </Badge>
                  </div>
                  <div className="text-xs text-trading-muted mt-1">Limit: {metric.threshold}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center p-4 bg-trading-dark rounded border border-trading-border">
                <div className="text-sm text-trading-muted">Portfolio VaR</div>
                <div className="text-xl font-bold text-trading-light">{isLoading ? '...' : '--'}</div>
                <Badge variant="outline" className="text-gray-400 border-gray-400 mt-1">
                  {isLoading ? 'Loading' : 'No Data'}
                </Badge>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded border border-trading-border">
                <div className="text-sm text-trading-muted">Max Drawdown</div>
                <div className="text-xl font-bold text-trading-light">{isLoading ? '...' : '--'}</div>
                <Badge variant="outline" className="text-gray-400 border-gray-400 mt-1">
                  {isLoading ? 'Loading' : 'No Data'}
                </Badge>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded border border-trading-border">
                <div className="text-sm text-trading-muted">Concentration Risk</div>
                <div className="text-xl font-bold text-trading-light">{isLoading ? '...' : '--'}</div>
                <Badge variant="outline" className="text-gray-400 border-gray-400 mt-1">
                  {isLoading ? 'Loading' : 'No Data'}
                </Badge>
              </div>
              <div className="text-center p-4 bg-trading-dark rounded border border-trading-border">
                <div className="text-sm text-trading-muted">Leverage Ratio</div>
                <div className="text-xl font-bold text-trading-light">{isLoading ? '...' : '--'}</div>
                <Badge variant="outline" className="text-gray-400 border-gray-400 mt-1">
                  {isLoading ? 'Loading' : 'No Data'}
                </Badge>
              </div>
            </div>
          )}

          <div className="flex space-x-2">
            <Button 
              size="sm" 
              className="bg-red-600 hover:bg-red-700"
              onClick={onEmergencyStop}
              disabled={isLoading}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Emergency Stop
            </Button>
            <Button variant="outline" size="sm" disabled={isLoading}>Update Limits</Button>
            <Button variant="outline" size="sm" disabled={isLoading}>Risk Report</Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="alerts" className="data-[state=active]:bg-trading-accent">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Active Alerts
          </TabsTrigger>
          <TabsTrigger value="limits" className="data-[state=active]:bg-trading-accent">
            <Shield className="h-4 w-4 mr-2" />
            Risk Limits
          </TabsTrigger>
          <TabsTrigger value="stress" className="data-[state=active]:bg-trading-accent">
            <TrendingDown className="h-4 w-4 mr-2" />
            Stress Tests
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="alerts">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Active Risk Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              {activeAlerts.length > 0 ? (
                <div className="space-y-3">
                  {activeAlerts.map((alert, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline" className="text-blue-400 border-blue-400">
                            {alert.type}
                          </Badge>
                          <Badge variant="outline" className={
                            alert.severity === "High" ? "text-red-400 border-red-400" : 
                            alert.severity === "Medium" ? "text-yellow-400 border-yellow-400" : 
                            "text-green-400 border-green-400"
                          }>
                            {alert.severity}
                          </Badge>
                        </div>
                        <span className="text-xs text-trading-muted">{alert.time}</span>
                      </div>
                      <div className="text-sm text-trading-light">{alert.message}</div>
                      <div className="flex space-x-2 mt-3">
                        <Button size="sm">Acknowledge</Button>
                        <Button variant="outline" size="sm">Details</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Active Alerts',
                  'Risk monitoring alerts will appear here when triggered',
                  <AlertTriangle className="h-12 w-12 mx-auto opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="limits">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Risk Limits Monitor</CardTitle>
            </CardHeader>
            <CardContent>
              {riskLimits.length > 0 ? (
                <div className="space-y-4">
                  {riskLimits.map((limit, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-trading-light">{limit.parameter}</span>
                        <Badge variant="outline" className={
                          limit.status === "OK" ? "text-green-400 border-green-400" : 
                          limit.status === "Warning" ? "text-yellow-400 border-yellow-400" : 
                          "text-red-400 border-red-400"
                        }>
                          {limit.status}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-trading-muted">
                          Current: <span className="text-trading-light">{limit.current}</span> | 
                          Limit: <span className="text-trading-light">{limit.limit}</span>
                        </div>
                        <Button variant="outline" size="sm">Adjust</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Risk Limits Configured',
                  'Set up risk limits to monitor portfolio exposure',
                  <Shield className="h-12 w-12 mx-auto opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stress">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Stress Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              {stressTests.length > 0 ? (
                <div className="space-y-4">
                  {stressTests.map((test, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-trading-light">{test.scenario}</span>
                        <Badge variant="outline" className="text-blue-400 border-blue-400">
                          {test.probability} probability
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-red-400">{test.impact}</span>
                        <Button variant="outline" size="sm">Simulate</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                renderEmptyState(
                  'No Stress Tests Available',
                  'Run stress tests to evaluate portfolio resilience',
                  <TrendingDown className="h-12 w-12 mx-auto opacity-50" />
                )
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Real-time Risk Monitoring</CardTitle>
            </CardHeader>
            <CardContent>
              {renderEmptyState(
                'Risk Monitoring Dashboard',
                'Real-time risk metrics, heat maps, and correlation analysis',
                <Activity className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
