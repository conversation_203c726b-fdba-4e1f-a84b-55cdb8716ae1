
export interface TickData {
  symbol: string;
  timestamp: string;
  ltp: number;
  volume: number;
  vwap: number;
  bid: number;
  ask: number;
}

export interface OptionChainData {
  symbol: string;
  expiry: string;
  strikes: Array<{
    strike: number;
    CE_IV: number;
    PE_IV: number;
    CE_OI: number;
    PE_OI: number;
    CE_LTP?: number;
    PE_LTP?: number;
  }>;
}

export interface FinancialData {
  symbol: string;
  date: string;
  roce: number;
  roe: number;
  eps: number;
  sales_growth: number;
  debt_equity: number;
  mf_holding: number;
}

export interface OrderBookData {
  symbol: string;
  timestamp: string;
  bids: Array<{ price: number; quantity: number; orders: number }>;
  asks: Array<{ price: number; quantity: number; orders: number }>;
}

export class LiveDataService {
  private wsConnection: WebSocket | null = null;
  private subscribers: Map<string, Array<(data: any) => void>> = new Map();
  private isConnected: boolean = false;
  
  constructor(private wsUrl?: string, private apiKey?: string) {
    // Only initialize if configuration is provided
    if (wsUrl && apiKey) {
      this.initializeWebSocket();
    }
  }

  private initializeWebSocket() {
    if (!this.wsUrl || !this.apiKey) {
      console.warn("WebSocket URL or API key not configured");
      return;
    }

    try {
      this.wsConnection = new WebSocket(this.wsUrl);
      
      this.wsConnection.onopen = () => {
        console.log("WebSocket connected");
        this.isConnected = true;
        // Send authentication if required
        this.authenticate();
      };

      this.wsConnection.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleIncomingData(data);
        } catch (error) {
          console.error("Error parsing WebSocket data:", error);
        }
      };

      this.wsConnection.onclose = () => {
        console.log("WebSocket disconnected");
        this.isConnected = false;
        // Attempt reconnection after delay
        setTimeout(() => this.initializeWebSocket(), 5000);
      };

      this.wsConnection.onerror = (error) => {
        console.error("WebSocket error:", error);
        this.isConnected = false;
      };
    } catch (error) {
      console.error("Failed to initialize WebSocket:", error);
    }
  }

  private authenticate() {
    if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify({
        action: 'auth',
        api_key: this.apiKey
      }));
    }
  }

  private handleIncomingData(data: any) {
    const { type, ...payload } = data;
    this.notifySubscribers(type, payload);
  }

  subscribe(dataType: string, callback: (data: any) => void) {
    if (!this.subscribers.has(dataType)) {
      this.subscribers.set(dataType, []);
    }
    this.subscribers.get(dataType)?.push(callback);

    // Subscribe to data type via WebSocket if connected
    if (this.isConnected && this.wsConnection) {
      this.wsConnection.send(JSON.stringify({
        action: 'subscribe',
        type: dataType
      }));
    }
  }

  unsubscribe(dataType: string, callback: (data: any) => void) {
    const callbacks = this.subscribers.get(dataType);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }

    // Unsubscribe from data type if no more callbacks
    if (callbacks && callbacks.length === 0) {
      this.subscribers.delete(dataType);
      if (this.isConnected && this.wsConnection) {
        this.wsConnection.send(JSON.stringify({
          action: 'unsubscribe',
          type: dataType
        }));
      }
    }
  }

  private notifySubscribers(dataType: string, data: any) {
    const callbacks = this.subscribers.get(dataType);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Calculate derived features
  calculateVWAP(tickData: TickData[]): number {
    if (tickData.length === 0) return 0;
    
    const totalVolumePrice = tickData.reduce((sum, tick) => sum + (tick.ltp * tick.volume), 0);
    const totalVolume = tickData.reduce((sum, tick) => sum + tick.volume, 0);
    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
  }

  calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50;
    
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) gains += change;
      else losses -= change;
    }
    
    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
    
    return 100 - (100 / (1 + rs));
  }

  calculateIVRank(currentIV: number, historicalIV: number[]): number {
    if (historicalIV.length === 0) return 50;
    
    const minIV = Math.min(...historicalIV);
    const maxIV = Math.max(...historicalIV);
    return maxIV === minIV ? 50 : ((currentIV - minIV) / (maxIV - minIV)) * 100;
  }

  // API methods for data persistence
  async saveTickData(data: TickData): Promise<void> {
    // Implement actual database save logic
    console.log("Save tick data:", data);
  }

  async saveOptionChain(data: OptionChainData): Promise<void> {
    // Implement actual database save logic
    console.log("Save option chain:", data);
  }

  async saveFinancialData(data: FinancialData): Promise<void> {
    // Implement actual database save logic
    console.log("Save financial data:", data);
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  disconnect() {
    if (this.wsConnection) {
      this.wsConnection.close();
    }
    this.subscribers.clear();
    this.isConnected = false;
  }
}

// Export a factory function instead of a singleton
export const createLiveDataService = (wsUrl?: string, apiKey?: string) => {
  return new LiveDataService(wsUrl, apiKey);
};
