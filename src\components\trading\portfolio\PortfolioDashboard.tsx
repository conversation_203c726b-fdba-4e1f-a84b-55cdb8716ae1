
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Activity } from "lucide-react";

export const PortfolioDashboard = () => {
  const holdings = [
    { symbol: "RELIANCE", qty: 100, avgPrice: 2450.30, ltp: 2523.45, pnl: 7315, pnlPercent: 2.98 },
    { symbol: "TCS", qty: 50, avgPrice: 3789.20, ltp: 3845.30, pnl: 2805, pnlPercent: 1.48 },
    { symbol: "HDFCBANK", qty: 80, avgPrice: 1678.90, ltp: 1654.75, pnl: -1932, pnlPercent: -1.44 },
    { symbol: "INFY", qty: 120, avgPrice: 1756.40, ltp: 1789.20, pnl: 3936, pnlPercent: 1.87 },
    { symbol: "ICICIBANK", qty: 90, avgPrice: 1234.50, ltp: 1267.80, pnl: 2997, pnlPercent: 2.70 },
  ];

  const sectorAllocation = [
    { sector: "IT", allocation: 35.2, value: "₹4,23,400", change: "+2.1%" },
    { sector: "Banking", allocation: 28.7, value: "₹3,45,600", change: "+0.8%" },
    { sector: "Energy", allocation: 15.4, value: "₹1,85,200", change: "+1.5%" },
    { sector: "Auto", allocation: 12.3, value: "₹1,48,200", change: "-0.3%" },
    { sector: "FMCG", allocation: 8.4, value: "₹1,01,200", change: "+0.9%" },
  ];

  const performanceMetrics = [
    { metric: "Total Portfolio Value", value: "₹12,03,600", change: "+1.8%" },
    { metric: "Total P&L", value: "₹15,121", change: "+2.1%" },
    { metric: "Day's P&L", value: "₹8,456", change: "+0.7%" },
    { metric: "XIRR", value: "18.7%", change: "+0.3%" },
  ];

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <PieChart className="h-5 w-5 mr-2" />
            Portfolio Analytics Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            {performanceMetrics.map((metric, index) => (
              <div key={index} className="text-center">
                <div className="text-sm text-trading-muted">{metric.metric}</div>
                <div className="text-2xl font-bold text-trading-light">{metric.value}</div>
                <div className={`text-xs ${metric.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                  {metric.change}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="holdings" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="holdings" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Holdings
          </TabsTrigger>
          <TabsTrigger value="allocation" className="data-[state=active]:bg-trading-accent">
            <PieChart className="h-4 w-4 mr-2" />
            Allocation
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            <BarChart className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="holdings">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Current Holdings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {holdings.map((holding, index) => (
                  <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-4">
                        <span className="font-medium text-trading-light text-lg">{holding.symbol}</span>
                        <Badge variant="outline" className="text-blue-400 border-blue-400">
                          Qty: {holding.qty}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm">View Chart</Button>
                        <Button size="sm">Trade</Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      <div>
                        <div className="text-xs text-trading-muted">Avg Price</div>
                        <div className="text-sm text-trading-light">₹{holding.avgPrice}</div>
                      </div>
                      <div>
                        <div className="text-xs text-trading-muted">LTP</div>
                        <div className="text-sm text-trading-light">₹{holding.ltp}</div>
                      </div>
                      <div>
                        <div className="text-xs text-trading-muted">P&L</div>
                        <div className={`text-sm font-medium ${holding.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          ₹{holding.pnl}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-trading-muted">P&L %</div>
                        <div className={`text-sm font-medium ${holding.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {holding.pnlPercent >= 0 ? '+' : ''}{holding.pnlPercent}%
                        </div>
                      </div>
                      <div>
                        <div className="text-xs text-trading-muted">Market Value</div>
                        <div className="text-sm text-trading-light">₹{(holding.qty * holding.ltp).toLocaleString()}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="allocation">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Sector Allocation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sectorAllocation.map((sector, index) => (
                  <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-trading-light">{sector.sector}</span>
                      <div className="flex items-center space-x-3">
                        <span className="text-trading-light">{sector.value}</span>
                        <Badge variant="outline" className={sector.change.startsWith('+') ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                          {sector.change}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex-1 bg-trading-darker rounded-full h-2">
                        <div 
                          className="bg-blue-400 h-2 rounded-full" 
                          style={{ width: `${sector.allocation}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-trading-muted">{sector.allocation}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Performance Chart</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-trading-dark border border-trading-border rounded-lg flex items-center justify-center">
                <div className="text-center text-trading-muted">
                  <BarChart className="h-12 w-12 mx-auto mb-2" />
                  <div className="text-lg">Portfolio Performance Chart</div>
                  <div className="text-sm">Historical returns, benchmark comparison, drawdown analysis</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Advanced Portfolio Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <div className="text-sm text-trading-muted">Risk Score</div>
                  <div className="text-2xl font-bold text-yellow-400">6.7/10</div>
                  <div className="text-xs text-trading-muted">Moderate Risk</div>
                </div>
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <div className="text-sm text-trading-muted">Sharpe Ratio</div>
                  <div className="text-2xl font-bold text-green-400">1.43</div>
                  <div className="text-xs text-trading-muted">Risk-adjusted return</div>
                </div>
                <div className="p-4 bg-trading-dark rounded border border-trading-border text-center">
                  <div className="text-sm text-trading-muted">Beta</div>
                  <div className="text-2xl font-bold text-blue-400">0.87</div>
                  <div className="text-xs text-trading-muted">vs NIFTY 50</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
