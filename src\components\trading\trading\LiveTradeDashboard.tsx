
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, DollarSign, Shield, AlertTriangle } from "lucide-react";

interface LivePosition {
  symbol: string;
  qty: number;
  avgPrice: number;
  ltp: number;
  pnl: number;
  pnlPct: number;
  type: 'LONG' | 'SHORT';
}

interface LiveOrder {
  symbol: string;
  type: 'BUY' | 'SELL';
  qty: number;
  price: number;
  status: 'PENDING' | 'TRIGGERED' | 'EXECUTED' | 'CANCELLED';
}

interface LiveTradeDashboardProps {
  balance?: number;
  todaysPnL?: number;
  todaysPnLPercent?: number;
  positions?: LivePosition[];
  orders?: LiveOrder[];
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  capitalUtilized?: number;
  dailyRiskLimit?: number;
  dailyRiskUsed?: number;
  positionSizePercent?: number;
  stopLossPercent?: number;
  maxPositions?: number;
  onModifyPosition?: (symbol: string) => void;
  onSquareOffPosition?: (symbol: string) => void;
  onCancelOrder?: (orderId: string) => void;
  onSquareOffAll?: () => void;
  onCancelAllOrders?: () => void;
  onEmergencyStop?: () => void;
}

export const LiveTradeDashboard = ({
  balance = 0,
  todaysPnL = 0,
  todaysPnLPercent = 0,
  positions = [],
  orders = [],
  riskLevel = 'LOW',
  capitalUtilized = 0,
  dailyRiskLimit = 0,
  dailyRiskUsed = 0,
  positionSizePercent = 2,
  stopLossPercent = -1,
  maxPositions = 5,
  onModifyPosition,
  onSquareOffPosition,
  onCancelOrder,
  onSquareOffAll,
  onCancelAllOrders,
  onEmergencyStop
}: LiveTradeDashboardProps) => {
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'text-green-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'HIGH': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-4">
      {/* Live Trading Warning */}
      <Card className="bg-red-900/20 border-red-500/50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-red-400" />
            <div>
              <div className="text-red-400 font-medium">Live Trading Mode</div>
              <div className="text-sm text-red-300">Real money at risk. All trades will be executed with actual capital.</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Live Trading Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Live Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">₹{balance.toLocaleString()}</div>
            <div className="text-xs text-blue-400">Available Margin</div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Today's P&L</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${todaysPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {todaysPnL >= 0 ? '+' : ''}₹{todaysPnL.toLocaleString()}
            </div>
            <div className={`text-xs ${todaysPnLPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {todaysPnLPercent >= 0 ? '+' : ''}{todaysPnLPercent.toFixed(2)}%
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Active Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-trading-light">{positions.length}</div>
            <div className="text-xs text-trading-muted">
              {positions.filter(p => p.qty > 0).length} Long • {positions.filter(p => p.qty < 0).length} Short
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-trading-muted">Risk Level</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getRiskLevelColor(riskLevel)}`}>{riskLevel}</div>
            <div className="text-xs text-trading-muted">{capitalUtilized}% capital utilized</div>
          </CardContent>
        </Card>
      </div>

      {/* Live Positions */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Live Positions
            <Badge className="ml-2 bg-red-600">LIVE</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {positions.length === 0 ? (
            <div className="text-center py-8 text-trading-muted">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No live positions</p>
            </div>
          ) : (
            <div className="space-y-3">
              {positions.map((position, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="font-medium text-trading-light">{position.symbol}</div>
                      <div className="text-xs text-trading-muted">
                        Qty: {position.qty} • Avg: ₹{position.avgPrice.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-trading-light">₹{position.ltp.toFixed(2)}</div>
                    <div className={`text-xs ${position.pnl >= 0 ? "text-green-400" : "text-red-400"}`}>
                      {position.pnl >= 0 ? "+" : ""}₹{position.pnl.toFixed(2)} ({position.pnlPct >= 0 ? "+" : ""}{position.pnlPct.toFixed(2)}%)
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onModifyPosition?.(position.symbol)}
                    >
                      Modify
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => onSquareOffPosition?.(position.symbol)}
                    >
                      Square Off
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Live Orders */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Live Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-8 text-trading-muted">
              <DollarSign className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No pending orders</p>
            </div>
          ) : (
            <div className="space-y-3">
              {orders.map((order, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-4">
                    <Badge variant="outline" className={order.type === "BUY" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                      {order.type}
                    </Badge>
                    <div>
                      <div className="font-medium text-trading-light">{order.symbol}</div>
                      <div className="text-xs text-trading-muted">
                        Qty: {order.qty} • Price: ₹{order.price.toFixed(2)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="outline" 
                      className={
                        order.status === "TRIGGERED" || order.status === "EXECUTED" ? "text-green-400 border-green-400" :
                        order.status === "PENDING" ? "text-yellow-400 border-yellow-400" :
                        "text-red-400 border-red-400"
                      }
                    >
                      {order.status}
                    </Badge>
                    {order.status === "PENDING" && (
                      <Button 
                        variant="destructive" 
                        size="sm"
                        onClick={() => onCancelOrder?.(order.symbol)}
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Risk Management */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Risk Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Daily Risk Limit</div>
              <div className="text-lg font-bold text-green-400">₹{dailyRiskLimit.toLocaleString()}</div>
              <div className="text-xs text-trading-muted">Used: ₹{dailyRiskUsed.toLocaleString()}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Position Size</div>
              <div className="text-lg font-bold text-blue-400">{positionSizePercent}%</div>
              <div className="text-xs text-trading-muted">Per trade</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Stop Loss</div>
              <div className="text-lg font-bold text-red-400">{stopLossPercent}%</div>
              <div className="text-xs text-trading-muted">Auto trigger</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Max Positions</div>
              <div className="text-lg font-bold text-yellow-400">{maxPositions}</div>
              <div className="text-xs text-trading-muted">Current: {positions.length}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Emergency Controls */}
      <Card className="bg-red-900/20 border-red-500/50">
        <CardHeader>
          <CardTitle className="text-red-400 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Emergency Controls
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button 
              variant="destructive" 
              className="bg-red-600 hover:bg-red-700"
              onClick={onSquareOffAll}
            >
              Square Off All
            </Button>
            <Button 
              variant="destructive" 
              className="bg-red-600 hover:bg-red-700"
              onClick={onCancelAllOrders}
            >
              Cancel All Orders
            </Button>
            <Button 
              variant="destructive" 
              className="bg-red-600 hover:bg-red-700"
              onClick={onEmergencyStop}
            >
              Emergency Stop
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
