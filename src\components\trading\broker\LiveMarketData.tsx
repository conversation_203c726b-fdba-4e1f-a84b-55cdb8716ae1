
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { BrokerAPIService, TickData } from "../../../services/BrokerAPIService";
import { TrendingUp, TrendingDown, Plus, X } from "lucide-react";

interface LiveMarketDataProps {
  brokerService: BrokerAPIService | null;
}

export const LiveMarketData: React.FC<LiveMarketDataProps> = ({ brokerService }) => {
  const [subscribedSymbols, setSubscribedSymbols] = useState<Set<string>>(new Set(['NIFTY', 'BANKNIFTY']));
  const [tickData, setTickData] = useState<Map<string, TickData>>(new Map());
  const [newSymbol, setNewSymbol] = useState('');

  useEffect(() => {
    if (!brokerService) return;

    const callbacks = new Map<string, (data: TickData) => void>();

    // Subscribe to initial symbols
    subscribedSymbols.forEach(symbol => {
      const callback = (data: TickData) => {
        setTickData(prev => new Map(prev.set(symbol, data)));
      };
      callbacks.set(symbol, callback);
      brokerService.subscribeToSymbol(symbol, callback);
    });

    return () => {
      // Cleanup subscriptions
      callbacks.forEach((callback, symbol) => {
        brokerService.unsubscribeFromSymbol(symbol, callback);
      });
    };
  }, [brokerService, subscribedSymbols]);

  const addSymbol = () => {
    if (newSymbol.trim() && !subscribedSymbols.has(newSymbol.toUpperCase())) {
      setSubscribedSymbols(prev => new Set([...prev, newSymbol.toUpperCase()]));
      setNewSymbol('');
    }
  };

  const removeSymbol = (symbol: string) => {
    setSubscribedSymbols(prev => {
      const newSet = new Set(prev);
      newSet.delete(symbol);
      return newSet;
    });
    setTickData(prev => {
      const newMap = new Map(prev);
      newMap.delete(symbol);
      return newMap;
    });
  };

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-IN', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  };

  const formatChange = (change: number, changePercent: number) => {
    const isPositive = change >= 0;
    const color = isPositive ? 'text-green-400' : 'text-red-400';
    const icon = isPositive ? TrendingUp : TrendingDown;
    const Icon = icon;

    return (
      <div className={`flex items-center ${color}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span>₹{Math.abs(change).toFixed(2)} ({changePercent.toFixed(2)}%)</span>
      </div>
    );
  };

  if (!brokerService) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="p-6 text-center">
          <p className="text-trading-muted">Connect to a broker to view live market data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Live Market Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2 mb-4">
            <Input
              value={newSymbol}
              onChange={(e) => setNewSymbol(e.target.value.toUpperCase())}
              placeholder="Add symbol (e.g., RELIANCE)"
              className="bg-trading-dark border-trading-border text-trading-light"
              onKeyPress={(e) => e.key === 'Enter' && addSymbol()}
            />
            <Button onClick={addSymbol} size="icon">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid gap-3">
            {Array.from(subscribedSymbols).map(symbol => {
              const data = tickData.get(symbol);
              
              return (
                <Card key={symbol} className="bg-trading-dark border-trading-border">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-trading-light">{symbol}</span>
                        <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                      </div>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => removeSymbol(symbol)}
                        className="h-6 w-6"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>

                    {data ? (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xl font-bold text-trading-light">
                            ₹{formatPrice(data.ltp)}
                          </span>
                          {formatChange(data.change, data.changePercent)}
                        </div>

                        <div className="grid grid-cols-4 gap-2 text-xs">
                          <div>
                            <div className="text-trading-muted">High</div>
                            <div className="text-trading-light">₹{formatPrice(data.high)}</div>
                          </div>
                          <div>
                            <div className="text-trading-muted">Low</div>
                            <div className="text-trading-light">₹{formatPrice(data.low)}</div>
                          </div>
                          <div>
                            <div className="text-trading-muted">Volume</div>
                            <div className="text-trading-light">{(data.volume / 1000000).toFixed(1)}M</div>
                          </div>
                          <div>
                            <div className="text-trading-muted">VWAP</div>
                            <div className="text-trading-light">₹{formatPrice(data.vwap)}</div>
                          </div>
                        </div>

                        <div className="flex justify-between text-xs text-trading-muted">
                          <span>Bid: ₹{formatPrice(data.bid)}</span>
                          <span>Ask: ₹{formatPrice(data.ask)}</span>
                          <span>Updated: {new Date(data.timestamp).toLocaleTimeString()}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <div className="text-trading-muted">Loading...</div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
