
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { TradingStrategy } from "@/types/strategies";
import { TrendingUp, Activity, Zap, Target, Brain, DollarSign } from "lucide-react";

interface StrategyScannerProps {
  strategy: TradingStrategy;
  onToggle: (strategyId: string, isActive: boolean) => void;
  onConfigure: (strategyId: string) => void;
}

export const StrategyScanner = ({ strategy, onToggle, onConfigure }: StrategyScannerProps) => {
  const getCategoryIcon = () => {
    switch (strategy.category) {
      case 'Swing': return TrendingUp;
      case 'Intraday': return Activity;
      case 'Scalping': return Zap;
      case 'Price Action': return Brain;
      case 'Options': return DollarSign;
      default: return Target;
    }
  };

  const getCategoryColor = () => {
    switch (strategy.category) {
      case 'Swing': return 'text-green-400 border-green-400';
      case 'Intraday': return 'text-blue-400 border-blue-400';
      case 'Scalping': return 'text-purple-400 border-purple-400';
      case 'Price Action': return 'text-orange-400 border-orange-400';
      case 'Options': return 'text-yellow-400 border-yellow-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const getRiskColor = () => {
    switch (strategy.riskLevel) {
      case 'Low': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'High': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const Icon = getCategoryIcon();

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-trading-light flex items-center">
            <Icon className="h-4 w-4 mr-2" />
            {strategy.name}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${strategy.isActive ? 'bg-green-500' : 'bg-gray-500'}`}></div>
            <Switch 
              checked={strategy.isActive} 
              onCheckedChange={(checked) => onToggle(strategy.id, checked)}
            />
          </div>
        </div>
        <div className="flex space-x-2">
          <Badge variant="outline" className={`text-xs ${getCategoryColor()}`}>
            {strategy.category}
          </Badge>
          <Badge variant="outline" className="text-xs text-trading-muted">
            {strategy.timeframe}
          </Badge>
          <Badge variant="outline" className={`text-xs ${getRiskColor()}`}>
            {strategy.riskLevel} Risk
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="text-center">
            <div className="text-green-400 font-medium">{strategy.winRate}%</div>
            <div className="text-trading-muted">Win Rate</div>
          </div>
          <div className="text-center">
            <div className="text-blue-400 font-medium">{strategy.confidence}%</div>
            <div className="text-trading-muted">Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-trading-light font-medium">{strategy.totalSignals}</div>
            <div className="text-trading-muted">Signals</div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="text-xs text-trading-muted">Indicators:</div>
          <div className="flex flex-wrap gap-1">
            {strategy.indicators.map((indicator, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {indicator}
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="text-xs text-trading-muted">Criteria:</div>
          <div className="space-y-1">
            {strategy.criteria.slice(0, 2).map((criterion, index) => (
              <div key={index} className="text-xs text-trading-light bg-trading-dark p-1 rounded">
                • {criterion}
              </div>
            ))}
            {strategy.criteria.length > 2 && (
              <div className="text-xs text-trading-muted">
                +{strategy.criteria.length - 2} more criteria
              </div>
            )}
          </div>
        </div>

        <Button 
          size="sm" 
          variant="outline" 
          className="w-full text-xs"
          onClick={() => onConfigure(strategy.id)}
        >
          Configure Strategy
        </Button>
      </CardContent>
    </Card>
  );
};
