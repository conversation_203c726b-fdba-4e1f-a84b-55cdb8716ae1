
import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Search, Filter, Brain, DollarSign, TrendingUp, Activity, Zap } from "lucide-react";
import { TradingStrategy } from "@/types/strategies";
import { StrategyScanner } from "./StrategyScanner";
import { ScannerFilter } from "@/types/strategies";

interface ScannerStats {
  total?: number;
  active?: number;
  totalSignals?: number;
  avgWinRate?: number;
  avgConfidence?: number;
}

interface ComprehensiveScannerDashboardProps {
  strategies?: TradingStrategy[];
  stats?: ScannerStats;
  isLoading?: boolean;
  onToggleStrategy?: (strategyId: string, isActive: boolean) => void;
  onConfigureStrategy?: (strategyId: string) => void;
  onGlobalToggle?: (active: boolean) => void;
}

export const ComprehensiveScannerDashboard = ({
  strategies = [],
  stats,
  isLoading = false,
  onToggleStrategy,
  onConfigureStrategy,
  onGlobalToggle
}: ComprehensiveScannerDashboardProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [globalActive, setGlobalActive] = useState(true);
  const [filters, setFilters] = useState<ScannerFilter>({});

  const filteredStrategies = useMemo(() => {
    let filtered = strategies.filter(strategy => {
      const matchesSearch = strategy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           strategy.indicators.some(ind => ind.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = !filters.category?.length || filters.category.includes(strategy.category);
      const matchesTimeframe = !filters.timeframe?.length || filters.timeframe.includes(strategy.timeframe);
      const matchesConfidence = !filters.minConfidence || strategy.confidence >= filters.minConfidence;
      const matchesWinRate = !filters.minWinRate || strategy.winRate >= filters.minWinRate;
      const matchesRisk = !filters.riskLevel?.length || filters.riskLevel.includes(strategy.riskLevel);

      return matchesSearch && matchesCategory && matchesTimeframe && matchesConfidence && matchesWinRate && matchesRisk;
    });

    return filtered;
  }, [strategies, searchTerm, filters]);

  const handleToggleStrategy = (strategyId: string, isActive: boolean) => {
    if (onToggleStrategy) {
      onToggleStrategy(strategyId, isActive);
    }
  };

  const handleConfigureStrategy = (strategyId: string) => {
    if (onConfigureStrategy) {
      onConfigureStrategy(strategyId);
    } else {
      console.log(`Configuring strategy: ${strategyId}`);
    }
  };

  const handleGlobalToggle = (checked: boolean) => {
    setGlobalActive(checked);
    if (onGlobalToggle) {
      onGlobalToggle(checked);
    }
  };

  const categorizedStrategies = {
    'Price Action': filteredStrategies.filter(s => s.category === 'Price Action'),
    'Options': filteredStrategies.filter(s => s.category === 'Options'),
    'Swing': filteredStrategies.filter(s => s.category === 'Swing'),
    'Intraday': filteredStrategies.filter(s => s.category === 'Intraday'),
    'Scalping': filteredStrategies.filter(s => s.category === 'Scalping')
  };

  return (
    <div className="space-y-6">
      {/* Global Stats */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-trading-light">Strategy Scanner System</CardTitle>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-trading-light">Master Control</span>
                <Switch checked={globalActive} onCheckedChange={handleGlobalToggle} disabled={isLoading} />
              </div>
              <Badge variant="outline" className={globalActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                {globalActive ? "All Active" : "All Inactive"}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-trading-light">
                {isLoading ? '...' : (stats?.total ?? '--')}
              </div>
              <div className="text-sm text-trading-muted">Total Strategies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {isLoading ? '...' : (stats?.active ?? '--')}
              </div>
              <div className="text-sm text-trading-muted">Active Scanners</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {isLoading ? '...' : (stats?.totalSignals ?? '--')}
              </div>
              <div className="text-sm text-trading-muted">Total Signals</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {isLoading ? '...' : (stats?.avgWinRate ? `${stats.avgWinRate}%` : '--')}
              </div>
              <div className="text-sm text-trading-muted">Avg Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {isLoading ? '...' : (stats?.avgConfidence ? `${stats.avgConfidence}%` : '--')}
              </div>
              <div className="text-sm text-trading-muted">Avg Confidence</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-trading-muted" />
                <Input
                  placeholder="Search strategies or indicators..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                  disabled={isLoading}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select onValueChange={(value) => setFilters(prev => ({ ...prev, category: value ? [value] : [] }))} disabled={isLoading}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Price Action">Price Action</SelectItem>
                  <SelectItem value="Options">Options</SelectItem>
                  <SelectItem value="Swing">Swing</SelectItem>
                  <SelectItem value="Intraday">Intraday</SelectItem>
                  <SelectItem value="Scalping">Scalping</SelectItem>
                </SelectContent>
              </Select>
              <Select onValueChange={(value) => setFilters(prev => ({ ...prev, timeframe: value ? [value] : [] }))} disabled={isLoading}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1m">1 Minute</SelectItem>
                  <SelectItem value="5m">5 Minutes</SelectItem>
                  <SelectItem value="15m">15 Minutes</SelectItem>
                  <SelectItem value="30m">30 Minutes</SelectItem>
                  <SelectItem value="1h">1 Hour</SelectItem>
                  <SelectItem value="Daily">Daily</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm" disabled={isLoading}>
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6 bg-trading-darker">
          <TabsTrigger value="all" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            All ({filteredStrategies.length})
          </TabsTrigger>
          <TabsTrigger value="price-action" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            <Brain className="h-4 w-4 mr-1" />
            Price Action ({categorizedStrategies['Price Action'].length})
          </TabsTrigger>
          <TabsTrigger value="options" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            <DollarSign className="h-4 w-4 mr-1" />
            Options ({categorizedStrategies['Options'].length})
          </TabsTrigger>
          <TabsTrigger value="swing" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            <TrendingUp className="h-4 w-4 mr-1" />
            Swing ({categorizedStrategies['Swing'].length})
          </TabsTrigger>
          <TabsTrigger value="intraday" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            <Activity className="h-4 w-4 mr-1" />
            Intraday ({categorizedStrategies['Intraday'].length})
          </TabsTrigger>
          <TabsTrigger value="scalping" className="data-[state=active]:bg-trading-accent" disabled={isLoading}>
            <Zap className="h-4 w-4 mr-1" />
            Scalping ({categorizedStrategies['Scalping'].length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          {filteredStrategies.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredStrategies.map((strategy) => (
                <StrategyScanner
                  key={strategy.id}
                  strategy={strategy}
                  onToggle={handleToggleStrategy}
                  onConfigure={handleConfigureStrategy}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              <div className="text-lg text-trading-muted mb-2">
                {isLoading ? 'Loading strategies...' : 'No strategies available'}
              </div>
              <div className="text-sm text-trading-muted">
                {isLoading ? 'Fetching scanner strategies...' : 'Connect to data feed to load trading strategies'}
              </div>
            </div>
          )}
        </TabsContent>

        {Object.entries(categorizedStrategies).map(([category, categoryStrategies]) => (
          <TabsContent key={category} value={category.toLowerCase().replace(' ', '-')}>
            {categoryStrategies.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categoryStrategies.map((strategy) => (
                  <StrategyScanner
                    key={strategy.id}
                    strategy={strategy}
                    onToggle={handleToggleStrategy}
                    onConfigure={handleConfigureStrategy}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                <div className="text-lg text-trading-muted mb-2">
                  {isLoading ? 'Loading strategies...' : `No ${category} strategies available`}
                </div>
                <div className="text-sm text-trading-muted">
                  {isLoading ? 'Fetching scanner strategies...' : `Connect to data feed to load ${category} strategies`}
                </div>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
