
/* Futuristic Glassmorphism Design System */

/* Base glass utilities */
.glass {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 255, 136, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.3),
    rgba(0, 0, 0, 0.1));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 136, 0.3);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.2),
    inset 0 1px 0 0 rgba(0, 255, 136, 0.1);
}

.glassmorphism-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.08),
    rgba(255, 255, 255, 0.03));
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(0, 255, 136, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.glass-nav {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 255, 136, 0.2);
  box-shadow: 
    0 4px 16px 0 rgba(0, 255, 136, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

/* Futuristic trading specific effects */
.glass-trading-panel {
  background: linear-gradient(135deg, 
    rgba(26, 26, 26, 0.9),
    rgba(26, 26, 26, 0.7));
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(0, 255, 136, 0.3);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.2),
    inset 0 1px 0 0 rgba(0, 255, 136, 0.1);
}

.glass-chart {
  background: linear-gradient(135deg, 
    rgba(15, 15, 15, 0.95),
    rgba(15, 15, 15, 0.85));
  backdrop-filter: blur(35px);
  -webkit-backdrop-filter: blur(35px);
  border: 1px solid rgba(0, 255, 136, 0.25);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.2),
    inset 0 1px 0 0 rgba(0, 255, 136, 0.1);
}

/* Enhanced hover effects */
.glass-hover:hover {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.15),
    rgba(0, 255, 136, 0.05));
  border-color: rgba(0, 255, 136, 0.5);
  transform: translateY(-4px);
  transition: all 0.3s ease;
  box-shadow: 
    0 12px 40px 0 rgba(0, 255, 136, 0.3),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
}

.glass-button {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.1), 
    rgba(0, 255, 136, 0.05));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 136, 0.3);
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 16px 0 rgba(0, 255, 136, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

.glass-button:hover {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.2), 
    rgba(0, 255, 136, 0.1));
  border-color: rgba(0, 255, 136, 0.6);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 136, 0.3),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Futuristic status indicators */
.glass-status-positive {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.2),
    rgba(0, 255, 136, 0.1));
  border: 1px solid rgba(0, 255, 136, 0.6);
  backdrop-filter: blur(15px);
  box-shadow: 
    0 0 20px rgba(0, 255, 136, 0.3),
    inset 0 1px 0 0 rgba(0, 255, 136, 0.2);
}

.glass-status-negative {
  background: linear-gradient(135deg, 
    rgba(255, 0, 102, 0.2),
    rgba(255, 0, 102, 0.1));
  border: 1px solid rgba(255, 0, 102, 0.6);
  backdrop-filter: blur(15px);
  box-shadow: 
    0 0 20px rgba(255, 0, 102, 0.3),
    inset 0 1px 0 0 rgba(255, 0, 102, 0.2);
}

.glass-status-neutral {
  background: linear-gradient(135deg, 
    rgba(156, 163, 175, 0.2),
    rgba(156, 163, 175, 0.1));
  border: 1px solid rgba(156, 163, 175, 0.6);
  backdrop-filter: blur(15px);
  box-shadow: 
    0 0 20px rgba(156, 163, 175, 0.2),
    inset 0 1px 0 0 rgba(156, 163, 175, 0.2);
}

/* Cyber grid overlay */
.cyber-grid-overlay {
  position: relative;
}

.cyber-grid-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 1;
}

/* Matrix-style scrolling effect */
.matrix-bg {
  background: linear-gradient(180deg, 
    rgba(0, 255, 136, 0.05) 0%,
    transparent 50%,
    rgba(0, 255, 136, 0.05) 100%);
  background-size: 100% 200%;
  animation: matrix-scroll 4s linear infinite;
}

@keyframes matrix-scroll {
  0% { background-position: 0% 0%; }
  100% { background-position: 0% 200%; }
}
