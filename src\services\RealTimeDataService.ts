import { WebSocketManager, WebSocketMessage } from './WebSocketManager';
import { useMarketStore, MarketData, OrderBook } from '../store/useMarketStore';

export interface DataSubscription {
  symbol: string;
  types: ('tick' | 'orderbook' | 'trades')[];
}

export class RealTimeDataService {
  private wsManager: WebSocketManager;
  private marketStore = useMarketStore.getState();
  private subscriptions: Map<string, DataSubscription> = new Map();
  private dataBuffer: Map<string, MarketData[]> = new Map();
  private compressionEnabled = true;
  private updateThrottleMs = 100;
  private lastUpdateTime: Map<string, number> = new Map();

  constructor(wsUrl: string) {
    this.wsManager = new WebSocketManager({
      url: wsUrl,
      heartbeatInterval: 30000,
      reconnectInterval: 2000,
      maxReconnectAttempts: 10,
      compression: true
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Connection events
    this.wsManager.onConnection((connected: boolean) => {
      if (connected) {
        useMarketStore.getState().setConnectionStatus('connected');
        this.resubscribeAll();
      }
    });

    this.wsManager.onError(() => {
      useMarketStore.getState().setConnectionStatus('error');
    });

    // Market data handlers
    this.wsManager.subscribe('tick', this.handleTickData.bind(this));
    this.wsManager.subscribe('orderbook', this.handleOrderBookData.bind(this));
    this.wsManager.subscribe('latency', this.handleLatencyUpdate.bind(this));
    this.wsManager.subscribe('bulk_tick', this.handleBulkTickData.bind(this));
  }

  async connect(): Promise<void> {
    useMarketStore.getState().setConnectionStatus('connecting');
    try {
      console.log('Real-time data service connected');
    } catch (error) {
      useMarketStore.getState().setConnectionStatus('error');
      throw error;
    }
  }

  disconnect(): void {
    this.wsManager.disconnect();
    useMarketStore.getState().setConnectionStatus('disconnected');
    this.subscriptions.clear();
    this.dataBuffer.clear();
    this.lastUpdateTime.clear();
  }

  subscribeToSymbol(symbol: string, types: ('tick' | 'orderbook' | 'trades')[] = ['tick']): void {
    const subscription: DataSubscription = { symbol, types };
    this.subscriptions.set(symbol, subscription);
    
    useMarketStore.getState().addSubscription(symbol);

    if (this.wsManager.isConnected) {
      this.sendSubscription(subscription);
    }
  }

  unsubscribeFromSymbol(symbol: string): void {
    if (this.subscriptions.has(symbol)) {
      const subscription = this.subscriptions.get(symbol)!;
      this.sendUnsubscription(subscription);
      this.subscriptions.delete(symbol);
      
      useMarketStore.getState().removeSubscription(symbol);
      this.dataBuffer.delete(symbol);
      this.lastUpdateTime.delete(symbol);
    }
  }

  private sendSubscription(subscription: DataSubscription): void {
    const message: WebSocketMessage = {
      type: 'subscribe',
      data: {
        symbol: subscription.symbol,
        types: subscription.types,
        compression: this.compressionEnabled
      }
    };
    
    this.wsManager.send(message);
    console.log(`Subscribed to ${subscription.symbol} for ${subscription.types.join(', ')}`);
  }

  private sendUnsubscription(subscription: DataSubscription): void {
    const message: WebSocketMessage = {
      type: 'unsubscribe',
      data: {
        symbol: subscription.symbol,
        types: subscription.types
      }
    };
    
    this.wsManager.send(message);
    console.log(`Unsubscribed from ${subscription.symbol}`);
  }

  private resubscribeAll(): void {
    console.log('Resubscribing to all symbols...');
    this.subscriptions.forEach(subscription => {
      this.sendSubscription(subscription);
    });
  }

  private handleTickData(message: WebSocketMessage): void {
    const { symbol, data } = message;
    if (!symbol || !data) return;

    const marketData: MarketData = {
      symbol,
      price: data.ltp || data.price,
      change: data.change || 0,
      changePercent: data.changePercent || 0,
      volume: data.volume || 0,
      high: data.high || data.price,
      low: data.low || data.price,
      open: data.open || data.price,
      bid: data.bid || 0,
      ask: data.ask || 0,
      vwap: data.vwap || data.price,
      timestamp: data.timestamp || Date.now()
    };

    this.throttledUpdate(symbol, marketData);
  }

  private handleBulkTickData(message: WebSocketMessage): void {
    const { data } = message;
    if (!Array.isArray(data)) return;

    data.forEach((tickData: any) => {
      this.handleTickData({
        type: 'tick',
        symbol: tickData.symbol,
        data: tickData
      });
    });
  }

  private handleOrderBookData(message: WebSocketMessage): void {
    const { symbol, data } = message;
    if (!symbol || !data) return;

    const orderBook: OrderBook = {
      symbol,
      bids: data.bids || [],
      asks: data.asks || [],
      timestamp: data.timestamp || Date.now()
    };

    useMarketStore.getState().updateOrderBook(symbol, orderBook);
  }

  private handleLatencyUpdate(message: WebSocketMessage): void {
    const { data } = message;
    if (data.latency) {
      useMarketStore.getState().setLatency(data.latency);
    }
  }

  private throttledUpdate(symbol: string, marketData: MarketData): void {
    const now = Date.now();
    const lastUpdate = this.lastUpdateTime.get(symbol) || 0;

    if (now - lastUpdate >= this.updateThrottleMs) {
      useMarketStore.getState().updateMarketData(symbol, marketData);
      this.lastUpdateTime.set(symbol, now);
    } else {
      // Buffer the update for later
      if (!this.dataBuffer.has(symbol)) {
        this.dataBuffer.set(symbol, []);
      }
      this.dataBuffer.get(symbol)!.push(marketData);
    }
  }

  // Flush buffered updates periodically
  private flushBufferedUpdates(): void {
    this.dataBuffer.forEach((buffer, symbol) => {
      if (buffer.length > 0) {
        const latestData = buffer[buffer.length - 1];
        useMarketStore.getState().updateMarketData(symbol, latestData);
        this.dataBuffer.set(symbol, []);
        this.lastUpdateTime.set(symbol, Date.now());
      }
    });
  }

  startBufferFlush(): void {
    setInterval(() => {
      this.flushBufferedUpdates();
    }, this.updateThrottleMs);
  }

  // Public getters
  get isConnected(): boolean {
    return this.wsManager.isConnected;
  }

  get connectionState(): string {
    return this.wsManager.connectionState;
  }

  get subscribedSymbols(): string[] {
    return Array.from(this.subscriptions.keys());
  }

  // Configuration methods
  setUpdateThrottle(ms: number): void {
    this.updateThrottleMs = Math.max(50, ms); // Min 50ms
  }

  enableCompression(enabled: boolean): void {
    this.compressionEnabled = enabled;
  }
}

// Export singleton instance
export const realTimeDataService = new RealTimeDataService(
  process.env.NODE_ENV === 'production' 
    ? 'wss://api.your-trading-platform.com/ws'
    : 'ws://localhost:8080/ws'
);
