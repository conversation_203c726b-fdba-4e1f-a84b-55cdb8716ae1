export interface TechnicalIndicatorResult {
  timestamp: number;
  value: number;
  signal?: 'buy' | 'sell' | 'hold';
}

export interface RSIResult extends TechnicalIndicatorResult {
  oversold: boolean;
  overbought: boolean;
}

export interface MACDResult extends TechnicalIndicatorResult {
  macd: number;
  signalLine: number;
  histogram: number;
  crossover?: 'bullish' | 'bearish';
}

export interface VWAPResult extends TechnicalIndicatorResult {
  vwap: number;
  deviation: number;
}

export interface BollingerBandsResult extends TechnicalIndicatorResult {
  upper: number;
  middle: number;
  lower: number;
  bandwidth: number;
  squeeze: boolean;
}

export class TechnicalIndicatorService {
  // Simple Moving Average
  static calculateSMA(prices: number[], period: number): number[] {
    const sma: number[] = [];
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      sma.push(sum / period);
    }
    return sma;
  }

  // Exponential Moving Average
  static calculateEMA(prices: number[], period: number): number[] {
    const ema: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    let smaSum = 0;
    for (let i = 0; i < period; i++) {
      smaSum += prices[i];
    }
    ema.push(smaSum / period);
    
    // Calculate EMA for rest
    for (let i = period; i < prices.length; i++) {
      const currentEma = (prices[i] * multiplier) + (ema[ema.length - 1] * (1 - multiplier));
      ema.push(currentEma);
    }
    
    return ema;
  }

  // RSI Calculation
  static calculateRSI(prices: number[], period: number = 14): RSIResult[] {
    const rsi: RSIResult[] = [];
    const gains: number[] = [];
    const losses: number[] = [];
    
    // Calculate price changes
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    // Calculate RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
      
      const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
      const rsiValue = 100 - (100 / (1 + rs));
      
      rsi.push({
        timestamp: Date.now() + i * 60000,
        value: rsiValue,
        oversold: rsiValue < 30,
        overbought: rsiValue > 70,
        signal: rsiValue < 30 ? 'buy' : rsiValue > 70 ? 'sell' : 'hold'
      });
    }
    
    return rsi;
  }

  // MACD Calculation
  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): MACDResult[] {
    const fastEMA = this.calculateEMA(prices, fastPeriod);
    const slowEMA = this.calculateEMA(prices, slowPeriod);
    const macd: MACDResult[] = [];
    
    // Calculate MACD line
    const macdLine: number[] = [];
    const startIndex = slowPeriod - fastPeriod;
    
    for (let i = 0; i < fastEMA.length - startIndex; i++) {
      macdLine.push(fastEMA[i + startIndex] - slowEMA[i]);
    }
    
    // Calculate signal line
    const signalLine = this.calculateEMA(macdLine, signalPeriod);
    
    // Calculate histogram and crossovers
    for (let i = 0; i < signalLine.length; i++) {
      const histogram = macdLine[i + signalPeriod - 1] - signalLine[i];
      const prevHistogram = i > 0 ? macdLine[i + signalPeriod - 2] - signalLine[i - 1] : 0;
      
      let crossover: 'bullish' | 'bearish' | undefined;
      if (prevHistogram <= 0 && histogram > 0) crossover = 'bullish';
      if (prevHistogram >= 0 && histogram < 0) crossover = 'bearish';
      
      macd.push({
        timestamp: Date.now() + i * 60000,
        value: macdLine[i + signalPeriod - 1],
        macd: macdLine[i + signalPeriod - 1],
        signalLine: signalLine[i],
        histogram,
        crossover,
        signal: crossover === 'bullish' ? 'buy' : crossover === 'bearish' ? 'sell' : 'hold'
      });
    }
    
    return macd;
  }

  // VWAP Calculation
  static calculateVWAP(prices: number[], volumes: number[], timestamps: number[]): VWAPResult[] {
    const vwap: VWAPResult[] = [];
    let cumulativePV = 0;
    let cumulativeVolume = 0;
    
    for (let i = 0; i < prices.length; i++) {
      const pv = prices[i] * volumes[i];
      cumulativePV += pv;
      cumulativeVolume += volumes[i];
      
      const vwapValue = cumulativeVolume > 0 ? cumulativePV / cumulativeVolume : prices[i];
      const deviation = Math.abs(prices[i] - vwapValue) / vwapValue * 100;
      
      vwap.push({
        timestamp: timestamps[i],
        value: vwapValue,
        vwap: vwapValue,
        deviation,
        signal: deviation > 2 ? (prices[i] > vwapValue ? 'sell' : 'buy') : 'hold'
      });
    }
    
    return vwap;
  }

  // Bollinger Bands Calculation
  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): BollingerBandsResult[] {
    const sma = this.calculateSMA(prices, period);
    const bands: BollingerBandsResult[] = [];
    
    for (let i = 0; i < sma.length; i++) {
      const priceSlice = prices.slice(i, i + period);
      const mean = sma[i];
      
      const variance = priceSlice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
      const standardDeviation = Math.sqrt(variance);
      
      const upper = mean + (standardDeviation * stdDev);
      const lower = mean - (standardDeviation * stdDev);
      const bandwidth = (upper - lower) / mean * 100;
      const squeeze = bandwidth < 10;
      
      bands.push({
        timestamp: Date.now() + i * 60000,
        value: mean,
        upper,
        middle: mean,
        lower,
        bandwidth,
        squeeze,
        signal: prices[i + period - 1] > upper ? 'sell' : prices[i + period - 1] < lower ? 'buy' : 'hold'
      });
    }
    
    return bands;
  }

  // Volume Profile Calculation
  static calculateVolumeProfile(prices: number[], volumes: number[], bins: number = 50): Array<{price: number, volume: number, percentage: number}> {
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    const binSize = priceRange / bins;
    
    const volumeProfile: Array<{price: number, volume: number, percentage: number}> = [];
    const totalVolume = volumes.reduce((sum, vol) => sum + vol, 0);
    
    for (let i = 0; i < bins; i++) {
      const binPrice = minPrice + (i * binSize);
      let binVolume = 0;
      
      for (let j = 0; j < prices.length; j++) {
        if (prices[j] >= binPrice && prices[j] < binPrice + binSize) {
          binVolume += volumes[j];
        }
      }
      
      volumeProfile.push({
        price: binPrice,
        volume: binVolume,
        percentage: (binVolume / totalVolume) * 100
      });
    }
    
    return volumeProfile.sort((a, b) => b.volume - a.volume);
  }
}
