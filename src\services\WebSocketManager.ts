
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface WebSocketConfig {
  url: string;
  heartbeatInterval?: number;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  compression?: boolean;
  enableCompression?: boolean;
}

export interface WebSocketMessage {
  type: string;
  symbol?: string;
  data?: any;
  timestamp?: number;
  sequence?: number;
}

export interface ConnectionState {
  isConnected: boolean;
  reconnectAttempts: number;
  lastError?: string;
  latency: number;
}

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private connectionStateSubject = new BehaviorSubject<ConnectionState>({
    isConnected: false,
    reconnectAttempts: 0,
    latency: 0
  });
  private messageSubject = new Subject<WebSocketMessage>();
  private dataSubject = new Subject<WebSocketMessage>();
  private orderSubject = new Subject<WebSocketMessage>();
  private heartbeatIntervalId: any;
  private reconnectTimeoutId: any;
  private lastMessageTimestamp: number = 0;
  public currentLatency: number = 0;
  private connectionCallbacks: ((connected: boolean) => void)[] = [];
  private errorCallbacks: ((error: any) => void)[] = [];
  private messageCallbacks: Map<string, ((message: WebSocketMessage) => void)[]> = new Map();

  constructor(config: WebSocketConfig) {
    this.config = config;
    this.connect();
  }

  onConnection(callback: (connected: boolean) => void): void {
    this.connectionCallbacks.push(callback);
  }

  onError(callback: (error: any) => void): void {
    this.errorCallbacks.push(callback);
  }

  subscribe(type: string, callback: (message: WebSocketMessage) => void): void {
    if (!this.messageCallbacks.has(type)) {
      this.messageCallbacks.set(type, []);
    }
    this.messageCallbacks.get(type)!.push(callback);
  }

  send(message: WebSocketMessage): void {
    this.sendMessage(message);
  }

  private connect(): void {
    this.ws = new WebSocket(this.config.url);
    this.ws.onopen = () => this.onOpen();
    this.ws.onmessage = (event) => this.onMessage(event);
    this.ws.onclose = (event) => this.onClose(event);
    this.ws.onerror = (error) => this.handleError(error);
  }

  private onOpen(): void {
    console.log('WebSocket connected');
    this.connectionStateSubject.next({
      ...this.connectionStateSubject.value,
      isConnected: true,
      reconnectAttempts: 0,
      lastError: undefined
    });
    this.startHeartbeat();
    this.connectionCallbacks.forEach(callback => callback(true));
  }

  private onMessage(event: MessageEvent): void {
    this.lastMessageTimestamp = Date.now();
    this.measureLatency();
    this.handleMessage(event);
  }

  private onClose(event: CloseEvent): void {
    console.log('WebSocket disconnected', event.reason);
    this.stopHeartbeat();
    this.connectionStateSubject.next({
      ...this.connectionStateSubject.value,
      isConnected: false,
      lastError: event.reason || 'Connection closed'
    });
    this.connectionCallbacks.forEach(callback => callback(false));
    this.reconnect();
  }

  private handleError(error: any): void {
    console.error('WebSocket error:', error);
    this.connectionStateSubject.next({
      ...this.connectionStateSubject.value,
      isConnected: false,
      lastError: error.message || 'WebSocket error'
    });
    this.errorCallbacks.forEach(callback => callback(error));
    this.reconnect();
  }

  private startHeartbeat(): void {
    if (this.config.heartbeatInterval) {
      this.heartbeatIntervalId = setInterval(() => {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
        }
      }, this.config.heartbeatInterval);
    }
  }

  private stopHeartbeat(): void {
    clearInterval(this.heartbeatIntervalId);
  }

  private reconnect(): void {
    if (this.connectionStateSubject.value.reconnectAttempts >= (this.config.maxReconnectAttempts || 10)) {
      console.error('Max reconnect attempts reached');
      return;
    }

    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
    }

    this.reconnectTimeoutId = setTimeout(() => {
      console.log('Attempting to reconnect...');
      this.connectionStateSubject.next({
        ...this.connectionStateSubject.value,
        reconnectAttempts: this.connectionStateSubject.value.reconnectAttempts + 1
      });
      this.connect();
    }, this.config.reconnectInterval || 2000);
  }

  private measureLatency(): void {
    const now = Date.now();
    this.currentLatency = now - this.lastMessageTimestamp;
    this.connectionStateSubject.next({
      ...this.connectionStateSubject.value,
      latency: this.currentLatency
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const rawData = JSON.parse(event.data);
      
      const message: WebSocketMessage = {
        type: rawData.type || 'data',
        symbol: rawData.symbol,
        data: rawData.data || rawData,
        timestamp: rawData.timestamp || Date.now(),
        sequence: rawData.sequence || 0
      };
      
      this.messageSubject.next(message);
      this.routeMessage(message);
      
      // Notify type-specific callbacks
      const callbacks = this.messageCallbacks.get(message.type);
      if (callbacks) {
        callbacks.forEach(callback => callback(message));
      }
      
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  private routeMessage(message: WebSocketMessage): void {
    // Route message to appropriate handlers based on type
    if (message.type === 'tick' || message.type === 'market_data') {
      this.dataSubject.next(message);
    } else if (message.type === 'order_update') {
      this.orderSubject.next(message);
    }
  }

  sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not open. Message not sent.', message);
    }
  }

  getMessageStream(): Observable<WebSocketMessage> {
    return this.messageSubject.asObservable();
  }

  getDataStream(): Observable<WebSocketMessage> {
    return this.dataSubject.asObservable();
  }

  getOrderStream(): Observable<WebSocketMessage> {
    return this.orderSubject.asObservable();
  }

  getConnectionState(): Observable<ConnectionState> {
    return this.connectionStateSubject.asObservable();
  }

  get isConnected(): boolean {
    return this.connectionStateSubject.value.isConnected;
  }

  get connectionState(): string {
    return this.connectionStateSubject.value.isConnected ? 'connected' : 'disconnected';
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
    }
    this.stopHeartbeat();
    clearTimeout(this.reconnectTimeoutId);
  }
}
