
import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from './card';

interface EnhancedCardProps {
  children: React.ReactNode;
  className?: string;
  glassEffect?: boolean;
  hover?: boolean;
  animation?: 'slide-in' | 'bounce-in' | 'fade-in';
  status?: 'positive' | 'negative' | 'neutral';
  onClick?: () => void;
  style?: React.CSSProperties;
}

export function EnhancedCard({ 
  children, 
  className,
  glassEffect = true,
  hover = true,
  animation,
  status,
  onClick,
  style
}: EnhancedCardProps) {
  const cardClasses = cn(
    glassEffect && 'glass-trading-panel',
    hover && 'glass-hover micro-lift',
    animation === 'slide-in' && 'slide-in-right',
    animation === 'bounce-in' && 'bounce-in',
    animation === 'fade-in' && 'animate-fade-in',
    status === 'positive' && 'glass-status-positive',
    status === 'negative' && 'glass-status-negative',
    status === 'neutral' && 'glass-status-neutral',
    onClick && 'cursor-pointer',
    className
  );

  return (
    <Card className={cardClasses} onClick={onClick} style={style}>
      {children}
    </Card>
  );
}

interface TradingCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  value?: string | number;
  change?: number;
  icon?: React.ReactNode;
  loading?: boolean;
  style?: React.CSSProperties;
}

export function TradingCard({ 
  title, 
  children, 
  className,
  value,
  change,
  icon,
  loading = false,
  style
}: TradingCardProps) {
  const isPositive = change !== undefined && change >= 0;
  
  return (
    <EnhancedCard 
      className={className}
      status={change !== undefined ? (isPositive ? 'positive' : 'negative') : 'neutral'}
      style={style}
    >
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-trading-light flex items-center">
          {icon && <span className="mr-2">{icon}</span>}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            <div className="skeleton h-6 w-20 rounded"></div>
            <div className="skeleton h-4 w-16 rounded"></div>
          </div>
        ) : (
          <div className="space-y-1">
            {value && (
              <div className="text-2xl font-bold text-trading-light">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </div>
            )}
            {change !== undefined && (
              <div className={cn(
                'text-sm font-medium flex items-center',
                isPositive ? 'text-green-400' : 'text-red-400',
                isPositive ? 'price-flash-green' : 'price-flash-red'
              )}>
                {isPositive ? '+' : ''}{change.toFixed(2)}%
              </div>
            )}
            {children}
          </div>
        )}
      </CardContent>
    </EnhancedCard>
  );
}
