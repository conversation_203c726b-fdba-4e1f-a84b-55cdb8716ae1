
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Search, Target, TrendingUp, Activity, Zap, Brain, Database } from "lucide-react";
import { ComprehensiveScannerDashboard } from "./ComprehensiveScannerDashboard";
import { ScannerTestingEngine } from "./ScannerTestingEngine";
import { AIDataExtractionAgent } from "../ai/AIDataExtractionAgent";
import { StrategyTabIntegration } from "../strategy/StrategyTabIntegration";

interface QuickStat {
  label: string;
  value: string;
  color: string;
}

interface RecentSignal {
  symbol: string;
  strategy: string;
  confidence: number;
  direction: string;
  time: string;
}

interface ScannerDashboardProps {
  quickStats?: QuickStat[];
  recentSignals?: RecentSignal[];
  isLoading?: boolean;
}

export const ScannerDashboard = ({
  quickStats = [],
  recentSignals = [],
  isLoading = false
}: ScannerDashboardProps) => {
  const defaultStats = [
    { label: "Active Scans", value: "53", color: "text-green-400" },
    { label: "Live Signals", value: "12", color: "text-blue-400" },
    { label: "High Confidence", value: "8", color: "text-yellow-400" },
    { label: "Avg Accuracy", value: "87.3%", color: "text-purple-400" },
  ];

  const statsToShow = quickStats.length > 0 ? quickStats : defaultStats;

  return (
    <div className="space-y-6">
      {/* Scanner Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Advanced Strategy Scanner & Real-Time Engine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {statsToShow.map((stat, index) => (
              <div key={index} className="text-center">
                <div className={`text-2xl font-bold ${stat.color}`}>
                  {isLoading ? '...' : stat.value}
                </div>
                <div className="text-sm text-trading-muted">{stat.label}</div>
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-trading-dark rounded">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <div className="text-lg font-bold text-green-400">15</div>
              <div className="text-sm text-trading-muted">Swing Strategies</div>
            </div>
            <div className="text-center p-4 bg-trading-dark rounded">
              <Activity className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <div className="text-lg font-bold text-blue-400">25</div>
              <div className="text-sm text-trading-muted">Intraday Strategies</div>
            </div>
            <div className="text-center p-4 bg-trading-dark rounded">
              <Zap className="h-8 w-8 mx-auto mb-2 text-purple-400" />
              <div className="text-lg font-bold text-purple-400">3</div>
              <div className="text-sm text-trading-muted">Scalping Strategies</div>
            </div>
            <div className="text-center p-4 bg-trading-dark rounded">
              <Database className="h-8 w-8 mx-auto mb-2 text-yellow-400" />
              <div className="text-lg font-bold text-yellow-400">10</div>
              <div className="text-sm text-trading-muted">Options Strategies</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="strategy-engine" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6 bg-trading-darker">
          <TabsTrigger value="strategy-engine" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            Strategy Engine
          </TabsTrigger>
          <TabsTrigger value="strategies" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            All Strategies
          </TabsTrigger>
          <TabsTrigger value="testing" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Testing Engine
          </TabsTrigger>
          <TabsTrigger value="signals" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Live Signals
          </TabsTrigger>
          <TabsTrigger value="ai-agent" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            AI Data Agent
          </TabsTrigger>
          <TabsTrigger value="optimization" className="data-[state=active]:bg-trading-accent">
            <Zap className="h-4 w-4 mr-2" />
            Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="strategy-engine">
          <StrategyTabIntegration />
        </TabsContent>

        <TabsContent value="strategies">
          <ComprehensiveScannerDashboard />
        </TabsContent>

        <TabsContent value="testing">
          <ScannerTestingEngine />
        </TabsContent>

        <TabsContent value="signals">
          <div className="space-y-4">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Live Trading Signals</CardTitle>
              </CardHeader>
              <CardContent>
                {recentSignals.length > 0 ? (
                  <div className="space-y-3">
                    {recentSignals.map((signal, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center space-x-4">
                          <div className="text-trading-light font-medium">{signal.symbol}</div>
                          <Badge variant="outline" className={signal.direction === "Long" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                            {signal.direction}
                          </Badge>
                          <div className="text-sm text-trading-muted">{signal.strategy}</div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-sm">
                            <span className="text-trading-muted">Confidence: </span>
                            <span className="text-blue-400 font-medium">{signal.confidence}%</span>
                          </div>
                          <div className="text-xs text-trading-muted">{signal.time}</div>
                          <Button size="sm" variant="outline" disabled={isLoading}>View</Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-trading-muted">
                      {isLoading ? 'Loading signals...' : 'Strategy Engine is generating live signals...'}
                    </p>
                    <p className="text-sm text-trading-muted mt-1">
                      {isLoading ? 'Fetching real-time signals...' : 'Real-time strategy signals will appear here'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ai-agent">
          <AIDataExtractionAgent />
        </TabsContent>

        <TabsContent value="optimization">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Scanner Performance Optimization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Zap className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                <div className="text-lg text-trading-muted mb-2">
                  {isLoading ? 'Loading optimization data...' : 'Strategy optimization in progress...'}
                </div>
                <div className="text-sm text-trading-muted">
                  {isLoading ? 'Fetching optimization history...' : 'Real-time strategy optimization and performance tuning'}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
