
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Target, Settings, PieChart, Activity } from "lucide-react";
import { useState } from "react";

export const OptionsAgent = () => {
  const [isActive, setIsActive] = useState(false);
  const [deltaThreshold, setDeltaThreshold] = useState([30]);
  const [ivPercentile, setIvPercentile] = useState([70]);

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Target className="h-5 w-5 mr-2 text-purple-400" />
            Options Trading Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-yellow-400 border-yellow-400"}>
              {isActive ? "Active" : "Training"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Model Accuracy</div>
            <div className="text-2xl font-bold text-blue-400">78%</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Strategies Active</div>
            <div className="text-2xl font-bold text-trading-light">8</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Profit</div>
            <div className="text-2xl font-bold text-green-400">₹23,400</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Theta Decay</div>
            <div className="text-2xl font-bold text-trading-light">+₹2,340</div>
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Agent Status</span>
            <Switch checked={isActive} onCheckedChange={setIsActive} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Delta Threshold</span>
              <span className="text-sm text-trading-muted">{deltaThreshold[0]}%</span>
            </div>
            <Slider
              value={deltaThreshold}
              onValueChange={setDeltaThreshold}
              max={100}
              min={10}
              step={5}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">IV Percentile Filter</span>
              <span className="text-sm text-trading-muted">{ivPercentile[0]}%</span>
            </div>
            <Slider
              value={ivPercentile}
              onValueChange={setIvPercentile}
              max={100}
              min={20}
              step={5}
              className="w-full"
            />
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1">
            <Settings className="h-4 w-4 mr-2" />
            Strategy
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <PieChart className="h-4 w-4 mr-2" />
            Greeks
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Activity className="h-4 w-4 mr-2" />
            Chains
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
