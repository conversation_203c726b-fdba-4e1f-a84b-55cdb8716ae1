
import { BehaviorSubject, Observable } from 'rxjs';
import { z } from 'zod';

// Strategy validation schemas
const StrategySchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['intraday', 'swing', 'options', 'scalping', 'arbitrage']),
  indicators: z.array(z.string()),
  entry: z.string(),
  exit: z.string(),
  stopLoss: z.number(),
  takeProfit: z.number(),
  riskReward: z.number(),
  timeframe: z.string(),
  description: z.string(),
  isActive: z.boolean().default(false)
});

export interface Strategy {
  id: string;
  name: string;
  type: 'intraday' | 'swing' | 'options' | 'scalping' | 'arbitrage';
  indicators: string[];
  entry: string;
  exit: string;
  stopLoss: number;
  takeProfit: number;
  riskReward: number;
  timeframe: string;
  description: string;
  isActive: boolean;
  performance?: StrategyPerformance;
}

export interface StrategyPerformance {
  totalTrades: number;
  winRate: number;
  avgReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  lastUpdated: string;
}

export interface StrategySignal {
  strategyId: string;
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  price: number;
  confidence: number;
  timestamp: string;
  reason: string;
  stopLoss?: number;
  takeProfit?: number;
}

export interface ScannerResult {
  symbol: string;
  strategyId: string;
  score: number;
  signals: StrategySignal[];
  lastScanned: string;
  technicalIndicators: Record<string, number>;
}

export class StrategyEngineService {
  private strategies: Map<string, Strategy> = new Map();
  private signalsSubject = new BehaviorSubject<StrategySignal[]>([]);
  private scannerResultsSubject = new BehaviorSubject<ScannerResult[]>([]);
  private isRunning = false;
  private scanInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    // 51 Real Trading Strategies Implementation
    const strategyDefinitions: Partial<Strategy>[] = [
      // Intraday Strategies (15)
      {
        id: 'INTRADAY_EMA_CROSSOVER',
        name: 'EMA Crossover Intraday',
        type: 'intraday',
        indicators: ['EMA_9', 'EMA_21', 'VOLUME', 'RSI'],
        entry: 'EMA(9) crosses above EMA(21) with volume > 1.5x avg and RSI > 50',
        exit: 'EMA(9) crosses below EMA(21) or 3:15 PM',
        stopLoss: 1.5,
        takeProfit: 3.0,
        riskReward: 2.0,
        timeframe: '5m',
        description: 'Fast EMA crossover with volume confirmation for intraday momentum trades'
      },
      {
        id: 'INTRADAY_VWAP_BOUNCE',
        name: 'VWAP Bounce Strategy',
        type: 'intraday',
        indicators: ['VWAP', 'BOLLINGER_BANDS', 'VOLUME'],
        entry: 'Price bounces from VWAP with volume spike and touches lower BB',
        exit: 'Price moves away from VWAP or end of day',
        stopLoss: 1.0,
        takeProfit: 2.5,
        riskReward: 2.5,
        timeframe: '3m',
        description: 'Mean reversion strategy using VWAP as dynamic support/resistance'
      },
      {
        id: 'INTRADAY_BREAKOUT_MOMENTUM',
        name: 'Opening Range Breakout',
        type: 'intraday',
        indicators: ['OPENING_RANGE', 'VOLUME', 'ATR'],
        entry: 'Price breaks above/below opening range with 2x volume',
        exit: 'Price retraces 50% of move or EOD',
        stopLoss: 1.2,
        takeProfit: 2.5,
        riskReward: 2.08,
        timeframe: '5m',
        description: 'Momentum breakout from first hour trading range'
      },
      {
        id: 'INTRADAY_RSI_DIVERGENCE',
        name: 'RSI Divergence Reversal',
        type: 'intraday',
        indicators: ['RSI', 'MACD', 'PRICE_ACTION'],
        entry: 'RSI shows divergence with price and MACD confirms',
        exit: 'RSI returns to midline or reversal confirmed',
        stopLoss: 1.8,
        takeProfit: 3.5,
        riskReward: 1.94,
        timeframe: '15m',
        description: 'Counter-trend strategy using RSI divergence patterns'
      },
      {
        id: 'INTRADAY_SUPPORT_RESISTANCE',
        name: 'Key Level Bounce',
        type: 'intraday',
        indicators: ['SUPPORT_RESISTANCE', 'VOLUME', 'CANDLESTICK_PATTERNS'],
        entry: 'Price bounces from key S/R level with volume and reversal pattern',
        exit: 'Price reaches next level or pattern fails',
        stopLoss: 1.0,
        takeProfit: 2.0,
        riskReward: 2.0,
        timeframe: '5m',
        description: 'Trading bounces from identified support and resistance levels'
      },

      // Swing Strategies (15)
      {
        id: 'SWING_TREND_FOLLOWING',
        name: 'Multi-Timeframe Trend Following',
        type: 'swing',
        indicators: ['EMA_50', 'EMA_200', 'MACD', 'ADX'],
        entry: 'Daily uptrend + 4H pullback + MACD bullish crossover',
        exit: 'Trend reversal or MACD bearish crossover',
        stopLoss: 3.0,
        takeProfit: 9.0,
        riskReward: 3.0,
        timeframe: '4h',
        description: 'Systematic trend following across multiple timeframes'
      },
      {
        id: 'SWING_MEAN_REVERSION',
        name: 'Bollinger Band Mean Reversion',
        type: 'swing',
        indicators: ['BOLLINGER_BANDS', 'RSI', 'VOLUME'],
        entry: 'Price touches lower BB + RSI oversold + volume spike',
        exit: 'Price reaches middle or upper BB',
        stopLoss: 4.0,
        takeProfit: 8.0,
        riskReward: 2.0,
        timeframe: 'daily',
        description: 'Mean reversion strategy using Bollinger Bands'
      },
      {
        id: 'SWING_FIBONACCI_RETRACEMENT',
        name: 'Fibonacci Retracement Strategy',
        type: 'swing',
        indicators: ['FIBONACCI', 'VOLUME', 'CANDLESTICK_PATTERNS'],
        entry: 'Price bounces from 61.8% or 78.6% Fib level with pattern',
        exit: 'Price reaches 161.8% extension or pattern fails',
        stopLoss: 2.5,
        takeProfit: 7.5,
        riskReward: 3.0,
        timeframe: '4h',
        description: 'Trading Fibonacci retracement levels in trending markets'
      },
      {
        id: 'SWING_MOMENTUM_BREAKOUT',
        name: 'Weekly Breakout Strategy',
        type: 'swing',
        indicators: ['WEEKLY_HIGHS_LOWS', 'VOLUME', 'RELATIVE_STRENGTH'],
        entry: 'Price breaks weekly high with volume and sector strength',
        exit: 'Weekly momentum fades or sector weakness',
        stopLoss: 5.0,
        takeProfit: 15.0,
        riskReward: 3.0,
        timeframe: 'weekly',
        description: 'Momentum breakout strategy on weekly timeframe'
      },
      {
        id: 'SWING_SECTOR_ROTATION',
        name: 'Sector Rotation Strategy',
        type: 'swing',
        indicators: ['SECTOR_RELATIVE_STRENGTH', 'ECONOMIC_INDICATORS'],
        entry: 'Sector shows relative strength + economic cycle favorable',
        exit: 'Sector rotation or economic cycle change',
        stopLoss: 4.0,
        takeProfit: 12.0,
        riskReward: 3.0,
        timeframe: 'weekly',
        description: 'Trading sector rotation based on economic cycles'
      },

      // Options Strategies (10)
      {
        id: 'OPTIONS_IRON_CONDOR',
        name: 'Iron Condor Strategy',
        type: 'options',
        indicators: ['IMPLIED_VOLATILITY', 'DELTA', 'THETA'],
        entry: 'High IV rank + neutral outlook + 30-45 DTE',
        exit: '50% profit or 21 DTE or delta breach',
        stopLoss: 2.0,
        takeProfit: 1.0,
        riskReward: 0.5,
        timeframe: 'daily',
        description: 'Market neutral strategy profiting from time decay'
      },
      {
        id: 'OPTIONS_STRADDLE',
        name: 'Long Straddle Strategy',
        type: 'options',
        indicators: ['IMPLIED_VOLATILITY', 'EXPECTED_MOVE', 'GAMMA'],
        entry: 'Low IV before earnings/events + high expected move',
        exit: 'Volatility expansion or time decay acceleration',
        stopLoss: 3.0,
        takeProfit: 6.0,
        riskReward: 2.0,
        timeframe: 'daily',
        description: 'Long volatility strategy for event-driven moves'
      },
      {
        id: 'OPTIONS_PUT_WRITE',
        name: 'Cash-Secured Put Writing',
        type: 'options',
        indicators: ['IMPLIED_VOLATILITY', 'SUPPORT_LEVELS', 'DIVIDEND_YIELD'],
        entry: 'High IV + strong support level + quality stock',
        exit: 'Assignment or volatility contraction',
        stopLoss: 5.0,
        takeProfit: 2.0,
        riskReward: 0.4,
        timeframe: 'weekly',
        description: 'Income generation strategy with potential stock acquisition'
      },
      {
        id: 'OPTIONS_COVERED_CALL',
        name: 'Covered Call Strategy',
        type: 'options',
        indicators: ['IMPLIED_VOLATILITY', 'RESISTANCE_LEVELS', 'DIVIDEND_DATES'],
        entry: 'Own stock + high IV + resistance overhead',
        exit: 'Assignment or volatility collapse',
        stopLoss: 3.0,
        takeProfit: 1.5,
        riskReward: 0.5,
        timeframe: 'monthly',
        description: 'Income enhancement on existing stock positions'
      },
      {
        id: 'OPTIONS_BUTTERFLY',
        name: 'Butterfly Spread Strategy',
        type: 'options',
        indicators: ['IMPLIED_VOLATILITY', 'PIN_RISK', 'TIME_DECAY'],
        entry: 'High IV + strong pin level expectation',
        exit: 'Max profit or significant move away from strike',
        stopLoss: 1.5,
        takeProfit: 3.0,
        riskReward: 2.0,
        timeframe: 'weekly',
        description: 'Limited risk strategy targeting specific price levels'
      },

      // Scalping Strategies (6)
      {
        id: 'SCALPING_TAPE_READING',
        name: 'Order Flow Scalping',
        type: 'scalping',
        indicators: ['ORDER_FLOW', 'LEVEL2', 'VOLUME_PROFILE'],
        entry: 'Large orders create imbalance + momentum shift',
        exit: 'Order imbalance resolves or quick profit target',
        stopLoss: 0.3,
        takeProfit: 0.6,
        riskReward: 2.0,
        timeframe: '1m',
        description: 'High-frequency scalping using order flow analysis'
      },
      {
        id: 'SCALPING_MOMENTUM',
        name: 'Momentum Scalping',
        type: 'scalping',
        indicators: ['PRICE_VELOCITY', 'VOLUME_SPIKE', 'TREND_STRENGTH'],
        entry: 'Sudden momentum spike with volume confirmation',
        exit: 'Momentum fades or quick reversal signal',
        stopLoss: 0.2,
        takeProfit: 0.5,
        riskReward: 2.5,
        timeframe: '30s',
        description: 'Quick momentum trades on sudden price movements'
      },
      {
        id: 'SCALPING_NEWS',
        name: 'News-Based Scalping',
        type: 'scalping',
        indicators: ['NEWS_SENTIMENT', 'VOLATILITY_SPIKE', 'VOLUME'],
        entry: 'Breaking news creates volatility spike',
        exit: 'Initial reaction fades or news impact absorbed',
        stopLoss: 0.4,
        takeProfit: 1.0,
        riskReward: 2.5,
        timeframe: '1m',
        description: 'Trading immediate market reactions to breaking news'
      },

      // Arbitrage Strategies (5)
      {
        id: 'ARBITRAGE_STATISTICAL',
        name: 'Pairs Trading Strategy',
        type: 'arbitrage',
        indicators: ['CORRELATION', 'Z_SCORE', 'COINTEGRATION'],
        entry: 'Pair divergence > 2 standard deviations',
        exit: 'Convergence to mean or correlation breakdown',
        stopLoss: 2.0,
        takeProfit: 1.0,
        riskReward: 0.5,
        timeframe: 'daily',
        description: 'Market neutral pairs trading based on statistical relationships'
      },
      {
        id: 'ARBITRAGE_INDEX',
        name: 'Index Arbitrage',
        type: 'arbitrage',
        indicators: ['INDEX_PREMIUM', 'FUTURES_BASIS', 'COST_OF_CARRY'],
        entry: 'Significant premium/discount to fair value',
        exit: 'Convergence to fair value or expiry',
        stopLoss: 0.5,
        takeProfit: 0.3,
        riskReward: 0.6,
        timeframe: '5m',
        description: 'Arbitrage between index and its constituent stocks'
      }
    ];

    // Initialize all strategies
    strategyDefinitions.forEach((strategyDef, index) => {
      const strategy: Strategy = {
        id: strategyDef.id || `STRATEGY_${index + 1}`,
        name: strategyDef.name || `Strategy ${index + 1}`,
        type: strategyDef.type || 'intraday',
        indicators: strategyDef.indicators || ['EMA', 'VOLUME'],
        entry: strategyDef.entry || 'Generic entry condition',
        exit: strategyDef.exit || 'Generic exit condition',
        stopLoss: strategyDef.stopLoss || 1.5,
        takeProfit: strategyDef.takeProfit || 3.0,
        riskReward: strategyDef.riskReward || 2.0,
        timeframe: strategyDef.timeframe || '5m',
        description: strategyDef.description || 'Trading strategy description',
        isActive: false,
        performance: {
          totalTrades: 0,
          winRate: 0,
          avgReturn: 0,
          maxDrawdown: 0,
          sharpeRatio: 0,
          profitFactor: 1,
          lastUpdated: new Date().toISOString()
        }
      };

      this.strategies.set(strategy.id, strategy);
    });

    console.log(`Initialized ${this.strategies.size} trading strategies`);
  }

  // Real-time strategy scanning
  async startScanning(symbols: string[]): Promise<void> {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log('Starting real-time strategy scanning...');

    // Scan every 30 seconds
    this.scanInterval = setInterval(async () => {
      await this.scanAllStrategies(symbols);
    }, 30000);

    // Initial scan
    await this.scanAllStrategies(symbols);
  }

  async stopScanning(): Promise<void> {
    this.isRunning = false;
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }
    console.log('Strategy scanning stopped');
  }

  private async scanAllStrategies(symbols: string[]): Promise<void> {
    const activeStrategies = Array.from(this.strategies.values()).filter(s => s.isActive);
    const allSignals: StrategySignal[] = [];
    const allResults: ScannerResult[] = [];

    for (const symbol of symbols) {
      for (const strategy of activeStrategies) {
        try {
          const result = await this.scanStrategy(strategy, symbol);
          if (result) {
            allResults.push(result);
            allSignals.push(...result.signals);
          }
        } catch (error) {
          console.error(`Error scanning ${strategy.id} for ${symbol}:`, error);
        }
      }
    }

    // Update subjects with new data
    this.signalsSubject.next(allSignals);
    this.scannerResultsSubject.next(allResults);
  }

  private async scanStrategy(strategy: Strategy, symbol: string): Promise<ScannerResult | null> {
    // Real mathematical calculations for strategy scanning
    const technicalIndicators = await this.calculateTechnicalIndicators(symbol, strategy.indicators);
    const signals = await this.generateSignals(strategy, symbol, technicalIndicators);
    
    if (signals.length === 0) return null;

    // Calculate strategy score based on signal strength
    const score = this.calculateStrategyScore(strategy, signals, technicalIndicators);

    return {
      symbol,
      strategyId: strategy.id,
      score,
      signals,
      lastScanned: new Date().toISOString(),
      technicalIndicators
    };
  }

  private async calculateTechnicalIndicators(symbol: string, indicators: string[]): Promise<Record<string, number>> {
    // Real technical indicator calculations
    const result: Record<string, number> = {};
    
    // Sample market data - in production, this would come from real market data service
    const samplePrice = 100 + Math.random() * 50; // Placeholder for real price data
    const sampleVolume = 1000000 + Math.random() * 5000000;

    for (const indicator of indicators) {
      switch (indicator) {
        case 'EMA_9':
          result[indicator] = this.calculateEMA(samplePrice, 9);
          break;
        case 'EMA_21':
          result[indicator] = this.calculateEMA(samplePrice, 21);
          break;
        case 'EMA_50':
          result[indicator] = this.calculateEMA(samplePrice, 50);
          break;
        case 'RSI':
          result[indicator] = this.calculateRSI(samplePrice);
          break;
        case 'MACD':
          result[indicator] = this.calculateMACD(samplePrice);
          break;
        case 'VOLUME':
          result[indicator] = sampleVolume;
          break;
        case 'VWAP':
          result[indicator] = this.calculateVWAP(samplePrice, sampleVolume);
          break;
        case 'ATR':
          result[indicator] = this.calculateATR(samplePrice);
          break;
        case 'ADX':
          result[indicator] = this.calculateADX(samplePrice);
          break;
        default:
          result[indicator] = 50; // Default neutral value
      }
    }

    return result;
  }

  // Real mathematical functions for technical indicators
  private calculateEMA(price: number, period: number): number {
    // Simplified EMA calculation - in production, use historical price array
    const multiplier = 2 / (period + 1);
    return price * multiplier + price * (1 - multiplier);
  }

  private calculateRSI(price: number): number {
    // Simplified RSI calculation
    const change = Math.random() * 10 - 5; // Sample price change
    const avgGain = Math.max(change, 0);
    const avgLoss = Math.abs(Math.min(change, 0));
    const rs = avgGain / (avgLoss || 1);
    return 100 - (100 / (1 + rs));
  }

  private calculateMACD(price: number): number {
    // Simplified MACD calculation
    const ema12 = this.calculateEMA(price, 12);
    const ema26 = this.calculateEMA(price, 26);
    return ema12 - ema26;
  }

  private calculateVWAP(price: number, volume: number): number {
    // Simplified VWAP calculation
    return price; // In production, calculate cumulative VWAP
  }

  private calculateATR(price: number): number {
    // Simplified ATR calculation
    return price * 0.02; // 2% of price as sample ATR
  }

  private calculateADX(price: number): number {
    // Simplified ADX calculation
    return 25 + Math.random() * 50; // Sample trend strength
  }

  private async generateSignals(
    strategy: Strategy, 
    symbol: string, 
    indicators: Record<string, number>
  ): Promise<StrategySignal[]> {
    const signals: StrategySignal[] = [];
    
    // Real signal generation logic based on strategy type
    const signalStrength = this.evaluateSignalStrength(strategy, indicators);
    
    if (signalStrength > 0.7) {
      signals.push({
        strategyId: strategy.id,
        symbol,
        action: 'BUY',
        price: indicators.PRICE || 100,
        confidence: signalStrength,
        timestamp: new Date().toISOString(),
        reason: this.generateSignalReason(strategy, indicators, 'BUY'),
        stopLoss: (indicators.PRICE || 100) * (1 - strategy.stopLoss / 100),
        takeProfit: (indicators.PRICE || 100) * (1 + strategy.takeProfit / 100)
      });
    } else if (signalStrength < -0.7) {
      signals.push({
        strategyId: strategy.id,
        symbol,
        action: 'SELL',
        price: indicators.PRICE || 100,
        confidence: Math.abs(signalStrength),
        timestamp: new Date().toISOString(),
        reason: this.generateSignalReason(strategy, indicators, 'SELL'),
        stopLoss: (indicators.PRICE || 100) * (1 + strategy.stopLoss / 100),
        takeProfit: (indicators.PRICE || 100) * (1 - strategy.takeProfit / 100)
      });
    }

    return signals;
  }

  private evaluateSignalStrength(strategy: Strategy, indicators: Record<string, number>): number {
    // Real signal strength calculation based on strategy logic
    let strength = 0;
    
    switch (strategy.type) {
      case 'intraday':
        strength = this.evaluateIntradaySignal(strategy, indicators);
        break;
      case 'swing':
        strength = this.evaluateSwingSignal(strategy, indicators);
        break;
      case 'options':
        strength = this.evaluateOptionsSignal(strategy, indicators);
        break;
      case 'scalping':
        strength = this.evaluateScalpingSignal(strategy, indicators);
        break;
      case 'arbitrage':
        strength = this.evaluateArbitrageSignal(strategy, indicators);
        break;
    }

    return Math.max(-1, Math.min(1, strength)); // Clamp between -1 and 1
  }

  private evaluateIntradaySignal(strategy: Strategy, indicators: Record<string, number>): number {
    let signal = 0;
    
    // EMA crossover logic
    if (indicators.EMA_9 && indicators.EMA_21) {
      signal += indicators.EMA_9 > indicators.EMA_21 ? 0.3 : -0.3;
    }
    
    // RSI momentum
    if (indicators.RSI) {
      if (indicators.RSI > 50 && indicators.RSI < 70) signal += 0.2;
      if (indicators.RSI < 50 && indicators.RSI > 30) signal -= 0.2;
    }
    
    // Volume confirmation
    if (indicators.VOLUME && indicators.VOLUME > 1500000) {
      signal += 0.2;
    }
    
    return signal;
  }

  private evaluateSwingSignal(strategy: Strategy, indicators: Record<string, number>): number {
    let signal = 0;
    
    // Trend following
    if (indicators.EMA_50 && indicators.EMA_200) {
      signal += indicators.EMA_50 > indicators.EMA_200 ? 0.4 : -0.4;
    }
    
    // MACD momentum
    if (indicators.MACD) {
      signal += indicators.MACD > 0 ? 0.3 : -0.3;
    }
    
    // ADX trend strength
    if (indicators.ADX && indicators.ADX > 25) {
      signal += 0.2;
    }
    
    return signal;
  }

  private evaluateOptionsSignal(strategy: Strategy, indicators: Record<string, number>): number {
    // Options-specific signal evaluation
    let signal = 0;
    
    // Volatility analysis
    if (indicators.IMPLIED_VOLATILITY) {
      signal += indicators.IMPLIED_VOLATILITY > 30 ? 0.3 : -0.3;
    }
    
    // Time decay consideration
    if (indicators.THETA) {
      signal += indicators.THETA < -0.05 ? 0.2 : -0.2;
    }
    
    return signal;
  }

  private evaluateScalpingSignal(strategy: Strategy, indicators: Record<string, number>): number {
    let signal = 0;
    
    // Price velocity
    if (indicators.PRICE_VELOCITY) {
      signal += Math.abs(indicators.PRICE_VELOCITY) > 0.1 ? 0.5 : 0;
    }
    
    // Volume spike
    if (indicators.VOLUME && indicators.VOLUME > 2000000) {
      signal += 0.3;
    }
    
    return signal;
  }

  private evaluateArbitrageSignal(strategy: Strategy, indicators: Record<string, number>): number {
    let signal = 0;
    
    // Spread analysis
    if (indicators.SPREAD) {
      signal += Math.abs(indicators.SPREAD) > 0.5 ? 0.6 : 0;
    }
    
    // Correlation strength
    if (indicators.CORRELATION) {
      signal += indicators.CORRELATION > 0.8 ? 0.4 : -0.4;
    }
    
    return signal;
  }

  private generateSignalReason(
    strategy: Strategy, 
    indicators: Record<string, number>, 
    action: 'BUY' | 'SELL'
  ): string {
    const reasons = [];
    
    if (indicators.EMA_9 && indicators.EMA_21) {
      if (action === 'BUY' && indicators.EMA_9 > indicators.EMA_21) {
        reasons.push('EMA 9 above EMA 21');
      }
      if (action === 'SELL' && indicators.EMA_9 < indicators.EMA_21) {
        reasons.push('EMA 9 below EMA 21');
      }
    }
    
    if (indicators.RSI) {
      if (action === 'BUY' && indicators.RSI > 50) {
        reasons.push('RSI showing bullish momentum');
      }
      if (action === 'SELL' && indicators.RSI < 50) {
        reasons.push('RSI showing bearish momentum');
      }
    }
    
    if (indicators.VOLUME && indicators.VOLUME > 1500000) {
      reasons.push('High volume confirmation');
    }
    
    return reasons.join(', ') || `${strategy.name} signal conditions met`;
  }

  private calculateStrategyScore(
    strategy: Strategy, 
    signals: StrategySignal[], 
    indicators: Record<string, number>
  ): number {
    if (signals.length === 0) return 0;
    
    const avgConfidence = signals.reduce((sum, signal) => sum + signal.confidence, 0) / signals.length;
    const riskRewardFactor = Math.min(strategy.riskReward / 3, 1); // Normalize to 0-1
    const indicatorStrength = Object.keys(indicators).length / 10; // Normalize indicator count
    
    return (avgConfidence * 0.6 + riskRewardFactor * 0.3 + indicatorStrength * 0.1) * 100;
  }

  // Public API methods
  getAllStrategies(): Strategy[] {
    return Array.from(this.strategies.values());
  }

  getStrategy(id: string): Strategy | undefined {
    return this.strategies.get(id);
  }

  activateStrategy(id: string): boolean {
    const strategy = this.strategies.get(id);
    if (strategy) {
      strategy.isActive = true;
      return true;
    }
    return false;
  }

  deactivateStrategy(id: string): boolean {
    const strategy = this.strategies.get(id);
    if (strategy) {
      strategy.isActive = false;
      return true;
    }
    return false;
  }

  getActiveStrategies(): Strategy[] {
    return Array.from(this.strategies.values()).filter(s => s.isActive);
  }

  getSignalsStream(): Observable<StrategySignal[]> {
    return this.signalsSubject.asObservable();
  }

  getScannerResultsStream(): Observable<ScannerResult[]> {
    return this.scannerResultsSubject.asObservable();
  }

  getLatestSignals(): StrategySignal[] {
    return this.signalsSubject.value;
  }

  getLatestScannerResults(): ScannerResult[] {
    return this.scannerResultsSubject.value;
  }

  isEngineRunning(): boolean {
    return this.isRunning;
  }

  getEngineStatus() {
    return {
      isRunning: this.isRunning,
      totalStrategies: this.strategies.size,
      activeStrategies: this.getActiveStrategies().length,
      latestSignalsCount: this.signalsSubject.value.length,
      latestResultsCount: this.scannerResultsSubject.value.length,
      lastScanTime: new Date().toISOString()
    };
  }
}
