
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Bar<PERSON>hart3, 
  Pie<PERSON>hart, 
  TrendingUp, 
  Activity, 
  Target,
  Brain,
  Zap,
  Eye,
  Users
} from "lucide-react";
import { Line<PERSON>hart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart as Re<PERSON>rts<PERSON>ie<PERSON>hart, Cell, Pie } from 'recharts';

interface ToolData {
  name: string;
  description: string;
  status: string;
  accuracy: string;
}

interface AdvancedAnalyticsToolsProps {
  advancedToolsCount?: number;
  avgAccuracy?: number;
  aiModels?: number;
  dailySignals?: number;
  performanceData?: Array<{ name: string; accuracy: number; usage: number }>;
  distributionData?: Array<{ name: string; value: number; color: string }>;
  institutionalTools?: ToolData[];
  sectorAnalysisTools?: ToolData[];
  aiPoweredTools?: ToolData[];
  marketStructureTools?: ToolData[];
}

export const AdvancedAnalyticsTools = ({
  advancedToolsCount = 0,
  avgAccuracy = 0,
  aiModels = 0,
  dailySignals = 0,
  performanceData = [],
  distributionData = [],
  institutionalTools = [],
  sectorAnalysisTools = [],
  aiPoweredTools = [],
  marketStructureTools = []
}: AdvancedAnalyticsToolsProps) => {
  const [activeCategory, setActiveCategory] = useState("institutional");

  const renderToolCard = (tool: ToolData, icon: React.ElementType) => {
    const IconComponent = icon;
    return (
      <Card key={tool.name} className="bg-trading-darker border-trading-border hover:border-trading-accent transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <IconComponent className="h-6 w-6 text-blue-400" />
            <Badge variant="outline" className="text-green-400 border-green-400">
              {tool.accuracy}
            </Badge>
          </div>
          <h3 className="text-trading-light font-medium mb-2">{tool.name}</h3>
          <p className="text-trading-muted text-sm mb-3">{tool.description}</p>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" className="text-xs flex-1">
              <Eye className="h-3 w-3 mr-1" />
              Analyze
            </Button>
            <Button size="sm" variant="outline" className="text-xs flex-1">
              <Brain className="h-3 w-3 mr-1" />
              AI View
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tool Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-blue-400" />
            <div className="text-2xl font-bold text-blue-400">{advancedToolsCount}</div>
            <div className="text-sm text-trading-muted">Advanced Tools</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 mx-auto mb-2 text-green-400" />
            <div className="text-2xl font-bold text-green-400">{avgAccuracy}%</div>
            <div className="text-sm text-trading-muted">Avg Accuracy</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <Brain className="h-8 w-8 mx-auto mb-2 text-purple-400" />
            <div className="text-2xl font-bold text-purple-400">{aiModels}</div>
            <div className="text-sm text-trading-muted">AI Models</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-400" />
            <div className="text-2xl font-bold text-yellow-400">{dailySignals}</div>
            <div className="text-sm text-trading-muted">Daily Signals</div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Tool Performance Matrix</CardTitle>
          </CardHeader>
          <CardContent>
            {performanceData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={performanceData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="name" stroke="#9ca3af" />
                  <YAxis stroke="#9ca3af" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }}
                  />
                  <Bar dataKey="accuracy" fill="#3b82f6" />
                  <Bar dataKey="usage" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-trading-muted">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No performance data available</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Tool Category Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            {distributionData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={distributionData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    dataKey="value"
                  >
                    {distributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-trading-muted">
                <div className="text-center">
                  <PieChart className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No distribution data available</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeCategory} onValueChange={setActiveCategory} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="institutional" className="data-[state=active]:bg-trading-accent">
            <Users className="h-4 w-4 mr-2" />
            Institutional
          </TabsTrigger>
          <TabsTrigger value="sector" className="data-[state=active]:bg-trading-accent">
            <PieChart className="h-4 w-4 mr-2" />
            Sector Analysis
          </TabsTrigger>
          <TabsTrigger value="ai" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            AI Powered
          </TabsTrigger>
          <TabsTrigger value="structure" className="data-[state=active]:bg-trading-accent">
            <Activity className="h-4 w-4 mr-2" />
            Market Structure
          </TabsTrigger>
        </TabsList>

        <TabsContent value="institutional">
          {institutionalTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {institutionalTools.map((tool) => renderToolCard(tool, Users))}
            </div>
          ) : (
            <Card className="bg-trading-darker border-trading-border">
              <CardContent className="p-8 text-center text-trading-muted">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No institutional tools data available</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="sector">
          {sectorAnalysisTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sectorAnalysisTools.map((tool) => renderToolCard(tool, PieChart))}
            </div>
          ) : (
            <Card className="bg-trading-darker border-trading-border">
              <CardContent className="p-8 text-center text-trading-muted">
                <PieChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No sector analysis tools data available</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="ai">
          {aiPoweredTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {aiPoweredTools.map((tool) => renderToolCard(tool, Brain))}
            </div>
          ) : (
            <Card className="bg-trading-darker border-trading-border">
              <CardContent className="p-8 text-center text-trading-muted">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No AI powered tools data available</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="structure">
          {marketStructureTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {marketStructureTools.map((tool) => renderToolCard(tool, Activity))}
            </div>
          ) : (
            <Card className="bg-trading-darker border-trading-border">
              <CardContent className="p-8 text-center text-trading-muted">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No market structure tools data available</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
