
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Play, Pause, Settings } from "lucide-react";

interface Strategy {
  id: number;
  name: string;
  type: string;
  status: string;
  pnl: number;
  trades: number;
  winRate: number;
  drawdown: number;
  allocation: string;
}

interface StrategyOverview {
  activeStrategies: number;
  totalPnl: number;
  totalTrades: number;
  avgWinRate: number;
  maxDrawdown: number;
}

interface StrategyDashboardProps {
  strategies?: Strategy[];
  overview?: StrategyOverview;
  isLoading?: boolean;
}

export const StrategyDashboard = ({
  strategies = [],
  overview,
  isLoading = false
}: StrategyDashboardProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "text-green-400 border-green-400";
      case "paused": return "text-yellow-400 border-yellow-400";
      case "stopped": return "text-red-400 border-red-400";
      default: return "text-gray-400 border-gray-400";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Scalping": return "text-purple-400 border-purple-400";
      case "Intraday": return "text-blue-400 border-blue-400";
      case "Swing": return "text-orange-400 border-orange-400";
      case "Options": return "text-pink-400 border-pink-400";
      case "Investment": return "text-green-400 border-green-400";
      default: return "text-gray-400 border-gray-400";
    }
  };

  return (
    <div className="space-y-6">
      {/* Strategy Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Strategy Performance Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {overview ? overview.activeStrategies : '--'}
              </div>
              <div className="text-sm text-trading-muted">Active Strategies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {overview ? `₹${overview.totalPnl.toLocaleString()}` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Total P&L</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {overview ? overview.totalTrades : '--'}
              </div>
              <div className="text-sm text-trading-muted">Total Trades</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {overview ? `${overview.avgWinRate}%` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Avg Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-400">
                {overview ? `${overview.maxDrawdown}%` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Max Drawdown</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy Grid */}
      {strategies.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {strategies.map((strategy) => (
            <Card key={strategy.id} className="bg-trading-darker border-trading-border">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm text-trading-light">{strategy.name}</CardTitle>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      {strategy.status === "active" ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                    </Button>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Badge variant="outline" className={`text-xs ${getTypeColor(strategy.type)}`}>
                    {strategy.type}
                  </Badge>
                  <Badge variant="outline" className={`text-xs ${getStatusColor(strategy.status)}`}>
                    {strategy.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-xs text-trading-muted">P&L</span>
                  <span className={`text-xs font-medium ${strategy.pnl >= 0 ? "text-green-400" : "text-red-400"}`}>
                    {strategy.pnl >= 0 ? "+" : ""}₹{strategy.pnl.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-trading-muted">Trades</span>
                  <span className="text-xs text-trading-light">{strategy.trades}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-trading-muted">Win Rate</span>
                  <span className="text-xs text-blue-400">{strategy.winRate}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-trading-muted">Drawdown</span>
                  <span className="text-xs text-red-400">{strategy.drawdown}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-xs text-trading-muted">Allocation</span>
                  <span className="text-xs text-trading-light">{strategy.allocation}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="py-12">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              <div className="text-lg text-trading-muted mb-2">
                {isLoading ? 'Loading strategies...' : 'No active strategies'}
              </div>
              <div className="text-sm text-trading-muted mb-4">
                {isLoading ? 'Fetching strategy data...' : 'Create and deploy your first trading strategy'}
              </div>
              {!isLoading && (
                <Button variant="outline">
                  Create Strategy
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
