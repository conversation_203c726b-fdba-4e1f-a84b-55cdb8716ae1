
/* Advanced Trading Animations */

/* Price change animations */
@keyframes priceFlash {
  0% { background-color: transparent; }
  50% { background-color: rgba(16, 185, 129, 0.3); }
  100% { background-color: transparent; }
}

@keyframes priceFlashRed {
  0% { background-color: transparent; }
  50% { background-color: rgba(239, 68, 68, 0.3); }
  100% { background-color: transparent; }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation classes */
.price-flash-green {
  animation: priceFlash 0.5s ease-in-out;
}

.price-flash-red {
  animation: priceFlashRed 0.5s ease-in-out;
}

.slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

.pulse-glow {
  animation: pulseGlow 2s infinite;
}

.shimmer-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Micro-interactions */
.micro-scale:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.micro-lift:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

.micro-glow:hover {
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.4);
  transition: box-shadow 0.3s ease;
}

/* Chart animations */
.chart-enter {
  animation: slideInFromRight 0.5s ease-out;
}

.chart-update {
  transition: all 0.3s ease;
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
