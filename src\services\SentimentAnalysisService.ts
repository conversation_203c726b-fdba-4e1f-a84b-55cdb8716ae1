export interface NewsItem {
  id: string;
  title: string;
  content: string;
  source: string;
  timestamp: Date;
  sentiment: number; // -1 to 1
  relevance: number; // 0 to 1
  symbols: string[];
}

export interface SentimentData {
  symbol: string;
  overallSentiment: number;
  newsCount: number;
  positiveCount: number;
  negativeCount: number;
  neutralCount: number;
  trendingTopics: string[];
  timestamp: Date;
}

export class SentimentAnalysisService {
  private sentimentCache: Map<string, SentimentData> = new Map();
  private newsItems: NewsItem[] = [];

  async analyzeSentiment(symbol: string): Promise<SentimentData> {
    // Get recent news for the symbol
    const relevantNews = this.getRelevantNews(symbol);
    
    if (relevantNews.length === 0) {
      return this.getDefaultSentiment(symbol);
    }

    const sentiments = relevantNews.map(news => news.sentiment);
    const overallSentiment = sentiments.reduce((sum, s) => sum + s, 0) / sentiments.length;
    
    const positiveCount = sentiments.filter(s => s > 0.1).length;
    const negativeCount = sentiments.filter(s => s < -0.1).length;
    const neutralCount = sentiments.length - positiveCount - negativeCount;

    const trendingTopics = this.extractTrendingTopics(relevantNews);

    const sentimentData: SentimentData = {
      symbol,
      overallSentiment,
      newsCount: relevantNews.length,
      positiveCount,
      negativeCount,
      neutralCount,
      trendingTopics,
      timestamp: new Date()
    };

    this.sentimentCache.set(symbol, sentimentData);
    return sentimentData;
  }

  private getRelevantNews(symbol: string): NewsItem[] {
    return this.newsItems.filter(news => 
      news.symbols.includes(symbol) && 
      news.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
    );
  }

  private getDefaultSentiment(symbol: string): SentimentData {
    return {
      symbol,
      overallSentiment: 0,
      newsCount: 0,
      positiveCount: 0,
      negativeCount: 0,
      neutralCount: 0,
      trendingTopics: [],
      timestamp: new Date()
    };
  }

  private extractTrendingTopics(newsItems: NewsItem[]): string[] {
    const topics = ['earnings', 'merger', 'acquisition', 'results', 'growth', 'expansion'];
    const topicCounts = new Map();

    newsItems.forEach(news => {
      const content = (news.title + ' ' + news.content).toLowerCase();
      topics.forEach(topic => {
        if (content.includes(topic)) {
          topicCounts.set(topic, (topicCounts.get(topic) || 0) + 1);
        }
      });
    });

    return Array.from(topicCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([topic]) => topic);
  }

  async processNews(newsItem: Omit<NewsItem, 'id' | 'sentiment' | 'relevance'>): Promise<NewsItem> {
    const sentiment = await this.calculateSentiment(newsItem.title + ' ' + newsItem.content);
    const relevance = await this.calculateRelevance(newsItem);

    const processedNews: NewsItem = {
      ...newsItem,
      id: `news_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sentiment,
      relevance
    };

    this.newsItems.push(processedNews);
    
    // Keep only last 1000 news items
    if (this.newsItems.length > 1000) {
      this.newsItems = this.newsItems.slice(-1000);
    }

    return processedNews;
  }

  private async calculateSentiment(text: string): Promise<number> {
    // Mock sentiment analysis - in production, use real NLP models
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'growth', 'profit', 'up', 'rise'];
    const negativeWords = ['bad', 'poor', 'negative', 'loss', 'down', 'fall', 'decline', 'drop'];
    
    const words = text.toLowerCase().split(/\s+/);
    let score = 0;
    
    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) score += 0.1;
      if (negativeWords.some(nw => word.includes(nw))) score -= 0.1;
    });
    
    return Math.max(-1, Math.min(1, score));
  }

  private async calculateRelevance(newsItem: any): Promise<number> {
    // Mock relevance calculation
    const sourceReliability = newsItem.source === 'Economic Times' ? 0.9 : 0.7;
    const symbolMentions = newsItem.symbols.length;
    return Math.min(1, sourceReliability * (0.5 + symbolMentions * 0.2));
  }

  getSentimentHistory(symbol: string, days: number = 7): SentimentData[] {
    // Mock historical sentiment data
    const history: SentimentData[] = [];
    for (let i = days; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      history.push({
        symbol,
        overallSentiment: (Math.random() - 0.5) * 2,
        newsCount: Math.floor(Math.random() * 20) + 5,
        positiveCount: Math.floor(Math.random() * 10),
        negativeCount: Math.floor(Math.random() * 8),
        neutralCount: Math.floor(Math.random() * 5),
        trendingTopics: ['earnings', 'growth'],
        timestamp: date
      });
    }
    return history;
  }

  getRecentNews(symbol: string, limit: number = 10): NewsItem[] {
    return this.newsItems
      .filter(news => news.symbols.includes(symbol))
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }
}
