
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MarketDataDashboard } from "../data/MarketDataDashboard";
import {
  <PERSON><PERSON>,
  Settings
} from "lucide-react";
import { ChartingDashboard } from "../charting/ChartingDashboard";
import { PerformanceAnalytics } from "../analytics/PerformanceAnalytics";
import { MLModelDashboard } from "../ml/MLModelDashboard";
import { StrategyMLDashboard } from "../ml/StrategyMLDashboard";
import { PaperTradingDashboard } from "../trading/PaperTradingDashboard";
import { AdvancedRiskDashboard } from "../risk/AdvancedRiskDashboard";
import { ScannerDashboard } from "../scanners/ScannerDashboard";
import { BacktestDashboard } from "../backtesting/BacktestDashboard";
import { AIAgentDashboard } from "../ai/AIAgentDashboard";
import { OrderFlowAnalytics } from "../analytics/OrderFlowAnalytics";
import { SmartMoneyTracker } from "../analytics/SmartMoneyTracker";
import { OptionsDashboard } from "../options/OptionsDashboard";
import { LiveTradeDashboard } from "../trading/LiveTradeDashboard";
import { AlertCenter } from "../alerts/AlertCenter";
import { APIDocumentation } from "../api/APIDocumentation";
import { MobileOptimizer } from "../mobile/MobileOptimizer";
import { LiveDataDashboard } from "../data/LiveDataDashboard";
import { SystemDashboard } from "../system/SystemDashboard";
import { StockAnalysis } from "../analysis/StockAnalysis";
import { MarketMovers } from "../data/MarketMovers";
import { AnalyticsToolsDashboard } from "../analytics/AnalyticsToolsDashboard";
import { VWAPAnalysis } from "../analytics/VWAPAnalysis";
import { VolumeProfile } from "../analytics/VolumeProfile";
import { DOMHeatmap } from "../analytics/DOMHeatmap";
import { SectorRotationTracker } from "../analytics/SectorRotationTracker";
import { StrategyPerformanceDashboard } from "../analytics/StrategyPerformanceDashboard";
import { StockAnalysis360 } from "../analytics/StockAnalysis360";
import { BreakoutRetestDetector } from "../analytics/BreakoutRetestDetector";
import { AccumulationTracker } from "../analytics/AccumulationTracker";
import { SentimentEngine } from "../analytics/SentimentEngine";
import { OptionChainAnalyzer } from "../analytics/OptionChainAnalyzer";
import { RiskRewardCalculator } from "../analytics/RiskRewardCalculator";
import { Watchlist } from "../watchlist/Watchlist";
import { ComprehensiveStrategyDashboard } from "../strategies/ComprehensiveStrategyDashboard";
import { RealDataConfiguration } from "../data/RealDataConfiguration";

interface MainContentProps {
  activeView: string;
  onViewChange: (view: string) => void;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

export const MainContent = ({ activeView, onViewChange, sidebarCollapsed, onToggleSidebar }: MainContentProps) => {
  const [selectedStock, setSelectedStock] = useState<string | null>(null);

  const handleStockSelect = (symbol: string) => {
    setSelectedStock(symbol);
    onViewChange("stock-analysis");
  };

  const renderContent = () => {
    if (selectedStock && activeView === "stock-analysis") {
      return <StockAnalysis symbol={selectedStock} onBack={() => setSelectedStock(null)} />;
    }

    switch (activeView) {
      case "dashboard":
        return <MarketDataDashboard onStockSelect={handleStockSelect} />;
      case "strategies":
        return <ComprehensiveStrategyDashboard />;
      case "real-data-config":
        return <RealDataConfiguration onServiceChange={() => {}} />;
      case "market-movers":
        return <MarketMovers onStockSelect={handleStockSelect} />;
      case "watchlist":
        return <Watchlist />;
      case "analytics-tools":
        return <AnalyticsToolsDashboard />;
      case "vwap-analysis":
        return <VWAPAnalysis />;
      case "volume-profile":
        return <VolumeProfile />;
      case "dom-heatmap":
        return <DOMHeatmap />;
      case "sector-rotation":
        return <SectorRotationTracker />;
      case "strategy-performance":
        return <StrategyPerformanceDashboard />;
      case "stock-analysis-360":
        return <StockAnalysis360 />;
      case "breakout-retest":
        return <BreakoutRetestDetector />;
      case "accumulation-tracker":
        return <AccumulationTracker />;
      case "sentiment-engine":
        return <SentimentEngine />;
      case "option-chain":
        return <OptionChainAnalyzer />;
      case "risk-reward":
        return <RiskRewardCalculator />;
      case "system":
        return <SystemDashboard />;
      case "live-data":
        return <LiveDataDashboard />;
      case "charting":
        return <ChartingDashboard />;
      case "scanners":
        return <ScannerDashboard />;
      case "backtesting":
        return <BacktestDashboard />;
      case "analytics":
        return <PerformanceAnalytics />;
      case "ml-models":
        return <MLModelDashboard />;
      case "strategy-ml":
        return <StrategyMLDashboard />;
      case "paper-trading":
        return <PaperTradingDashboard />;
      case "live-trading":
        return <LiveTradeDashboard />;
      case "risk":
        return <AdvancedRiskDashboard />;
      case "ai-agents":
        return <AIAgentDashboard />;
      case "order-flow":
        return <OrderFlowAnalytics />;
      case "smart-money":
        return <SmartMoneyTracker />;
      case "options":
        return <OptionsDashboard />;
      case "alerts":
        return <AlertCenter />;
      case "api-docs":
        return <APIDocumentation />;
      case "mobile":
        return <MobileOptimizer />;
      default:
        return <MarketDataDashboard onStockSelect={handleStockSelect} />;
    }
  };

  const getPageTitle = () => {
    const titles: { [key: string]: string } = {
      "dashboard": "Market Overview",
      "strategies": "Strategy Command Center - All 53 Strategies",
      "real-data-config": "Real Data Configuration",
      "market-movers": "Market Movers", 
      "watchlist": "Smart Watchlists",
      "analytics-tools": "Analytics Tools",
      "vwap-analysis": "VWAP Analysis",
      "volume-profile": "Volume Profile",
      "dom-heatmap": "DOM Heatmap",
      "sector-rotation": "Sector Rotation",
      "strategy-performance": "Strategy Performance",
      "stock-analysis-360": "360° Stock Analysis",
      "breakout-retest": "Breakout Retest Detector",
      "accumulation-tracker": "Accumulation Tracker",
      "sentiment-engine": "Sentiment Engine",
      "option-chain": "Option Chain Analyzer",
      "risk-reward": "Risk-Reward Calculator",
      "charting": "Advanced Charts",
      "order-flow": "Order Flow Analysis",
      "smart-money": "Smart Money Tracker",
      "analytics": "Performance Analytics",
      "paper-trading": "Paper Trading",
      "live-trading": "Live Trading",
      "options": "Options Trading",
      "scanners": "Stock Scanners",
      "backtesting": "Strategy Testing",
      "ai-agents": "AI Trading Agents",
      "ml-models": "ML Models",
      "strategy-ml": "Strategy Builder",
      "risk": "Risk Management",
      "alerts": "Alert Center",
      "system": "System Health",
      "live-data": "Live Data Feeds",
      "api-docs": "API Documentation",
      "mobile": "Mobile Sync",
      "stock-analysis": "Stock Analysis"
    };
    return titles[activeView] || "Trading Platform";
  };

  return (
    <div className="flex-1 overflow-hidden bg-gradient-to-br from-trading-dark via-trading-darker to-trading-dark">
      {/* Simplified Header */}
      <div className="border-b border-trading-border/50 bg-trading-darker/80 backdrop-blur-sm">
        <div className="p-3 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleSidebar}
                className="lg:hidden"
              >
                <Menu className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl lg:text-2xl font-bold text-trading-light">{getPageTitle()}</h1>
                <p className="text-xs lg:text-sm text-trading-muted">Advanced Analytics & Trading Suite</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="hidden lg:flex">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area with ScrollArea */}
      <ScrollArea className="h-[calc(100vh-8rem)] w-full">
        <div className="p-3 lg:p-6 space-y-4 lg:space-y-6 max-w-full">
          {renderContent()}
        </div>
      </ScrollArea>
    </div>
  );
};
