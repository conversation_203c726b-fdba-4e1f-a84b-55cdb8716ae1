
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Zap, Brain, Activity, Target, TrendingUp } from "lucide-react";

interface TradingSignal {
  symbol: string;
  action: 'BUY' | 'SELL';
  price: number;
  target: number;
  sl: number;
  confidence: number;
  model: string;
}

interface PerformanceMetric {
  metric: string;
  value: string;
  color: string;
}

interface ModelStatus {
  model: string;
  accuracy: number;
  status: string;
  trades: number;
}

interface IntradayAgentProps {
  liveSignals?: TradingSignal[];
  performance?: PerformanceMetric[];
  modelStatus?: ModelStatus[];
  isLiveTrading?: boolean;
  onExecuteSignal?: (signal: TradingSignal) => void;
}

export const IntradayAgent = ({
  liveSignals = [],
  performance = [],
  modelStatus = [],
  isLiveTrading = false,
  onExecuteSignal
}: IntradayAgentProps) => {
  const handleExecuteSignal = (signal: TradingSignal) => {
    if (onExecuteSignal) {
      onExecuteSignal(signal);
    }
  };

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Zap className="h-5 w-5 mr-2 text-yellow-400" />
            Intraday AI Agent
            {isLiveTrading && <Badge className="ml-2 bg-yellow-600">Live Trading</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {performance.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              {performance.map((metric, index) => (
                <div key={index} className="text-center">
                  <div className={`text-lg font-bold ${metric.color}`}>{metric.value}</div>
                  <div className="text-xs text-trading-muted">{metric.metric}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-lg font-bold text-trading-muted">--</div>
                <div className="text-xs text-trading-muted">Today's P&L</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-muted">--</div>
                <div className="text-xs text-trading-muted">Win Rate</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-muted">--</div>
                <div className="text-xs text-trading-muted">Active Trades</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-trading-muted">--</div>
                <div className="text-xs text-trading-muted">Avg Hold</div>
              </div>
            </div>
          )}
          
          <div className="text-sm text-trading-light mb-4">
            <div className="flex items-center mb-2">
              <Brain className="h-4 w-4 mr-2 text-purple-400" />
              <span>AI Models: LSTM Neural Networks + Reinforcement Learning (PPO/A2C)</span>
            </div>
            <div className="text-trading-muted">
              Data Feed: Tick-level OHLCV + Order Book + VWAP • Update Frequency: Real-time
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Live Trading Signals</CardTitle>
        </CardHeader>
        <CardContent>
          {liveSignals.length > 0 ? (
            <div className="space-y-3">
              {liveSignals.map((signal, index) => (
                <div key={index} className="p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div className="text-trading-light font-medium">{signal.symbol}</div>
                      <Badge variant="outline" className={signal.action === "BUY" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                        {signal.action}
                      </Badge>
                      <div className="text-sm text-trading-muted">@ ₹{signal.price}</div>
                      <div className="text-xs bg-purple-600 px-2 py-1 rounded">{signal.model}</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-blue-400">{signal.confidence}%</div>
                      <Button size="sm" onClick={() => handleExecuteSignal(signal)}>Execute</Button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="text-trading-muted">Target: <span className="text-green-400">₹{signal.target}</span></div>
                    <div className="text-trading-muted">SL: <span className="text-red-400">₹{signal.sl}</span></div>
                    <div className="text-trading-muted">R:R: <span className="text-blue-400">1:{((signal.target - signal.price) / (signal.price - signal.sl)).toFixed(1)}</span></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-trading-muted">No live signals available</p>
              <p className="text-sm text-trading-muted mt-1">Connect to data feed to see real-time signals</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">AI Model Performance</CardTitle>
        </CardHeader>
        <CardContent>
          {modelStatus.length > 0 ? (
            <div className="space-y-3">
              {modelStatus.map((model, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-4">
                    <div className="text-trading-light font-medium">{model.model}</div>
                    <Badge variant="outline" className={model.status === "Active" ? "text-green-400 border-green-400" : "text-yellow-400 border-yellow-400"}>
                      {model.status}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-sm text-trading-muted">{model.trades} trades today</div>
                    <div className="flex items-center space-x-2">
                      <Progress value={model.accuracy} className="w-16 h-2" />
                      <div className="text-sm text-trading-light">{model.accuracy}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-trading-muted">No model performance data available</p>
              <p className="text-sm text-trading-muted mt-1">Models will appear when connected to training data</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
