
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, Card<PERSON>itle } from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";

interface SectorData {
  name: string;
  change: number;
  volume: string;
  marketCap: string;
}

const sectorData: SectorData[] = [
  { name: "Banking", change: 2.45, volume: "15.2B", marketCap: "8.5T" },
  { name: "IT", change: -1.23, volume: "8.9B", marketCap: "12.3T" },
  { name: "Auto", change: 3.67, volume: "4.2B", marketCap: "3.8T" },
  { name: "<PERSON><PERSON>", change: 1.89, volume: "3.1B", marketCap: "5.2T" },
  { name: "FMCG", change: 0.45, volume: "2.8B", marketCap: "6.7T" },
  { name: "Energy", change: -2.15, volume: "6.5B", marketCap: "4.1T" },
  { name: "Metals", change: 4.23, volume: "7.8B", marketCap: "2.9T" },
  { name: "Telecom", change: -0.67, volume: "1.9B", marketCap: "3.4T" },
  { name: "Realty", change: 2.89, volume: "1.2B", marketCap: "1.8T" },
  { name: "Media", change: -1.45, volume: "0.8B", marketCap: "0.9T" },
  { name: "PSU", change: 1.67, volume: "3.4B", marketCap: "2.1T" },
  { name: "Infra", change: 0.89, volume: "2.1B", marketCap: "1.5T" },
];

export const MarketHeatmap = () => {
  const getHeatmapColor = (change: number) => {
    if (change > 3) return "bg-green-600";
    if (change > 1) return "bg-green-500";
    if (change > 0) return "bg-green-400";
    if (change > -1) return "bg-red-400";
    if (change > -3) return "bg-red-500";
    return "bg-red-600";
  };

  const getTextColor = (change: number) => {
    return Math.abs(change) > 1.5 ? "text-white" : "text-gray-900";
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <CardTitle className="text-trading-light flex items-center">
          <TrendingUp className="h-5 w-5 mr-2" />
          Market Heatmap
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {sectorData.map((sector, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg transition-all duration-200 hover:scale-105 cursor-pointer ${getHeatmapColor(sector.change)}`}
            >
              <div className={`text-sm font-semibold ${getTextColor(sector.change)}`}>
                {sector.name}
              </div>
              <div className={`text-lg font-bold flex items-center ${getTextColor(sector.change)}`}>
                {sector.change > 0 ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                {sector.change > 0 ? "+" : ""}{sector.change}%
              </div>
              <div className={`text-xs ${getTextColor(sector.change)} opacity-80`}>
                Vol: {sector.volume}
              </div>
              <div className={`text-xs ${getTextColor(sector.change)} opacity-80`}>
                Cap: {sector.marketCap}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 flex items-center justify-center space-x-4 text-xs text-trading-muted">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-600 rounded"></div>
            <span>Strong Gain (+3%)</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-400 rounded"></div>
            <span>Moderate Gain</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-400 rounded"></div>
            <span>Moderate Loss</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-600 rounded"></div>
            <span>Strong Loss (-3%)</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
