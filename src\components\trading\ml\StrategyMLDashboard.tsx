
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Brain, BarChart3, TrendingUp, Target, Zap, Activity } from "lucide-react";

interface MLModel {
  name: string;
  type: string;
  accuracy: number;
  status: 'active' | 'training' | 'stopped';
  strategies: number;
  category: string;
  lastTrained: string;
}

interface BacktestResult {
  strategy: string;
  period: string;
  trades: number;
  winRate: number;
  sharpe: number;
  maxDD: number;
}

interface StrategyMLDashboardProps {
  mlModels?: MLModel[];
  backtestResults?: BacktestResult[];
  activeModels?: number;
  avgAccuracy?: number;
  strategiesEnhanced?: number;
  avgSharpeRatio?: number;
  optimizationProgress?: number;
  currentRegime?: string;
  regimeConfidence?: number;
}

export const StrategyMLDashboard = ({
  mlModels = [],
  backtestResults = [],
  activeModels = 0,
  avgAccuracy = 0,
  strategiesEnhanced = 0,
  avgSharpeRatio = 0,
  optimizationProgress = 0,
  currentRegime = "Unknown",
  regimeConfidence = 0
}: StrategyMLDashboardProps) => {

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "text-green-400 border-green-400";
      case "training": return "text-yellow-400 border-yellow-400";
      case "stopped": return "text-red-400 border-red-400";
      default: return "text-gray-400 border-gray-400";
    }
  };

  return (
    <div className="space-y-6">
      {/* ML Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            ML Model Performance Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{activeModels}</div>
              <div className="text-sm text-trading-muted">Active Models</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{avgAccuracy.toFixed(1)}%</div>
              <div className="text-sm text-trading-muted">Avg Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{strategiesEnhanced}</div>
              <div className="text-sm text-trading-muted">Strategies Enhanced</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{avgSharpeRatio.toFixed(2)}</div>
              <div className="text-sm text-trading-muted">Avg Sharpe Ratio</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="models" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="models" className="data-[state=active]:bg-trading-accent">
            <Brain className="h-4 w-4 mr-2" />
            ML Models
          </TabsTrigger>
          <TabsTrigger value="backtesting" className="data-[state=active]:bg-trading-accent">
            <BarChart3 className="h-4 w-4 mr-2" />
            Backtesting
          </TabsTrigger>
          <TabsTrigger value="optimization" className="data-[state=active]:bg-trading-accent">
            <Target className="h-4 w-4 mr-2" />
            Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="models">
          {mlModels.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {mlModels.map((model, index) => (
                <Card key={index} className="bg-trading-darker border-trading-border">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm text-trading-light">{model.name}</CardTitle>
                      <Badge variant="outline" className={`text-xs ${getStatusColor(model.status)}`}>
                        {model.status}
                      </Badge>
                    </div>
                    <div className="flex space-x-2">
                      <Badge variant="outline" className="text-xs text-blue-400 border-blue-400">
                        {model.type}
                      </Badge>
                      <Badge variant="outline" className="text-xs text-purple-400 border-purple-400">
                        {model.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-trading-muted">Accuracy</span>
                        <span className="text-green-400">{model.accuracy}%</span>
                      </div>
                      <Progress value={model.accuracy} className="h-2" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <div className="text-trading-muted">Strategies</div>
                        <div className="text-trading-light font-medium">{model.strategies}</div>
                      </div>
                      <div>
                        <div className="text-trading-muted">Last Trained</div>
                        <div className="text-trading-light font-medium">{model.lastTrained}</div>
                      </div>
                    </div>

                    <Button size="sm" variant="outline" className="w-full text-xs">
                      {model.status === "training" ? "View Training" : "Retrain Model"}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-trading-darker border-trading-border">
              <CardContent className="p-8 text-center text-trading-muted">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No ML models available</p>
                <p className="text-sm mt-1">Create your first model to get started</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="backtesting">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Strategy Backtesting Results</CardTitle>
            </CardHeader>
            <CardContent>
              {backtestResults.length > 0 ? (
                <div className="space-y-3">
                  <div className="grid grid-cols-7 gap-4 text-xs text-trading-muted font-medium border-b border-trading-border pb-2">
                    <div>Strategy</div>
                    <div>Period</div>
                    <div>Trades</div>
                    <div>Win Rate</div>
                    <div>Sharpe</div>
                    <div>Max DD</div>
                    <div>Actions</div>
                  </div>
                  {backtestResults.map((result, index) => (
                    <div key={index} className="grid grid-cols-7 gap-4 text-xs items-center p-2 bg-trading-dark rounded border border-trading-border">
                      <div className="text-trading-light font-medium">{result.strategy}</div>
                      <div className="text-trading-muted">{result.period}</div>
                      <div className="text-trading-light">{result.trades}</div>
                      <div className="text-green-400">{result.winRate}%</div>
                      <div className="text-blue-400">{result.sharpe}</div>
                      <div className="text-red-400">{result.maxDD}%</div>
                      <Button size="sm" variant="outline" className="text-xs">
                        Details
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-trading-muted">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No backtest results available</p>
                  <p className="text-sm mt-1">Run backtests to see performance data</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Parameter Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-trading-light">Auto-optimization</span>
                    <Badge variant="outline" className={optimizationProgress > 0 ? "text-green-400 border-green-400" : "text-gray-400 border-gray-400"}>
                      {optimizationProgress > 0 ? "Running" : "Stopped"}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span className="text-trading-muted">Progress</span>
                      <span className="text-blue-400">{optimizationProgress}%</span>
                    </div>
                    <Progress value={optimizationProgress} className="h-2" />
                  </div>
                  <div className="text-xs text-trading-muted">
                    {optimizationProgress > 0 ? "Optimizing strategies..." : "No optimization running"}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Market Regime Detection</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-trading-light">Current Regime</span>
                    <Badge variant="outline" className="text-green-400 border-green-400">
                      {currentRegime}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span className="text-trading-muted">Confidence</span>
                      <span className="text-green-400">{regimeConfidence}%</span>
                    </div>
                    <Progress value={regimeConfidence} className="h-2" />
                  </div>
                  <div className="text-xs text-trading-muted">
                    {regimeConfidence > 0 ? "Market regime detected" : "Analyzing market conditions..."}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
