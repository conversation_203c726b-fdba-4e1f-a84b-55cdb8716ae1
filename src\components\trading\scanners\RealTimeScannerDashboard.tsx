
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Target, 
  AlertTriangle,
  Play,
  Pause,
  Settings,
  Filter
} from "lucide-react";
import { StrategyEngineService, Strategy, StrategySignal, ScannerResult } from "../../../services/StrategyEngineService";

interface RealTimeScannerDashboardProps {
  strategyEngine: StrategyEngineService;
}

export const RealTimeScannerDashboard: React.FC<RealTimeScannerDashboardProps> = ({ 
  strategyEngine 
}) => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [signals, setSignals] = useState<StrategySignal[]>([]);
  const [scannerResults, setScannerResults] = useState<ScannerResult[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [symbolsToScan, setSymbolsToScan] = useState('NIFTY,BANKNIFTY,RELIANCE,TCS,INFY,HDFC,ICICIBANK');

  useEffect(() => {
    // Initialize strategies
    setStrategies(strategyEngine.getAllStrategies());

    // Subscribe to real-time signals and results
    const signalsSubscription = strategyEngine.getSignalsStream().subscribe(setSignals);
    const resultsSubscription = strategyEngine.getScannerResultsStream().subscribe(setScannerResults);

    return () => {
      signalsSubscription.unsubscribe();
      resultsSubscription.unsubscribe();
    };
  }, [strategyEngine]);

  const handleStartScanning = async () => {
    const symbols = symbolsToScan.split(',').map(s => s.trim()).filter(s => s);
    setIsScanning(true);
    await strategyEngine.startScanning(symbols);
  };

  const handleStopScanning = async () => {
    setIsScanning(false);
    await strategyEngine.stopScanning();
  };

  const toggleStrategy = (strategyId: string, active: boolean) => {
    if (active) {
      strategyEngine.activateStrategy(strategyId);
    } else {
      strategyEngine.deactivateStrategy(strategyId);
    }
    setStrategies(strategyEngine.getAllStrategies());
  };

  const filteredStrategies = strategies.filter(strategy => {
    const matchesType = selectedType === 'all' || strategy.type === selectedType;
    const matchesSearch = strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         strategy.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesType && matchesSearch;
  });

  const filteredSignals = signals.filter(signal => {
    if (selectedType === 'all') return true;
    const strategy = strategies.find(s => s.id === signal.strategyId);
    return strategy?.type === selectedType;
  });

  const filteredResults = scannerResults.filter(result => {
    if (selectedType === 'all') return true;
    const strategy = strategies.find(s => s.id === result.strategyId);
    return strategy?.type === selectedType;
  });

  const getSignalColor = (action: string) => {
    switch (action) {
      case 'BUY': return 'text-green-400';
      case 'SELL': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getSignalIcon = (action: string) => {
    switch (action) {
      case 'BUY': return TrendingUp;
      case 'SELL': return TrendingDown;
      default: return Activity;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Scanner Control Panel */}
      <Card className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30">
        <CardHeader>
          <CardTitle className="text-blue-300 flex items-center">
            <Search className="h-6 w-6 mr-2" />
            Real-Time Strategy Scanner Control
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm text-blue-200">Symbols to Scan</label>
              <Input
                value={symbolsToScan}
                onChange={(e) => setSymbolsToScan(e.target.value)}
                placeholder="NIFTY,BANKNIFTY,RELIANCE..."
                className="bg-blue-900/20 border-blue-500/30 text-blue-100"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm text-blue-200">Strategy Type Filter</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="bg-blue-900/20 border-blue-500/30 text-blue-100">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="intraday">Intraday</SelectItem>
                  <SelectItem value="swing">Swing</SelectItem>
                  <SelectItem value="options">Options</SelectItem>
                  <SelectItem value="scalping">Scalping</SelectItem>
                  <SelectItem value="arbitrage">Arbitrage</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm text-blue-200">Scanner Control</label>
              <div className="flex space-x-2">
                {!isScanning ? (
                  <Button 
                    onClick={handleStartScanning}
                    className="bg-green-600 hover:bg-green-700 flex-1"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Start Scanning
                  </Button>
                ) : (
                  <Button 
                    onClick={handleStopScanning}
                    className="bg-red-600 hover:bg-red-700 flex-1"
                  >
                    <Pause className="h-4 w-4 mr-2" />
                    Stop Scanning
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-900/30 rounded">
              <div className="text-2xl font-bold text-blue-300">{strategies.length}</div>
              <div className="text-xs text-blue-200">Total Strategies</div>
            </div>
            <div className="text-center p-3 bg-green-900/30 rounded">
              <div className="text-2xl font-bold text-green-300">
                {strategies.filter(s => s.isActive).length}
              </div>
              <div className="text-xs text-green-200">Active Strategies</div>
            </div>
            <div className="text-center p-3 bg-purple-900/30 rounded">
              <div className="text-2xl font-bold text-purple-300">{signals.length}</div>
              <div className="text-xs text-purple-200">Live Signals</div>
            </div>
            <div className="text-center p-3 bg-cyan-900/30 rounded">
              <div className="text-2xl font-bold text-cyan-300">{scannerResults.length}</div>
              <div className="text-xs text-cyan-200">Scanner Results</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="strategies" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="strategies" className="data-[state=active]:bg-blue-600">
            <Settings className="h-4 w-4 mr-2" />
            Strategy Management
          </TabsTrigger>
          <TabsTrigger value="signals" className="data-[state=active]:bg-green-600">
            <Activity className="h-4 w-4 mr-2" />
            Live Signals
          </TabsTrigger>
          <TabsTrigger value="results" className="data-[state=active]:bg-purple-600">
            <Target className="h-4 w-4 mr-2" />
            Scanner Results
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-cyan-600">
            <TrendingUp className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="strategies">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center justify-between">
                <span>51 Trading Strategies</span>
                <div className="flex items-center space-x-2">
                  <Input
                    placeholder="Search strategies..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-64 bg-trading-dark border-trading-border text-trading-light"
                  />
                  <Filter className="h-4 w-4 text-trading-muted" />
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {filteredStrategies.map((strategy) => (
                  <div key={strategy.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-semibold text-trading-light">{strategy.name}</h4>
                        <Badge variant="outline" className="capitalize">
                          {strategy.type}
                        </Badge>
                        <Badge variant="outline" className="text-cyan-400 border-cyan-400">
                          {strategy.timeframe}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-trading-muted">
                          R:R {strategy.riskReward}:1
                        </span>
                        <Button
                          size="sm"
                          variant={strategy.isActive ? "destructive" : "default"}
                          onClick={() => toggleStrategy(strategy.id, !strategy.isActive)}
                        >
                          {strategy.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </div>
                    
                    <p className="text-sm text-trading-muted mb-3">{strategy.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                      <div>
                        <span className="text-trading-muted">Entry: </span>
                        <span className="text-trading-light">{strategy.entry}</span>
                      </div>
                      <div>
                        <span className="text-trading-muted">Exit: </span>
                        <span className="text-trading-light">{strategy.exit}</span>
                      </div>
                      <div>
                        <span className="text-trading-muted">Indicators: </span>
                        <span className="text-blue-400">{strategy.indicators.join(', ')}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="signals">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Live Trading Signals</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredSignals.length === 0 ? (
                <div className="text-center py-8 text-trading-muted">
                  <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No active signals</p>
                  <p className="text-xs mt-1">Start scanning to generate signals</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredSignals.map((signal, index) => {
                    const strategy = strategies.find(s => s.id === signal.strategyId);
                    const SignalIcon = getSignalIcon(signal.action);
                    
                    return (
                      <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <SignalIcon className={`h-5 w-5 ${getSignalColor(signal.action)}`} />
                            <span className="font-medium text-trading-light">{signal.symbol}</span>
                            <Badge variant="outline" className={getSignalColor(signal.action)}>
                              {signal.action}
                            </Badge>
                            <Badge variant="outline">
                              {strategy?.name || signal.strategyId}
                            </Badge>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-trading-light">
                              ₹{signal.price.toFixed(2)}
                            </div>
                            <div className="text-sm text-green-400">
                              {(signal.confidence * 100).toFixed(1)}% confidence
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-sm text-trading-muted mb-3">{signal.reason}</div>
                        
                        <div className="grid grid-cols-3 gap-4 text-xs">
                          <div>
                            <span className="text-trading-muted">Stop Loss: </span>
                            <span className="text-red-400">₹{signal.stopLoss?.toFixed(2)}</span>
                          </div>
                          <div>
                            <span className="text-trading-muted">Take Profit: </span>
                            <span className="text-green-400">₹{signal.takeProfit?.toFixed(2)}</span>
                          </div>
                          <div>
                            <span className="text-trading-muted">Time: </span>
                            <span className="text-trading-light">
                              {new Date(signal.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Scanner Results</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredResults.length === 0 ? (
                <div className="text-center py-8 text-trading-muted">
                  <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No scanner results</p>
                  <p className="text-xs mt-1">Start scanning to see results</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {filteredResults.map((result, index) => {
                    const strategy = strategies.find(s => s.id === result.strategyId);
                    
                    return (
                      <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <span className="font-medium text-trading-light">{result.symbol}</span>
                            <Badge variant="outline">
                              {strategy?.name || result.strategyId}
                            </Badge>
                          </div>
                          <div className="text-right">
                            <div className={`text-lg font-bold ${getScoreColor(result.score)}`}>
                              {result.score.toFixed(1)}
                            </div>
                            <div className="text-xs text-trading-muted">Score</div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                          {Object.entries(result.technicalIndicators).slice(0, 4).map(([key, value]) => (
                            <div key={key}>
                              <span className="text-trading-muted">{key}: </span>
                              <span className="text-trading-light">{value.toFixed(2)}</span>
                            </div>
                          ))}
                        </div>
                        
                        <div className="mt-3 text-xs text-trading-muted">
                          Last scanned: {new Date(result.lastScanned).toLocaleTimeString()}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Scanner Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold text-trading-light">Strategy Distribution</h4>
                  {['intraday', 'swing', 'options', 'scalping', 'arbitrage'].map(type => {
                    const count = strategies.filter(s => s.type === type).length;
                    const activeCount = strategies.filter(s => s.type === type && s.isActive).length;
                    
                    return (
                      <div key={type} className="flex justify-between text-sm">
                        <span className="text-trading-muted capitalize">{type}</span>
                        <span className="text-trading-light">
                          {activeCount}/{count} active
                        </span>
                      </div>
                    );
                  })}
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-semibold text-trading-light">Signal Statistics</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Total Signals</span>
                      <span className="text-trading-light">{signals.length}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Buy Signals</span>
                      <span className="text-green-400">
                        {signals.filter(s => s.action === 'BUY').length}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Sell Signals</span>
                      <span className="text-red-400">
                        {signals.filter(s => s.action === 'SELL').length}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Avg Confidence</span>
                      <span className="text-trading-light">
                        {signals.length > 0 
                          ? (signals.reduce((sum, s) => sum + s.confidence, 0) / signals.length * 100).toFixed(1)
                          : 0}%
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-semibold text-trading-light">Scanner Health</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Status</span>
                      <Badge variant="outline" className={isScanning ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                        {isScanning ? 'Running' : 'Stopped'}
                      </Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Scan Frequency</span>
                      <span className="text-trading-light">30s</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-trading-muted">Last Scan</span>
                      <span className="text-trading-light">
                        {new Date().toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
