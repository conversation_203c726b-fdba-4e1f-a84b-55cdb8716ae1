
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { TradingDashboardGrid, WidgetGrid } from "@/components/ui/responsive-grid";
import { EnhancedCard, TradingCard } from "@/components/ui/enhanced-card";
import { TrendingUp, TrendingDown, Activity, Eye, Brain, BarChart } from "lucide-react";
import { cn } from "@/lib/utils";

interface MarketDataItem {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  trending: 'up' | 'down';
}

interface EnhancedMarketDataDashboardProps {
  onStockSelect?: (symbol: string) => void;
  marketData?: MarketDataItem[];
  advancing?: number;
  declining?: number;
  totalVolume?: number;
  marketSentiment?: number;
  isLoading?: boolean;
}

export const EnhancedMarketDataDashboard = ({ 
  onStockSelect,
  marketData = [],
  advancing = 0,
  declining = 0,
  totalVolume = 0,
  marketSentiment = 0,
  isLoading = false
}: EnhancedMarketDataDashboardProps) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState("1D");

  const timeframes = ["1D", "1W", "1M", "3M", "1Y"];

  return (
    <div className="space-y-6 p-6">
      {/* Enhanced Market Overview Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div className="slide-in-left">
          <h2 className="text-3xl font-bold text-trading-light bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            Market Overview
          </h2>
          <p className="text-trading-muted mt-1">Real-time market data and advanced analytics</p>
        </div>
        
        <div className="flex items-center gap-4 slide-in-right">
          <div className="flex gap-2">
            {timeframes.map((tf) => (
              <Button
                key={tf}
                variant={tf === selectedTimeframe ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTimeframe(tf)}
                className={cn(
                  "text-xs glass-button micro-scale",
                  tf === selectedTimeframe && "pulse-glow"
                )}
                disabled={isLoading}
              >
                {tf}
              </Button>
            ))}
          </div>
          <ThemeToggle />
        </div>
      </div>

      {/* Enhanced Market Statistics */}
      <WidgetGrid className="mb-6">
        <TradingCard 
          title="Advancing" 
          value={advancing}
          icon={<TrendingUp className="h-4 w-4 text-green-400" />}
          loading={isLoading}
          className="bounce-in"
        >
          <div className="text-xs text-trading-muted">Stocks moving up</div>
        </TradingCard>
        
        <TradingCard 
          title="Declining" 
          value={declining}
          icon={<TrendingDown className="h-4 w-4 text-red-400" />}
          loading={isLoading}
          className="bounce-in"
          style={{ animationDelay: '0.1s' }}
        >
          <div className="text-xs text-trading-muted">Stocks moving down</div>
        </TradingCard>
        
        <TradingCard 
          title="Total Volume" 
          value={totalVolume > 0 ? `₹${(totalVolume / 10000000).toFixed(1)}Cr` : '₹0'}
          icon={<BarChart className="h-4 w-4 text-blue-400" />}
          loading={isLoading}
          className="bounce-in"
          style={{ animationDelay: '0.2s' }}
        >
          <div className="text-xs text-trading-muted">Market turnover</div>
        </TradingCard>
        
        <TradingCard 
          title="Market Sentiment" 
          value={marketSentiment.toFixed(1)}
          icon={<Activity className="h-4 w-4 text-yellow-400" />}
          loading={isLoading}
          className="bounce-in"
          style={{ animationDelay: '0.3s' }}
        >
          <div className="text-xs text-trading-muted">Overall mood</div>
        </TradingCard>
      </WidgetGrid>

      {/* Enhanced Market Data Grid */}
      {marketData.length > 0 ? (
        <TradingDashboardGrid>
          {marketData.map((stock, index) => {
            const isPositive = stock.trending === "up";
            return (
              <EnhancedCard 
                key={stock.symbol}
                className={cn(
                  "cursor-pointer transition-all duration-300",
                  "hover:scale-105 hover:z-10",
                  isPositive ? "glass-status-positive" : "glass-status-negative"
                )}
                onClick={() => onStockSelect?.(stock.symbol)}
                animation="slide-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-bold text-trading-light">{stock.symbol}</h3>
                      <div className={cn(
                        "w-2 h-2 rounded-full animate-pulse",
                        isPositive ? "bg-green-400" : "bg-red-400"
                      )}></div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {isPositive ? (
                        <TrendingUp className="h-4 w-4 text-green-400" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-400" />
                      )}
                      <Badge 
                        variant="outline" 
                        className={cn(
                          "text-xs font-medium micro-scale",
                          isPositive 
                            ? "text-green-400 border-green-400 price-flash-green" 
                            : "text-red-400 border-red-400 price-flash-red"
                        )}
                      >
                        {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="text-2xl font-bold text-trading-light">
                      ₹{stock.price.toLocaleString()}
                    </div>
                    
                    <div className={cn(
                      "text-sm font-medium",
                      isPositive ? "text-green-400" : "text-red-400"
                    )}>
                      {stock.change >= 0 ? '+' : ''}₹{stock.change.toFixed(2)}
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="text-xs flex-1 glass-button micro-scale"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        Analyze
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="text-xs flex-1 glass-button micro-scale"
                      >
                        <Brain className="h-3 w-3 mr-1" />
                        AI Insights
                      </Button>
                    </div>
                  </div>
                </div>
              </EnhancedCard>
            );
          })}
        </TradingDashboardGrid>
      ) : (
        <EnhancedCard className="py-16">
          <div className="text-center">
            <div className="shimmer-loading h-16 w-16 mx-auto mb-4 rounded-full"></div>
            <div className="text-lg text-trading-muted mb-2">
              {isLoading ? 'Loading market data...' : 'No market data available'}
            </div>
            <div className="text-sm text-trading-muted">
              {isLoading ? 'Fetching real-time prices...' : 'Connect to market data feed to view real-time prices'}
            </div>
          </div>
        </EnhancedCard>
      )}
    </div>
  );
};
