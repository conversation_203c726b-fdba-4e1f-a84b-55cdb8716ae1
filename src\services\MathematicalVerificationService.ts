
import { TechnicalIndicatorService } from './TechnicalIndicatorService';
import { PnLCalculationService } from './PnLCalculationService';
import { RiskCalculationService } from './RiskCalculationService';
import { StrategyService } from './StrategyService';

export interface VerificationResult {
  function: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  expectedValue?: number;
  actualValue?: number;
  error?: string;
}

export interface MathematicalAudit {
  totalTests: number;
  passed: number;
  failed: number;
  warnings: number;
  results: VerificationResult[];
  summary: string;
}

export class MathematicalVerificationService {
  
  // Test data for verification
  private static getTestData() {
    return {
      prices: [100, 102, 101, 103, 105, 104, 106, 108, 107, 109],
      volumes: [1000, 1200, 900, 1500, 1300, 1100, 1400, 1600, 1200, 1800],
      timestamps: Array.from({length: 10}, (_, i) => Date.now() + i * 60000)
    };
  }

  // Verify Simple Moving Average calculation
  static verifySMA(): VerificationResult {
    try {
      const prices = [10, 20, 30, 40, 50];
      const period = 3;
      const result = TechnicalIndicatorService.calculateSMA(prices, period);
      
      // Expected: [(10+20+30)/3, (20+30+40)/3, (30+40+50)/3] = [20, 30, 40]
      const expected = [20, 30, 40];
      
      if (result.length === expected.length && 
          result.every((val, i) => Math.abs(val - expected[i]) < 0.001)) {
        return {
          function: 'SMA Calculation',
          status: 'PASS',
          message: 'SMA calculations are mathematically correct'
        };
      }
      
      return {
        function: 'SMA Calculation',
        status: 'FAIL',
        message: 'SMA calculations do not match expected values',
        expectedValue: expected[0],
        actualValue: result[0]
      };
    } catch (error) {
      return {
        function: 'SMA Calculation',
        status: 'FAIL',
        message: 'SMA calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify Exponential Moving Average calculation
  static verifyEMA(): VerificationResult {
    try {
      const prices = [10, 20, 30];
      const period = 2;
      const result = TechnicalIndicatorService.calculateEMA(prices, period);
      
      // For period 2, multiplier = 2/(2+1) = 0.667
      // First EMA = SMA = (10+20)/2 = 15
      // Second EMA = (30 * 0.667) + (15 * 0.333) = 20 + 5 = 25
      const expected = [15, 25];
      
      if (result.length === expected.length && 
          result.every((val, i) => Math.abs(val - expected[i]) < 0.1)) {
        return {
          function: 'EMA Calculation',
          status: 'PASS',
          message: 'EMA calculations are mathematically correct'
        };
      }
      
      return {
        function: 'EMA Calculation',
        status: 'FAIL',
        message: 'EMA calculations do not match expected values',
        expectedValue: expected[0],
        actualValue: result[0]
      };
    } catch (error) {
      return {
        function: 'EMA Calculation',
        status: 'FAIL',
        message: 'EMA calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify RSI calculation
  static verifyRSI(): VerificationResult {
    try {
      // Use known values where RSI can be calculated manually
      const prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89, 46.03, 46.83, 47.69, 46.49, 46.26];
      const result = TechnicalIndicatorService.calculateRSI(prices, 14);
      
      if (result.length > 0 && result[0].value >= 0 && result[0].value <= 100) {
        return {
          function: 'RSI Calculation',
          status: 'PASS',
          message: 'RSI calculations are within valid range (0-100)'
        };
      }
      
      return {
        function: 'RSI Calculation',
        status: 'FAIL',
        message: 'RSI calculations are outside valid range',
        actualValue: result[0]?.value
      };
    } catch (error) {
      return {
        function: 'RSI Calculation',
        status: 'FAIL',
        message: 'RSI calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify P&L calculations
  static verifyPnLCalculations(): VerificationResult {
    try {
      // Test unrealized P&L calculation
      const quantity = 100;
      const averagePrice = 50;
      const currentPrice = 55;
      const unrealizedPnL = PnLCalculationService.calculateUnrealizedPnL(quantity, averagePrice, currentPrice);
      
      // Expected: 100 * (55 - 50) = 500
      const expected = 500;
      
      if (Math.abs(unrealizedPnL - expected) < 0.001) {
        return {
          function: 'P&L Calculation',
          status: 'PASS',
          message: 'P&L calculations are mathematically correct'
        };
      }
      
      return {
        function: 'P&L Calculation',
        status: 'FAIL',
        message: 'P&L calculations do not match expected values',
        expectedValue: expected,
        actualValue: unrealizedPnL
      };
    } catch (error) {
      return {
        function: 'P&L Calculation',
        status: 'FAIL',
        message: 'P&L calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify Risk calculations
  static verifyRiskCalculations(): VerificationResult {
    try {
      // Test VaR calculation with known distribution
      const returns = [-0.05, -0.03, -0.01, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07];
      const var95 = RiskCalculationService.calculateVaR(returns, 0.95);
      
      // For 95% confidence, we expect the 5th percentile (worst 5%)
      // With 10 data points, 5% = 0.5, so we take the 1st worst value = 0.05
      if (var95 > 0 && var95 <= 0.1) {
        return {
          function: 'Risk Calculation (VaR)',
          status: 'PASS',
          message: 'VaR calculations are within reasonable bounds'
        };
      }
      
      return {
        function: 'Risk Calculation (VaR)',
        status: 'FAIL',
        message: 'VaR calculations are outside reasonable bounds',
        actualValue: var95
      };
    } catch (error) {
      return {
        function: 'Risk Calculation (VaR)',
        status: 'FAIL',
        message: 'VaR calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify Volatility calculations
  static verifyVolatilityCalculations(): VerificationResult {
    try {
      // Test with known standard deviation
      const returns = [0.01, 0.02, 0.01, 0.03, 0.02]; // Mean = 0.018
      const volatility = RiskCalculationService.calculateVolatility(returns);
      
      // Manual calculation: sqrt(sum((x-mean)^2)/n)
      const mean = 0.018;
      const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
      const expectedVol = Math.sqrt(variance);
      
      if (Math.abs(volatility - expectedVol) < 0.0001) {
        return {
          function: 'Volatility Calculation',
          status: 'PASS',
          message: 'Volatility calculations are mathematically correct'
        };
      }
      
      return {
        function: 'Volatility Calculation',
        status: 'FAIL',
        message: 'Volatility calculations do not match expected values',
        expectedValue: expectedVol,
        actualValue: volatility
      };
    } catch (error) {
      return {
        function: 'Volatility Calculation',
        status: 'FAIL',
        message: 'Volatility calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify Correlation calculations
  static verifyCorrelationCalculations(): VerificationResult {
    try {
      // Test with perfectly correlated data
      const returns1 = [0.01, 0.02, 0.03, 0.04, 0.05];
      const returns2 = [0.02, 0.04, 0.06, 0.08, 0.10]; // 2x returns1
      
      const correlation = RiskCalculationService.calculateCorrelation(returns1, returns2);
      
      // Perfect positive correlation should be 1.0
      if (Math.abs(correlation - 1.0) < 0.001) {
        return {
          function: 'Correlation Calculation',
          status: 'PASS',
          message: 'Correlation calculations are mathematically correct'
        };
      }
      
      return {
        function: 'Correlation Calculation',
        status: 'FAIL',
        message: 'Correlation calculations do not match expected values',
        expectedValue: 1.0,
        actualValue: correlation
      };
    } catch (error) {
      return {
        function: 'Correlation Calculation',
        status: 'FAIL',
        message: 'Correlation calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Verify Black-Scholes implementation (if available in paper trading)
  static verifyBlackScholesCalculations(): VerificationResult {
    try {
      // This would test the Black-Scholes implementation in RealPaperTradingService
      // For now, return a warning as we need to check if it's implemented correctly
      return {
        function: 'Black-Scholes Options Pricing',
        status: 'WARNING',
        message: 'Black-Scholes implementation needs manual verification with known option pricing examples'
      };
    } catch (error) {
      return {
        function: 'Black-Scholes Options Pricing',
        status: 'FAIL',
        message: 'Black-Scholes calculation threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Run comprehensive mathematical audit
  static async runFullAudit(): Promise<MathematicalAudit> {
    console.log('🔍 Starting comprehensive mathematical verification audit...');
    
    const results: VerificationResult[] = [
      this.verifySMA(),
      this.verifyEMA(), 
      this.verifyRSI(),
      this.verifyPnLCalculations(),
      this.verifyRiskCalculations(),
      this.verifyVolatilityCalculations(),
      this.verifyCorrelationCalculations(),
      this.verifyBlackScholesCalculations()
    ];

    const passed = results.filter(r => r.status === 'PASS').length;
    const failed = results.filter(r => r.status === 'FAIL').length;
    const warnings = results.filter(r => r.status === 'WARNING').length;

    let summary = `📊 Mathematical Verification Complete:\n`;
    summary += `✅ ${passed} tests PASSED\n`;
    summary += `❌ ${failed} tests FAILED\n`;
    summary += `⚠️ ${warnings} tests have WARNINGS\n`;
    
    if (failed === 0) {
      summary += `\n🎉 All critical mathematical functions are verified and working correctly!`;
    } else {
      summary += `\n⚠️ ${failed} critical mathematical functions need attention.`;
    }

    console.log(summary);
    
    return {
      totalTests: results.length,
      passed,
      failed,
      warnings,
      results,
      summary
    };
  }

  // Verify data integrity and consistency
  static verifyDataIntegrity(): VerificationResult {
    try {
      const testData = this.getTestData();
      
      // Check if prices are reasonable
      const hasNegativePrices = testData.prices.some(p => p < 0);
      const hasInfinitePrices = testData.prices.some(p => !isFinite(p));
      const hasNaNPrices = testData.prices.some(p => isNaN(p));
      
      if (hasNegativePrices || hasInfinitePrices || hasNaNPrices) {
        return {
          function: 'Data Integrity Check',
          status: 'FAIL',
          message: 'Test data contains invalid values (negative, infinite, or NaN)'
        };
      }
      
      return {
        function: 'Data Integrity Check', 
        status: 'PASS',
        message: 'All test data is valid and consistent'
      };
    } catch (error) {
      return {
        function: 'Data Integrity Check',
        status: 'FAIL',
        message: 'Data integrity check threw an error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
