
import { TechnicalIndicatorService } from './TechnicalIndicatorService';

export interface MarketData {
  symbol: string;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface AIAnalysisResult {
  signal: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL';
  confidence: number;
  reasoning: string[];
  technicalScore: number;
  momentumScore: number;
  volumeScore: number;
  riskScore: number;
}

export interface PatternRecognition {
  pattern: string;
  probability: number;
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  timeframe: string;
}

export interface RiskAssessment {
  portfolioRisk: number;
  positionRisk: number;
  marketRisk: number;
  volatilityRisk: number;
  recommendations: string[];
}

export class RealAIAgentService {
  // Market Analysis AI Agent
  async analyzeMarket(data: MarketData[]): Promise<AIAnalysisResult> {
    if (data.length < 20) {
      throw new Error('Insufficient data for analysis');
    }

    const prices = data.map(d => d.close);
    const volumes = data.map(d => d.volume);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);

    // Technical Analysis
    const rsi = TechnicalIndicatorService.calculateRSI(prices, 14);
    const sma20 = TechnicalIndicatorService.calculateSMA(prices, 20);
    const sma50 = TechnicalIndicatorService.calculateSMA(prices, 50);
    const macd = TechnicalIndicatorService.calculateMACD(prices);

    const currentPrice = prices[prices.length - 1];
    const currentRSI = rsi[rsi.length - 1]?.value || 50;
    const currentSMA20 = sma20[sma20.length - 1] || currentPrice;
    const currentSMA50 = sma50[sma50.length - 1] || currentPrice;
    const currentMACD = macd[macd.length - 1];

    // Technical Score Calculation
    let technicalScore = 0;
    const reasoning: string[] = [];

    // RSI Analysis
    if (currentRSI < 30) {
      technicalScore += 20;
      reasoning.push('RSI indicates oversold condition - potential reversal');
    } else if (currentRSI > 70) {
      technicalScore -= 20;
      reasoning.push('RSI indicates overbought condition - potential correction');
    }

    // Moving Average Analysis
    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
      technicalScore += 15;
      reasoning.push('Price above both SMA20 and SMA50 - strong uptrend');
    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
      technicalScore -= 15;
      reasoning.push('Price below both SMA20 and SMA50 - strong downtrend');
    }

    // MACD Analysis
    if (currentMACD?.crossover === 'bullish') {
      technicalScore += 10;
      reasoning.push('MACD bullish crossover detected - momentum shifting up');
    } else if (currentMACD?.crossover === 'bearish') {
      technicalScore -= 10;
      reasoning.push('MACD bearish crossover detected - momentum shifting down');
    }

    // Momentum Score
    const priceChange = ((currentPrice - prices[prices.length - 5]) / prices[prices.length - 5]) * 100;
    const momentumScore = Math.max(-50, Math.min(50, priceChange * 10));

    // Volume Analysis
    const avgVolume = volumes.slice(-10).reduce((a, b) => a + b, 0) / 10;
    const currentVolume = volumes[volumes.length - 1];
    const volumeRatio = currentVolume / avgVolume;
    
    let volumeScore = 0;
    if (volumeRatio > 1.5) {
      volumeScore = 20;
      reasoning.push('High volume confirms price movement');
    } else if (volumeRatio < 0.5) {
      volumeScore = -10;
      reasoning.push('Low volume suggests weak conviction');
    }

    // Risk Score (Volatility-based)
    const returns = prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]);
    const volatility = Math.sqrt(returns.reduce((sum, ret) => sum + ret * ret, 0) / returns.length) * Math.sqrt(252);
    const riskScore = Math.max(0, Math.min(100, volatility * 100));

    // Final Signal Determination
    const totalScore = technicalScore + momentumScore + volumeScore - riskScore;
    let signal: AIAnalysisResult['signal'];
    
    if (totalScore > 30) signal = 'STRONG_BUY';
    else if (totalScore > 10) signal = 'BUY';
    else if (totalScore > -10) signal = 'HOLD';
    else if (totalScore > -30) signal = 'SELL';
    else signal = 'STRONG_SELL';

    const confidence = Math.min(95, Math.max(55, Math.abs(totalScore) + 50));

    return {
      signal,
      confidence,
      reasoning,
      technicalScore,
      momentumScore,
      volumeScore,
      riskScore
    };
  }

  // Pattern Recognition AI
  async detectPatterns(data: MarketData[]): Promise<PatternRecognition[]> {
    const patterns: PatternRecognition[] = [];
    const prices = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);

    // Double Top/Bottom Detection
    const peaks = this.findPeaks(highs);
    const troughs = this.findTroughs(lows);

    if (peaks.length >= 2) {
      const lastTwoPeaks = peaks.slice(-2);
      const priceDiff = Math.abs(highs[lastTwoPeaks[0]] - highs[lastTwoPeaks[1]]);
      const avgPrice = (highs[lastTwoPeaks[0]] + highs[lastTwoPeaks[1]]) / 2;
      
      if (priceDiff / avgPrice < 0.02) { // Within 2%
        patterns.push({
          pattern: 'Double Top',
          probability: 0.75,
          type: 'BEARISH',
          timeframe: '1D'
        });
      }
    }

    if (troughs.length >= 2) {
      const lastTwoTroughs = troughs.slice(-2);
      const priceDiff = Math.abs(lows[lastTwoTroughs[0]] - lows[lastTwoTroughs[1]]);
      const avgPrice = (lows[lastTwoTroughs[0]] + lows[lastTwoTroughs[1]]) / 2;
      
      if (priceDiff / avgPrice < 0.02) { // Within 2%
        patterns.push({
          pattern: 'Double Bottom',
          probability: 0.75,
          type: 'BULLISH',
          timeframe: '1D'
        });
      }
    }

    // Support/Resistance Levels
    const supportLevels = this.calculateSupportResistance(lows, 'support');
    const resistanceLevels = this.calculateSupportResistance(highs, 'resistance');
    
    const currentPrice = prices[prices.length - 1];
    
    supportLevels.forEach(level => {
      if (Math.abs(currentPrice - level) / currentPrice < 0.01) {
        patterns.push({
          pattern: 'Support Level Test',
          probability: 0.65,
          type: 'BULLISH',
          timeframe: '1D'
        });
      }
    });

    resistanceLevels.forEach(level => {
      if (Math.abs(currentPrice - level) / currentPrice < 0.01) {
        patterns.push({
          pattern: 'Resistance Level Test',
          probability: 0.65,
          type: 'BEARISH',
          timeframe: '1D'
        });
      }
    });

    return patterns;
  }

  // Risk Management AI
  async assessRisk(portfolioData: any, marketData: MarketData[]): Promise<RiskAssessment> {
    const prices = marketData.map(d => d.close);
    const returns = prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]);
    
    // Calculate Value at Risk (VaR)
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const var95 = sortedReturns[Math.floor(sortedReturns.length * 0.05)];
    
    // Portfolio Risk Calculation
    const volatility = Math.sqrt(returns.reduce((sum, ret) => sum + ret * ret, 0) / returns.length);
    const portfolioRisk = Math.abs(var95) * 100;
    
    // Position Risk (based on concentration)
    const positionRisk = portfolioData?.concentration || 0;
    
    // Market Risk (based on correlation with broader market)
    const marketRisk = volatility * 100;
    
    // Volatility Risk
    const volatilityRisk = volatility > 0.02 ? 100 : volatility * 5000;
    
    const recommendations: string[] = [];
    
    if (portfolioRisk > 5) {
      recommendations.push('Consider reducing position size - high VaR detected');
    }
    
    if (positionRisk > 20) {
      recommendations.push('Portfolio concentration risk - diversify holdings');
    }
    
    if (marketRisk > 30) {
      recommendations.push('High market volatility - consider hedging strategies');
    }
    
    if (volatilityRisk > 60) {
      recommendations.push('Extreme volatility detected - reduce leverage');
    }

    return {
      portfolioRisk,
      positionRisk,
      marketRisk,
      volatilityRisk,
      recommendations
    };
  }

  // Helper Methods
  private findPeaks(data: number[]): number[] {
    const peaks: number[] = [];
    for (let i = 1; i < data.length - 1; i++) {
      if (data[i] > data[i - 1] && data[i] > data[i + 1]) {
        peaks.push(i);
      }
    }
    return peaks;
  }

  private findTroughs(data: number[]): number[] {
    const troughs: number[] = [];
    for (let i = 1; i < data.length - 1; i++) {
      if (data[i] < data[i - 1] && data[i] < data[i + 1]) {
        troughs.push(i);
      }
    }
    return troughs;
  }

  private calculateSupportResistance(data: number[], type: 'support' | 'resistance'): number[] {
    const levels: number[] = [];
    const sortedData = [...data].sort((a, b) => type === 'support' ? a - b : b - a);
    
    for (let i = 0; i < Math.min(5, sortedData.length); i++) {
      const level = sortedData[i];
      const touchCount = data.filter(price => Math.abs(price - level) / level < 0.005).length;
      
      if (touchCount >= 2) {
        levels.push(level);
      }
    }
    
    return levels;
  }
}
