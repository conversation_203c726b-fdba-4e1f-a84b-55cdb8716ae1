
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, TrendingUp, TrendingDown, Minus } from "lucide-react";
import { TradingViewChart, ChartData } from "../charting/TradingViewChart";

interface StockAnalysisProps {
  symbol: string;
  onBack: () => void;
}

export const StockAnalysis = ({ symbol, onBack }: StockAnalysisProps) => {
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [stockInfo, setStockInfo] = useState({
    price: 0,
    change: 0,
    changePercent: 0,
    volume: 0,
    marketCap: 0,
    pe: 0,
    high52w: 0,
    low52w: 0
  });

  useEffect(() => {
    // Real data would come from API
    // For now, showing structure only - no mock data
    console.log(`Loading real data for ${symbol}`);
  }, [symbol]);

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-500" />;
  };

  const getTrendColor = (change: number) => {
    if (change > 0) return "text-green-500";
    if (change < 0) return "text-red-500";
    return "text-gray-500";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-trading-light">{symbol}</h1>
            <p className="text-trading-muted">Stock Analysis</p>
          </div>
        </div>
      </div>

      {/* Price Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            <div>
              <p className="text-sm text-trading-muted">Price</p>
              <p className="text-lg font-bold text-trading-light">
                ₹{stockInfo.price.toFixed(2) || '--'}
              </p>
            </div>
            <div>
              <p className="text-sm text-trading-muted">Change</p>
              <div className={`flex items-center ${getTrendColor(stockInfo.change)}`}>
                {getTrendIcon(stockInfo.change)}
                <span className="ml-1 font-medium">
                  {stockInfo.change ? stockInfo.change.toFixed(2) : '--'}
                </span>
              </div>
            </div>
            <div>
              <p className="text-sm text-trading-muted">Change %</p>
              <div className={`flex items-center ${getTrendColor(stockInfo.changePercent)}`}>
                <span className="font-medium">
                  {stockInfo.changePercent ? `${stockInfo.changePercent.toFixed(2)}%` : '--'}
                </span>
              </div>
            </div>
            <div>
              <p className="text-sm text-trading-muted">Volume</p>
              <p className="text-lg font-bold text-trading-light">
                {stockInfo.volume ? (stockInfo.volume / 1000000).toFixed(2) + 'M' : '--'}
              </p>
            </div>
            <div>
              <p className="text-sm text-trading-muted">Market Cap</p>
              <p className="text-lg font-bold text-trading-light">
                {stockInfo.marketCap ? '₹' + (stockInfo.marketCap / 10000000).toFixed(0) + 'Cr' : '--'}
              </p>
            </div>
            <div>
              <p className="text-sm text-trading-muted">P/E Ratio</p>
              <p className="text-lg font-bold text-trading-light">
                {stockInfo.pe ? stockInfo.pe.toFixed(2) : '--'}
              </p>
            </div>
            <div>
              <p className="text-sm text-trading-muted">52W High</p>
              <p className="text-lg font-bold text-trading-light">
                ₹{stockInfo.high52w.toFixed(2) || '--'}
              </p>
            </div>
            <div>
              <p className="text-sm text-trading-muted">52W Low</p>
              <p className="text-lg font-bold text-trading-light">
                ₹{stockInfo.low52w.toFixed(2) || '--'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chart */}
      <TradingViewChart 
        symbol={symbol}
        data={chartData}
        height={500}
      />

      {/* Analysis Tabs */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Technical Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-trading-muted">Connect to data feed for technical analysis</p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Fundamental Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-trading-muted">Connect to data feed for fundamental analysis</p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Sentiment Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm text-trading-muted">Connect to data feed for sentiment analysis</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
