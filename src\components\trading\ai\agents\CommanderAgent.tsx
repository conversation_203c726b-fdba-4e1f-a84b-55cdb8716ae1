
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Crown, Settings, Users, Activity, AlertCircle } from "lucide-react";
import { useState } from "react";

export const CommanderAgent = () => {
  const [isActive, setIsActive] = useState(true);
  
  const agentStatus = [
    { name: "Scalping", status: "active", performance: "+12.3%" },
    { name: "Swing", status: "active", performance: "+8.7%" },
    { name: "Options", status: "training", performance: "+5.1%" },
    { name: "Risk", status: "active", performance: "Protected" },
    { name: "Analysis", status: "active", performance: "94% Quality" },
  ];

  return (
    <Card className="bg-trading-darker border-trading-border border-2 border-blue-500">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Crown className="h-5 w-5 mr-2 text-yellow-400" />
            Commander Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
              {isActive ? "Coordinating" : "Offline"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">System Health</div>
            <div className="text-2xl font-bold text-green-400">98%</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Active Agents</div>
            <div className="text-2xl font-bold text-blue-400">4/5</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Coordination Score</div>
            <div className="text-2xl font-bold text-trading-light">95%</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Total P&L</div>
            <div className="text-2xl font-bold text-green-400">₹1,47,530</div>
          </div>
        </div>

        <div className="space-y-3 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Master Control</span>
            <Switch checked={isActive} onCheckedChange={setIsActive} />
          </div>

          <div className="space-y-2">
            <div className="text-sm text-trading-light mb-2">Agent Status Matrix</div>
            {agentStatus.map((agent, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded border border-trading-border">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    agent.status === "active" ? "bg-green-500" : 
                    agent.status === "training" ? "bg-yellow-500" : "bg-red-500"
                  }`}></div>
                  <span className="text-xs text-trading-light">{agent.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-trading-muted">{agent.performance}</span>
                  <Badge variant="outline" className={`text-xs ${
                    agent.status === "active" ? "text-green-400 border-green-400" : 
                    agent.status === "training" ? "text-yellow-400 border-yellow-400" : "text-red-400 border-red-400"
                  }`}>
                    {agent.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1">
            <Settings className="h-4 w-4 mr-2" />
            Orchestrate
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Users className="h-4 w-4 mr-2" />
            Agents
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Activity className="h-4 w-4 mr-2" />
            Monitor
          </Button>
        </div>

        <div className="mt-4 p-2 bg-blue-900/20 rounded border border-blue-500/30">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-blue-400" />
            <span className="text-xs text-blue-400">System operating at peak efficiency</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
