import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  BarChart3,
  TrendingUp,
  Activity,
  Shield,
  Brain,
  Zap,
  Target,
  Users,
  Bell,
  Database,
  ChevronRight,
  ChevronDown,
  Layers,
  PieChart,
  LineChart,
  Eye,
  Code,
  Smartphone,
  Server,
  Settings,
  Star
} from "lucide-react";

interface SidebarGroup {
  id: string;
  label: string;
  icon: any;
  color: string;
  items: { id: string; label: string; icon: any; badge?: string }[];
}

interface SidebarProps {
  onNavigate: (viewId: string) => void;
  activeView: string;
}

export const Sidebar = ({ onNavigate, activeView }: SidebarProps) => {
  const [expandedGroups, setExpandedGroups] = useState<string[]>(["analytics", "trading", "ai", "risk", "system"]);
  const [notifications] = useState({
    alerts: 3,
    signals: 12,
    risks: 1
  });

  const sidebarGroups: SidebarGroup[] = [
    {
      id: "analytics",
      label: "Analytics & Research",
      icon: BarChart3,
      color: "text-blue-400",
      items: [
        { id: "dashboard", label: "Market Overview", icon: BarChart3 },
        { id: "market-movers", label: "Market Movers", icon: TrendingUp },
        { id: "watchlist", label: "Watchlists", icon: Star },
        { id: "analytics-tools", label: "Analytics Tools", icon: Target },
        { id: "charting", label: "Advanced Charts", icon: LineChart },
        { id: "order-flow", label: "Order Flow Analysis", icon: Activity },
        { id: "smart-money", label: "Smart Money", icon: Users },
        { id: "analytics", label: "Performance", icon: PieChart }
      ]
    },
    {
      id: "trading",
      label: "Trading & Execution",
      icon: Zap,
      color: "text-green-400",
      items: [
        { id: "paper-trading", label: "Paper Trading", icon: Target },
        { id: "live-trading", label: "Live Trading", icon: Zap, badge: "Pro" },
        { id: "options", label: "Options Trading", icon: Target },
        { id: "scanners", label: "Stock Scanners", icon: Eye, badge: `${notifications.signals}` },
        { id: "backtesting", label: "Strategy Testing", icon: LineChart }
      ]
    },
    {
      id: "ai",
      label: "AI & Machine Learning",
      icon: Brain,
      color: "text-purple-400",
      items: [
        { id: "ai-agents", label: "AI Trading Agents", icon: Brain },
        { id: "ml-models", label: "ML Models", icon: Layers },
        { id: "strategy-ml", label: "Strategy Builder", icon: Zap }
      ]
    },
    {
      id: "risk",
      label: "Risk & Alerts",
      icon: Shield,
      color: "text-red-400",
      items: [
        { id: "risk", label: "Risk Dashboard", icon: Shield, badge: notifications.risks ? `${notifications.risks}` : undefined },
        { id: "alerts", label: "Alert Center", icon: Bell, badge: `${notifications.alerts}` }
      ]
    },
    {
      id: "system",
      label: "System & Tools",
      icon: Server,
      color: "text-gray-400",
      items: [
        { id: "system", label: "System Status", icon: Server },
        { id: "live-data", label: "Data Feeds", icon: Database },
        { id: "api-docs", label: "API Documentation", icon: Code },
        { id: "mobile", label: "Mobile Sync", icon: Smartphone },
        { id: "settings", label: "Settings", icon: Settings }
      ]
    }
  ];

  const toggleGroup = (groupId: string) => {
    setExpandedGroups(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  return (
    <div className="h-full bg-gradient-to-b from-trading-darker via-trading-dark to-trading-darker border-r border-trading-border flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-trading-border">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <TrendingUp className="h-3 w-3 text-white" />
          </div>
          <div className="hidden lg:block">
            <h2 className="font-bold text-trading-light text-sm">TradePro</h2>
            <p className="text-xs text-trading-muted">AI Trading Suite</p>
          </div>
        </div>
      </div>

      {/* Compact System Health */}
      <div className="p-2">
        <div className="bg-trading-darker border border-trading-border rounded-lg p-2">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-trading-light">System</span>
            <Badge variant="outline" className="text-xs px-1 py-0 h-4 text-green-400 border-green-400">
              Online
            </Badge>
          </div>
          <div className="grid grid-cols-2 gap-1">
            <div className="text-center">
              <div className="text-xs text-green-400">98%</div>
              <div className="text-xs text-trading-muted">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-xs text-blue-400">12ms</div>
              <div className="text-xs text-trading-muted">Latency</div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Groups */}
      <ScrollArea className="flex-1 px-2">
        <div className="space-y-1">
          {sidebarGroups.map((group) => {
            const GroupIcon = group.icon;
            const isExpanded = expandedGroups.includes(group.id);
            
            return (
              <div key={group.id} className="space-y-1">
                <Button
                  variant="ghost"
                  onClick={() => toggleGroup(group.id)}
                  className="w-full justify-start p-2 h-auto hover:bg-trading-accent/50 text-xs"
                >
                  <div className="flex items-center space-x-2 w-full">
                    <GroupIcon className={`h-3 w-3 ${group.color} flex-shrink-0`} />
                    <span className="font-medium text-trading-light flex-1 text-left truncate hidden lg:block">
                      {group.label}
                    </span>
                    <div className="hidden lg:block">
                      {isExpanded ? 
                        <ChevronDown className="h-3 w-3 text-trading-muted" /> : 
                        <ChevronRight className="h-3 w-3 text-trading-muted" />
                      }
                    </div>
                  </div>
                </Button>
                
                {isExpanded && (
                  <div className="ml-2 space-y-1">
                    {group.items.map((item) => {
                      const ItemIcon = item.icon;
                      const isActive = activeView === item.id;
                      return (
                        <Button
                          key={item.id}
                          variant="ghost"
                          onClick={() => onNavigate(item.id)}
                          className={`w-full justify-start p-2 h-auto text-xs hover:bg-trading-accent/30 ${
                            isActive ? 'bg-trading-accent text-trading-primary' : ''
                          }`}
                        >
                          <ItemIcon className="h-3 w-3 mr-2 text-trading-muted flex-shrink-0" />
                          <span className="text-trading-light flex-1 text-left truncate hidden lg:block">
                            {item.label}
                          </span>
                          {item.badge && (
                            <Badge variant="secondary" className="text-xs px-1 py-0 h-4 hidden lg:block">
                              {item.badge}
                            </Badge>
                          )}
                        </Button>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-2 border-t border-trading-border">
        <div className="text-center">
          <div className="text-xs text-trading-muted hidden lg:block">Version 2.1.0</div>
          <div className="text-xs text-green-400">● Online</div>
        </div>
      </div>
    </div>
  );
};
