
import { TechnicalIndicatorService, RS<PERSON><PERSON>ult, <PERSON><PERSON><PERSON>ult, BollingerBandsResult } from './TechnicalIndicatorService';
import { calculationEngine } from './CalculationEngineService';

export interface StrategySignal {
  timestamp: number;
  signal: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  price: number;
  reason: string;
  stopLoss?: number;
  takeProfit?: number;
}

export interface StrategyConfig {
  name: string;
  parameters: Record<string, any>;
  riskManagement: {
    stopLoss: number;
    takeProfit: number;
    maxPositionSize: number;
  };
}

export interface BacktestTrade {
  id: string;
  symbol: string;
  entryTime: number;
  exitTime?: number;
  entryPrice: number;
  exitPrice?: number;
  quantity: number;
  side: 'BUY' | 'SELL';
  pnl?: number;
  reason: string;
}

export interface BacktestResult {
  totalReturn: number;
  totalReturnPercent: number;
  maxDrawdown: number;
  sharpeRatio: number;
  totalTrades: number;
  winRate: number;
  avgWin: number;
  avgLoss: number;
  profitFactor: number;
  startDate: string;
  endDate: string;
  initialCapital: number;
  finalCapital: number;
  trades: BacktestTrade[];
}

export class StrategyService {
  
  // EMA Crossover Strategy
  static async emaCrossoverStrategy(
    prices: number[], 
    timestamps: number[],
    config: { fastPeriod: number, slowPeriod: number }
  ): Promise<StrategySignal[]> {
    const { fastPeriod = 12, slowPeriod = 26 } = config;
    
    const fastEMA = TechnicalIndicatorService.calculateEMA(prices, fastPeriod);
    const slowEMA = TechnicalIndicatorService.calculateEMA(prices, slowPeriod);
    
    const signals: StrategySignal[] = [];
    
    for (let i = 1; i < Math.min(fastEMA.length, slowEMA.length); i++) {
      const prevFast = fastEMA[i - 1];
      const prevSlow = slowEMA[i - 1];
      const currentFast = fastEMA[i];
      const currentSlow = slowEMA[i];
      
      let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
      let reason = '';
      let confidence = 0;
      
      // Bullish crossover
      if (prevFast <= prevSlow && currentFast > currentSlow) {
        signal = 'BUY';
        reason = `EMA(${fastPeriod}) crossed above EMA(${slowPeriod})`;
        confidence = Math.min(0.8, (currentFast - currentSlow) / currentSlow);
      }
      // Bearish crossover
      else if (prevFast >= prevSlow && currentFast < currentSlow) {
        signal = 'SELL';
        reason = `EMA(${fastPeriod}) crossed below EMA(${slowPeriod})`;
        confidence = Math.min(0.8, (currentSlow - currentFast) / currentSlow);
      }
      
      if (signal !== 'HOLD') {
        const priceIndex = i + slowPeriod - 1;
        signals.push({
          timestamp: timestamps[priceIndex] || Date.now(),
          signal,
          confidence,
          price: prices[priceIndex],
          reason,
          stopLoss: signal === 'BUY' ? prices[priceIndex] * 0.95 : prices[priceIndex] * 1.05,
          takeProfit: signal === 'BUY' ? prices[priceIndex] * 1.1 : prices[priceIndex] * 0.9
        });
      }
    }
    
    return signals;
  }

  // RSI Mean Reversion Strategy
  static async rsiMeanReversionStrategy(
    prices: number[], 
    timestamps: number[],
    config: { period: number, oversoldLevel: number, overboughtLevel: number }
  ): Promise<StrategySignal[]> {
    const { period = 14, oversoldLevel = 30, overboughtLevel = 70 } = config;
    
    const rsiResults = TechnicalIndicatorService.calculateRSI(prices, period);
    const signals: StrategySignal[] = [];
    
    rsiResults.forEach((rsi, index) => {
      let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
      let reason = '';
      let confidence = 0;
      
      if (rsi.value < oversoldLevel) {
        signal = 'BUY';
        reason = `RSI oversold at ${rsi.value.toFixed(2)}`;
        confidence = (oversoldLevel - rsi.value) / oversoldLevel;
      } else if (rsi.value > overboughtLevel) {
        signal = 'SELL';
        reason = `RSI overbought at ${rsi.value.toFixed(2)}`;
        confidence = (rsi.value - overboughtLevel) / (100 - overboughtLevel);
      }
      
      if (signal !== 'HOLD') {
        const priceIndex = index + period;
        signals.push({
          timestamp: rsi.timestamp,
          signal,
          confidence,
          price: prices[priceIndex],
          reason,
          stopLoss: signal === 'BUY' ? prices[priceIndex] * 0.97 : prices[priceIndex] * 1.03,
          takeProfit: signal === 'BUY' ? prices[priceIndex] * 1.06 : prices[priceIndex] * 0.94
        });
      }
    });
    
    return signals;
  }

  // MACD Strategy
  static async macdStrategy(
    prices: number[], 
    timestamps: number[],
    config: { fastPeriod: number, slowPeriod: number, signalPeriod: number }
  ): Promise<StrategySignal[]> {
    const { fastPeriod = 12, slowPeriod = 26, signalPeriod = 9 } = config;
    
    const macdResults = TechnicalIndicatorService.calculateMACD(prices, fastPeriod, slowPeriod, signalPeriod);
    const signals: StrategySignal[] = [];
    
    macdResults.forEach((macd, index) => {
      if (macd.crossover) {
        const signal = macd.crossover === 'bullish' ? 'BUY' : 'SELL';
        const confidence = Math.min(0.9, Math.abs(macd.histogram) / Math.abs(macd.macd));
        
        const priceIndex = index + slowPeriod + signalPeriod - 2;
        signals.push({
          timestamp: macd.timestamp,
          signal,
          confidence,
          price: prices[priceIndex],
          reason: `MACD ${macd.crossover} crossover`,
          stopLoss: signal === 'BUY' ? prices[priceIndex] * 0.96 : prices[priceIndex] * 1.04,
          takeProfit: signal === 'BUY' ? prices[priceIndex] * 1.08 : prices[priceIndex] * 0.92
        });
      }
    });
    
    return signals;
  }

  // Bollinger Bands Strategy
  static async bollingerBandsStrategy(
    prices: number[], 
    timestamps: number[],
    config: { period: number, stdDev: number }
  ): Promise<StrategySignal[]> {
    const { period = 20, stdDev = 2 } = config;
    
    const bbResults = TechnicalIndicatorService.calculateBollingerBands(prices, period, stdDev);
    const signals: StrategySignal[] = [];
    
    bbResults.forEach((bb, index) => {
      const currentPrice = prices[index + period - 1];
      let signal: 'BUY' | 'SELL' | 'HOLD' = 'HOLD';
      let reason = '';
      let confidence = 0;
      
      // Price touches lower band - potential buy
      if (currentPrice <= bb.lower) {
        signal = 'BUY';
        reason = 'Price at lower Bollinger Band';
        confidence = (bb.lower - currentPrice) / (bb.middle - bb.lower);
      }
      // Price touches upper band - potential sell
      else if (currentPrice >= bb.upper) {
        signal = 'SELL';
        reason = 'Price at upper Bollinger Band';
        confidence = (currentPrice - bb.upper) / (bb.upper - bb.middle);
      }
      
      if (signal !== 'HOLD') {
        signals.push({
          timestamp: bb.timestamp,
          signal,
          confidence: Math.min(0.8, confidence),
          price: currentPrice,
          reason,
          stopLoss: signal === 'BUY' ? bb.lower * 0.99 : bb.upper * 1.01,
          takeProfit: signal === 'BUY' ? bb.middle : bb.middle
        });
      }
    });
    
    return signals;
  }

  // Run Backtest
  static async runBacktest(
    strategyName: string,
    prices: number[],
    timestamps: number[],
    strategyConfig: any,
    initialCapital: number = 100000
  ): Promise<BacktestResult> {
    let signals: StrategySignal[] = [];
    
    // Get strategy signals
    switch (strategyName) {
      case 'ema-crossover':
        signals = await this.emaCrossoverStrategy(prices, timestamps, strategyConfig);
        break;
      case 'rsi-mean-reversion':
        signals = await this.rsiMeanReversionStrategy(prices, timestamps, strategyConfig);
        break;
      case 'macd-signal':
        signals = await this.macdStrategy(prices, timestamps, strategyConfig);
        break;
      case 'bollinger-bands':
        signals = await this.bollingerBandsStrategy(prices, timestamps, strategyConfig);
        break;
      default:
        throw new Error(`Unknown strategy: ${strategyName}`);
    }
    
    // Simulate trades
    const trades: BacktestTrade[] = [];
    let currentCapital = initialCapital;
    let currentPosition: BacktestTrade | null = null;
    let tradeId = 1;
    
    for (const signal of signals) {
      if (signal.signal === 'BUY' && !currentPosition) {
        // Open long position
        const quantity = Math.floor(currentCapital * 0.95 / signal.price);
        currentPosition = {
          id: `trade_${tradeId++}`,
          symbol: 'TEST',
          entryTime: signal.timestamp,
          entryPrice: signal.price,
          quantity,
          side: 'BUY',
          reason: signal.reason
        };
      } else if (signal.signal === 'SELL' && currentPosition && currentPosition.side === 'BUY') {
        // Close long position
        currentPosition.exitTime = signal.timestamp;
        currentPosition.exitPrice = signal.price;
        currentPosition.pnl = (signal.price - currentPosition.entryPrice) * currentPosition.quantity;
        currentCapital += currentPosition.pnl;
        trades.push(currentPosition);
        currentPosition = null;
      }
    }
    
    // Calculate metrics
    const winningTrades = trades.filter(t => (t.pnl || 0) > 0);
    const losingTrades = trades.filter(t => (t.pnl || 0) < 0);
    
    const totalReturn = currentCapital - initialCapital;
    const totalReturnPercent = (totalReturn / initialCapital) * 100;
    const winRate = trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + (t.pnl || 0), 0) / winningTrades.length
      : 0;
    const avgLoss = losingTrades.length > 0 
      ? Math.abs(losingTrades.reduce((sum, t) => sum + (t.pnl || 0), 0) / losingTrades.length)
      : 0;
    
    return {
      totalReturn,
      totalReturnPercent,
      maxDrawdown: 0, // Would need equity curve calculation
      sharpeRatio: 0, // Would need returns calculation
      totalTrades: trades.length,
      winRate,
      avgWin: (avgWin / initialCapital) * 100,
      avgLoss: (avgLoss / initialCapital) * 100,
      profitFactor: avgLoss > 0 ? avgWin / avgLoss : 0,
      startDate: new Date(timestamps[0]).toISOString().split('T')[0],
      endDate: new Date(timestamps[timestamps.length - 1]).toISOString().split('T')[0],
      initialCapital,
      finalCapital: currentCapital,
      trades
    };
  }

  // Get available strategies
  static getAvailableStrategies(): StrategyConfig[] {
    return [
      {
        name: 'ema-crossover',
        parameters: { fastPeriod: 12, slowPeriod: 26 },
        riskManagement: { stopLoss: 5, takeProfit: 10, maxPositionSize: 95 }
      },
      {
        name: 'rsi-mean-reversion',
        parameters: { period: 14, oversoldLevel: 30, overboughtLevel: 70 },
        riskManagement: { stopLoss: 3, takeProfit: 6, maxPositionSize: 95 }
      },
      {
        name: 'macd-signal',
        parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
        riskManagement: { stopLoss: 4, takeProfit: 8, maxPositionSize: 95 }
      },
      {
        name: 'bollinger-bands',
        parameters: { period: 20, stdDev: 2 },
        riskManagement: { stopLoss: 3, takeProfit: 6, maxPositionSize: 95 }
      }
    ];
  }
}
