
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  BarChart3, 
  TrendingUp, 
  Activity, 
  Target,
  Brain,
  Zap,
  Volume2,
  DollarSign,
  RotateCcw,
  ArrowLeft
} from "lucide-react";

// Import all analytics components
import { VWAPAnalysis } from "./VWAPAnalysis";
import { VolumeProfile } from "./VolumeProfile";
import { DOMHeatmap } from "./DOMHeatmap";
import { OrderFlowAnalytics } from "./OrderFlowAnalytics";
import { SectorRotationTracker } from "./SectorRotationTracker";
import { StockAnalysis360 } from "./StockAnalysis360";
import { BreakoutRetestDetector } from "./BreakoutRetestDetector";
import { AccumulationTracker } from "./AccumulationTracker";
import { SentimentEngine } from "./SentimentEngine";
import { OptionChainAnalyzer } from "./OptionChainAnalyzer";
import { RiskRewardCalculator } from "./RiskRewardCalculator";

type AnalyticsTool = 
  | 'overview' 
  | 'vwap' 
  | 'volume-profile' 
  | 'dom-heatmap' 
  | 'order-flow' 
  | 'sector-rotation'
  | 'stock-analysis-360'
  | 'breakout-retest'
  | 'accumulation-tracker'
  | 'sentiment-engine'
  | 'option-chain'
  | 'risk-reward';

export const AnalyticsToolsDashboard = () => {
  const [currentView, setCurrentView] = useState<AnalyticsTool>('overview');

  const analyticsTools = [
    {
      id: 'vwap' as const,
      title: 'VWAP Analysis',
      description: 'Volume Weighted Average Price analysis with real-time calculations',
      icon: <TrendingUp className="h-6 w-6" />,
      category: 'Price Analysis'
    },
    {
      id: 'volume-profile' as const,
      title: 'Volume Profile',
      description: 'Price-volume distribution analysis and value area calculations',
      icon: <BarChart3 className="h-6 w-6" />,
      category: 'Volume Analysis'
    },
    {
      id: 'dom-heatmap' as const,
      title: 'DOM Heatmap',
      description: 'Depth of Market visualization with liquidity analysis',
      icon: <Activity className="h-6 w-6" />,
      category: 'Order Book'
    },
    {
      id: 'order-flow' as const,
      title: 'Order Flow Analytics',
      description: 'Real-time order flow analysis and pattern detection',
      icon: <Volume2 className="h-6 w-6" />,
      category: 'Order Flow'
    },
    {
      id: 'sector-rotation' as const,
      title: 'Sector Rotation',
      description: 'Economic cycle-based sector analysis and rotation tracking',
      icon: <RotateCcw className="h-6 w-6" />,
      category: 'Market Analysis'
    },
    {
      id: 'stock-analysis-360' as const,
      title: '360° Stock Analysis',
      description: 'Complete technical, fundamental & sentiment analysis',
      icon: <Target className="h-6 w-6" />,
      category: 'AI Analysis'
    },
    {
      id: 'breakout-retest' as const,
      title: 'Breakout Retest Detector',
      description: 'Pattern recognition for breakout and retest scenarios',
      icon: <TrendingUp className="h-6 w-6" />,
      category: 'AI Analysis'
    },
    {
      id: 'accumulation-tracker' as const,
      title: 'Accumulation Tracker',
      description: 'Smart money detection and accumulation analysis',
      icon: <DollarSign className="h-6 w-6" />,
      category: 'AI Analysis'
    },
    {
      id: 'sentiment-engine' as const,
      title: 'Sentiment Engine',
      description: 'Multi-source NLP sentiment analysis',
      icon: <Brain className="h-6 w-6" />,
      category: 'AI Analysis'
    },
    {
      id: 'option-chain' as const,
      title: 'Option Chain Analyzer',
      description: 'Advanced Greeks calculations and option analysis',
      icon: <Activity className="h-6 w-6" />,
      category: 'Options'
    },
    {
      id: 'risk-reward' as const,
      title: 'Risk-Reward Calculator',
      description: 'AI-optimized position sizing and risk management',
      icon: <Zap className="h-6 w-6" />,
      category: 'Risk Management'
    }
  ];

  const handleToolSelect = (toolId: AnalyticsTool) => {
    setCurrentView(toolId);
  };

  const handleBackToOverview = () => {
    setCurrentView('overview');
  };

  // Render specific tool component
  const renderToolComponent = () => {
    switch (currentView) {
      case 'vwap':
        return <VWAPAnalysis />;
      case 'volume-profile':
        return <VolumeProfile />;
      case 'dom-heatmap':
        return <DOMHeatmap />;
      case 'order-flow':
        return <OrderFlowAnalytics />;
      case 'sector-rotation':
        return <SectorRotationTracker />;
      case 'stock-analysis-360':
        return <StockAnalysis360 onBack={handleBackToOverview} />;
      case 'breakout-retest':
        return <BreakoutRetestDetector onBack={handleBackToOverview} />;
      case 'accumulation-tracker':
        return <AccumulationTracker onBack={handleBackToOverview} />;
      case 'sentiment-engine':
        return <SentimentEngine onBack={handleBackToOverview} />;
      case 'option-chain':
        return <OptionChainAnalyzer onBack={handleBackToOverview} />;
      case 'risk-reward':
        return <RiskRewardCalculator onBack={handleBackToOverview} />;
      default:
        return null;
    }
  };

  // If not in overview, render the specific tool with scroll area
  if (currentView !== 'overview') {
    return (
      <div className="h-full">
        <div className="flex items-center space-x-4 p-4 border-b border-trading-border">
          <Button variant="ghost" onClick={handleBackToOverview}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Analytics Tools
          </Button>
        </div>
        <ScrollArea className="h-[calc(100vh-8rem)] w-full">
          <div className="p-6">
            {renderToolComponent()}
          </div>
        </ScrollArea>
      </div>
    );
  }

  // Overview with tabs and scroll areas
  return (
    <ScrollArea className="h-[calc(100vh-8rem)] w-full">
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-trading-light">Advanced Analytics Tools</h1>
            <p className="text-trading-muted">Professional-grade market analysis and AI-powered trading tools</p>
          </div>
        </div>

        <Tabs defaultValue="tools" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
            <TabsTrigger value="tools">Analysis Tools</TabsTrigger>
            <TabsTrigger value="ai-tools">AI-Powered Tools</TabsTrigger>
            <TabsTrigger value="patterns">Pattern Recognition</TabsTrigger>
          </TabsList>

          <TabsContent value="tools" className="space-y-6">
            <ScrollArea className="h-[calc(100vh-16rem)] w-full">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-1">
                {analyticsTools.filter(tool => ['Price Analysis', 'Volume Analysis', 'Order Book', 'Order Flow', 'Market Analysis'].includes(tool.category)).map((tool) => (
                  <Card 
                    key={tool.id}
                    className="glassmorphism-card hover:border-blue-500/50 transition-all duration-200 cursor-pointer"
                    onClick={() => handleToolSelect(tool.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-600/20 rounded-lg">
                          {tool.icon}
                        </div>
                        <div>
                          <CardTitle className="text-trading-light text-lg">
                            {tool.title}
                          </CardTitle>
                          <div className="text-xs text-blue-400 font-medium">
                            {tool.category}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-trading-muted text-sm mb-4">
                        {tool.description}
                      </p>
                      <Button 
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToolSelect(tool.id);
                        }}
                      >
                        Launch Tool
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="ai-tools" className="space-y-6">
            <ScrollArea className="h-[calc(100vh-16rem)] w-full">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-1">
                {analyticsTools.filter(tool => ['AI Analysis', 'Options', 'Risk Management'].includes(tool.category)).map((tool) => (
                  <Card 
                    key={tool.id}
                    className="glassmorphism-card hover:border-purple-500/50 transition-all duration-200 cursor-pointer"
                    onClick={() => handleToolSelect(tool.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-600/20 rounded-lg">
                          {tool.icon}
                        </div>
                        <div>
                          <CardTitle className="text-trading-light text-lg">
                            {tool.title}
                          </CardTitle>
                          <div className="text-xs text-purple-400 font-medium">
                            {tool.category}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-trading-muted text-sm mb-4">
                        {tool.description}
                      </p>
                      <Button 
                        className="w-full bg-purple-600 hover:bg-purple-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToolSelect(tool.id);
                        }}
                      >
                        Launch AI Tool
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="patterns" className="space-y-4">
            <ScrollArea className="h-[calc(100vh-16rem)] w-full">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-1">
                <Card className="glassmorphism-card">
                  <CardHeader>
                    <CardTitle className="text-trading-light flex items-center">
                      <Brain className="h-5 w-5 mr-2" />
                      Chart Patterns
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-trading-muted text-sm mb-3">
                      Automated detection of classical chart patterns
                    </p>
                    <div className="text-xs text-trading-muted">
                      • Head & Shoulders • Triangles • Flags & Pennants
                    </div>
                  </CardContent>
                </Card>

                <Card className="glassmorphism-card">
                  <CardHeader>
                    <CardTitle className="text-trading-light flex items-center">
                      <Zap className="h-5 w-5 mr-2" />
                      Candlestick Patterns
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-trading-muted text-sm mb-3">
                      Japanese candlestick pattern recognition
                    </p>
                    <div className="text-xs text-trading-muted">
                      • Doji • Hammer • Engulfing • Morning/Evening Star
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
