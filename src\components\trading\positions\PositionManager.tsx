
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, DollarSign, Target, Shield } from "lucide-react";
import { useState } from "react";

interface Position {
  id: string;
  symbol: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercent: number;
  type: 'LONG' | 'SHORT';
  marketValue: number;
  dayChange: number;
  dayChangePercent: number;
}

const mockPositions: Position[] = [
  {
    id: '1',
    symbol: 'RELIANCE',
    quantity: 100,
    avgPrice: 2420,
    currentPrice: 2465,
    pnl: 4500,
    pnlPercent: 1.86,
    type: 'LONG',
    marketValue: 246500,
    dayChange: 1200,
    dayChangePercent: 0.49
  },
  {
    id: '2',
    symbol: 'TCS',
    quantity: 50,
    avgPrice: 3850,
    currentPrice: 3820,
    pnl: -1500,
    pnlPercent: -0.78,
    type: 'LONG',
    marketValue: 191000,
    dayChange: -800,
    dayChangePercent: -0.21
  }
];

export const PositionManager = () => {
  const [positions, setPositions] = useState<Position[]>(mockPositions);

  const totalPnL = positions.reduce((sum, pos) => sum + pos.pnl, 0);
  const totalMarketValue = positions.reduce((sum, pos) => sum + pos.marketValue, 0);
  const totalDayChange = positions.reduce((sum, pos) => sum + pos.dayChange, 0);

  const closePosition = (positionId: string) => {
    setPositions(prev => prev.filter(pos => pos.id !== positionId));
  };

  return (
    <div className="space-y-4">
      {/* Portfolio Summary */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Portfolio Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-trading-light">
                ₹{totalMarketValue.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Market Value</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${totalPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₹{totalPnL.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Total P&L</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${totalDayChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₹{totalDayChange.toLocaleString()}
              </div>
              <div className="text-sm text-trading-muted">Day Change</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{positions.length}</div>
              <div className="text-sm text-trading-muted">Positions</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Positions List */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light">Open Positions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {positions.map((position) => (
              <div key={position.id} className="p-4 bg-trading-dark rounded border border-trading-border">
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                  {/* Symbol & Type */}
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-trading-light text-lg">
                      {position.symbol}
                    </span>
                    <Badge variant="outline" className={position.type === 'LONG' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                      {position.type}
                    </Badge>
                  </div>

                  {/* Quantity & Avg Price */}
                  <div className="text-center">
                    <div className="text-sm text-trading-muted">Quantity</div>
                    <div className="text-trading-light font-medium">{position.quantity}</div>
                    <div className="text-xs text-trading-muted">Avg: ₹{position.avgPrice}</div>
                  </div>

                  {/* Current Price */}
                  <div className="text-center">
                    <div className="text-sm text-trading-muted">Current Price</div>
                    <div className="text-trading-light font-medium">₹{position.currentPrice}</div>
                    <div className={`text-xs flex items-center justify-center ${position.dayChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {position.dayChangePercent >= 0 ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
                      {position.dayChangePercent.toFixed(2)}%
                    </div>
                  </div>

                  {/* P&L */}
                  <div className="text-center">
                    <div className="text-sm text-trading-muted">P&L</div>
                    <div className={`font-medium ${position.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ₹{position.pnl.toLocaleString()}
                    </div>
                    <div className={`text-xs ${position.pnlPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      ({position.pnlPercent > 0 ? '+' : ''}{position.pnlPercent.toFixed(2)}%)
                    </div>
                  </div>

                  {/* Market Value */}
                  <div className="text-center">
                    <div className="text-sm text-trading-muted">Market Value</div>
                    <div className="text-trading-light font-medium">
                      ₹{position.marketValue.toLocaleString()}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col space-y-2">
                    <Button size="sm" variant="outline" className="text-xs">
                      <Target className="h-3 w-3 mr-1" />
                      Set Target
                    </Button>
                    <Button size="sm" variant="outline" className="text-xs">
                      <Shield className="h-3 w-3 mr-1" />
                      Stop Loss
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      className="text-xs"
                      onClick={() => closePosition(position.id)}
                    >
                      Close Position
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {positions.length === 0 && (
              <div className="text-center py-8 text-trading-muted">
                <DollarSign className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No open positions</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
