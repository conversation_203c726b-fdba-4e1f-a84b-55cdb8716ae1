# Trading Platform Environment Configuration

# Market Data API Configuration
VITE_MARKET_DATA_API_KEY=your_market_data_api_key_here
VITE_MARKET_DATA_BASE_URL=https://api.marketdata.com
VITE_MARKET_DATA_WS_URL=wss://ws.marketdata.com

# Broker API Configuration
VITE_ZERODHA_API_KEY=your_zerodha_api_key_here
VITE_ZERODHA_API_SECRET=your_zerodha_api_secret_here
VITE_DHAN_CLIENT_ID=your_dhan_client_id_here
VITE_DHAN_ACCESS_TOKEN=your_dhan_access_token_here

# AI/ML Service Configuration
VITE_AI_SERVICE_URL=https://api.aiservice.com
VITE_AI_API_KEY=your_ai_api_key_here

# Database Configuration
VITE_DATABASE_URL=postgresql://username:password@localhost:5432/trading_db
VITE_REDIS_URL=redis://localhost:6379

# WebSocket Configuration
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_MAX_RETRIES=10

# Risk Management Configuration
VITE_MAX_POSITION_SIZE=100000
VITE_MAX_DAILY_LOSS=50000
VITE_RISK_FREE_RATE=0.06

# Feature Flags
VITE_ENABLE_REAL_TRADING=false
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_RISK_MONITORING=true

# Logging Configuration
VITE_LOG_LEVEL=info
VITE_ENABLE_PERFORMANCE_MONITORING=true
