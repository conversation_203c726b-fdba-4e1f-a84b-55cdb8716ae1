
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { TrendingUp, Activity, Target, AlertCircle } from "lucide-react";

interface VWAPData {
  timestamp: number;
  price: number;
  volume: number;
  vwap: number;
  anchored_vwap?: number;
  typical_price: number;
  cumulative_volume: number;
  cumulative_typical_price_volume: number;
}

interface VWAPAnalysisProps {
  symbol?: string;
  timeframe?: string;
  priceData?: Array<{ price: number; volume: number; timestamp: number; high?: number; low?: number; close?: number }>;
}

export const VWAPAnalysis = ({ 
  symbol = "NIFTY", 
  timeframe = "5m",
  priceData = []
}: VWAPAnalysisProps) => {
  const [vwapData, setVwapData] = useState<VWAPData[]>([]);
  const [anchorPoint, setAnchorPoint] = useState<number | null>(null);
  const [vwapSignals, setVwapSignals] = useState<Array<{ type: 'BUY' | 'SELL', price: number, confidence: number }>>([]);

  // Real VWAP calculation using Typical Price = (High + Low + Close) / 3
  const calculateVWAP = (priceData: Array<{ price: number; volume: number; timestamp: number; high?: number; low?: number; close?: number }>) => {
    if (priceData.length === 0) return [];

    let cumulativeVolume = 0;
    let cumulativeTypicalPriceVolume = 0;
    
    return priceData.map((item, index) => {
      // Use typical price formula: (High + Low + Close) / 3
      // If OHLC data not available, use price as approximation
      const typicalPrice = item.high && item.low && item.close 
        ? (item.high + item.low + item.close) / 3 
        : item.price;
      
      const priceVolume = typicalPrice * item.volume;
      
      cumulativeVolume += item.volume;
      cumulativeTypicalPriceVolume += priceVolume;
      
      const vwap = cumulativeVolume > 0 ? cumulativeTypicalPriceVolume / cumulativeVolume : 0;
      
      return {
        timestamp: item.timestamp,
        price: item.price,
        volume: item.volume,
        vwap: vwap,
        typical_price: typicalPrice,
        cumulative_volume: cumulativeVolume,
        cumulative_typical_price_volume: cumulativeTypicalPriceVolume
      };
    });
  };

  // Anchored VWAP calculation from specific anchor point
  const calculateAnchoredVWAP = (priceData: VWAPData[], anchorIndex: number) => {
    if (anchorIndex >= priceData.length || anchorIndex < 0) return priceData;
    
    let cumulativeVolume = 0;
    let cumulativeTypicalPriceVolume = 0;
    
    return priceData.map((item, index) => {
      if (index >= anchorIndex) {
        cumulativeVolume += item.volume;
        cumulativeTypicalPriceVolume += item.typical_price * item.volume;
        
        return {
          ...item,
          anchored_vwap: cumulativeVolume > 0 ? cumulativeTypicalPriceVolume / cumulativeVolume : 0
        };
      }
      return item;
    });
  };

  // Real VWAP trading signals based on price-VWAP relationships
  const generateVWAPSignals = (data: VWAPData[]) => {
    if (data.length < 2) return [];
    
    const signals: Array<{ type: 'BUY' | 'SELL', price: number, confidence: number, reason: string }> = [];
    
    for (let i = 1; i < data.length; i++) {
      const current = data[i];
      const previous = data[i - 1];
      
      if (!current.vwap || !previous.vwap) continue;
      
      // Calculate volume-weighted deviation
      const currentDeviation = Math.abs(current.price - current.vwap) / current.vwap;
      const volumeWeight = Math.min(current.volume / 10000, 1); // Normalize volume weight
      
      // Price crossing above VWAP with volume confirmation
      if (previous.price <= previous.vwap && current.price > current.vwap) {
        const confidence = Math.min(95, 60 + (currentDeviation * 1000 * volumeWeight));
        signals.push({
          type: 'BUY',
          price: current.price,
          confidence: Math.max(60, confidence),
          reason: 'Price crossed above VWAP with volume confirmation'
        });
      }
      
      // Price crossing below VWAP with volume confirmation
      if (previous.price >= previous.vwap && current.price < current.vwap) {
        const confidence = Math.min(95, 60 + (currentDeviation * 1000 * volumeWeight));
        signals.push({
          type: 'SELL',
          price: current.price,
          confidence: Math.max(60, confidence),
          reason: 'Price crossed below VWAP with volume confirmation'
        });
      }
    }
    
    return signals.slice(-5); // Return last 5 signals
  };

  // Standard deviation bands around VWAP
  const calculateVWAPBands = (data: VWAPData[], periods: number = 20, multiplier: number = 2) => {
    if (data.length < periods) return data;
    
    return data.map((item, index) => {
      if (index < periods - 1) return item;
      
      const recentData = data.slice(index - periods + 1, index + 1);
      const deviations = recentData.map(d => Math.pow(d.price - d.vwap, 2));
      const variance = deviations.reduce((sum, dev) => sum + dev, 0) / periods;
      const stdDev = Math.sqrt(variance);
      
      return {
        ...item,
        upper_band: item.vwap + (stdDev * multiplier),
        lower_band: item.vwap - (stdDev * multiplier)
      };
    });
  };

  useEffect(() => {
    if (priceData && priceData.length > 0) {
      const calculatedVWAP = calculateVWAP(priceData);
      setVwapData(calculatedVWAP);
      
      const signals = generateVWAPSignals(calculatedVWAP);
      setVwapSignals(signals);
    }
  }, [priceData, symbol, timeframe]);

  const currentPrice = vwapData.length > 0 ? vwapData[vwapData.length - 1].price : 0;
  const currentVWAP = vwapData.length > 0 ? vwapData[vwapData.length - 1].vwap : 0;
  const priceVsVWAP = currentPrice > currentVWAP ? 'ABOVE' : 'BELOW';
  const deviation = currentVWAP > 0 ? ((currentPrice - currentVWAP) / currentVWAP * 100) : 0;

  return (
    <div className="space-y-4">
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-trading-light flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            VWAP Analysis - {symbol}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* VWAP Metrics */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Current Price</div>
              <div className="text-lg font-bold text-trading-light">
                {currentPrice > 0 ? `₹${currentPrice.toFixed(2)}` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">VWAP</div>
              <div className="text-lg font-bold text-blue-400">
                {currentVWAP > 0 ? `₹${currentVWAP.toFixed(2)}` : '--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Position</div>
              <Badge variant="outline" className={priceVsVWAP === 'ABOVE' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}>
                {currentPrice > 0 ? priceVsVWAP : '--'}
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Deviation</div>
              <div className={`text-lg font-bold ${deviation > 0 ? 'text-green-400' : 'text-red-400'}`}>
                {currentPrice > 0 ? `${deviation > 0 ? '+' : ''}${deviation.toFixed(3)}%` : '--'}
              </div>
            </div>
          </div>

          {/* VWAP Chart */}
          <div className="h-64">
            {vwapData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={vwapData.slice(-50)}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    dataKey="timestamp" 
                    stroke="#9ca3af"
                    fontSize={12}
                    tickFormatter={(value) => new Date(value).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  />
                  <YAxis stroke="#9ca3af" fontSize={12} domain={['dataMin - 50', 'dataMax + 50']} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1f2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="price" 
                    stroke="#ffffff" 
                    strokeWidth={2} 
                    dot={false}
                    name="Price"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="vwap" 
                    stroke="#3b82f6" 
                    strokeWidth={2} 
                    dot={false}
                    strokeDasharray="5 5"
                    name="VWAP"
                  />
                  {anchorPoint && (
                    <Line 
                      type="monotone" 
                      dataKey="anchored_vwap" 
                      stroke="#10b981" 
                      strokeWidth={2} 
                      dot={false}
                      strokeDasharray="3 3"
                      name="Anchored VWAP"
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-trading-muted">
                <div className="text-center">
                  <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No price data available</p>
                  <p className="text-xs mt-1">Connect to data feed to see VWAP analysis</p>
                </div>
              </div>
            )}
          </div>

          {/* VWAP Controls */}
          <div className="flex gap-2 flex-wrap">
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => {
                if (vwapData.length > 0) {
                  const anchorIndex = Math.floor(vwapData.length * 0.8);
                  setAnchorPoint(anchorIndex);
                  setVwapData(calculateAnchoredVWAP(vwapData, anchorIndex));
                }
              }}
              disabled={vwapData.length === 0}
            >
              <Target className="h-4 w-4 mr-1" />
              Anchor VWAP
            </Button>
            <Button size="sm" variant="outline" disabled={vwapData.length === 0}>
              <TrendingUp className="h-4 w-4 mr-1" />
              VWAP Bands
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* VWAP Signals */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-trading-light text-base">VWAP Trading Signals</CardTitle>
        </CardHeader>
        <CardContent>
          {vwapSignals.length > 0 ? (
            <div className="space-y-2">
              {vwapSignals.map((signal, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant="outline" 
                      className={signal.type === 'BUY' ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'}
                    >
                      {signal.type}
                    </Badge>
                    <span className="text-sm text-trading-light">₹{signal.price.toFixed(2)}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-trading-muted">Confidence</div>
                    <div className="text-sm font-medium text-blue-400">{signal.confidence.toFixed(1)}%</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-trading-muted">
              <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No VWAP signals available</p>
              <p className="text-xs mt-1">Connect to data feed to generate signals</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
