
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Activity, Bell, Settings } from "lucide-react";

export const Header = () => {
  return (
    <header className="h-16 bg-trading-darker border-b border-trading-border flex items-center justify-between px-6">
      {/* Left Section - Logo & Navigation */}
      <div className="flex items-center space-x-6">
        <div className="text-xl font-bold text-trading-primary">
          SentientTrader
        </div>
        <nav className="flex space-x-4">
          <Button variant="ghost" size="sm" className="text-trading-light hover:text-trading-primary">
            Dashboard
          </Button>
          <Button variant="ghost" size="sm" className="text-trading-light hover:text-trading-primary">
            Strategies
          </Button>
          <Button variant="ghost" size="sm" className="text-trading-light hover:text-trading-primary">
            Analytics
          </Button>
          <Button variant="ghost" size="sm" className="text-trading-light hover:text-trading-primary">
            Portfolio
          </Button>
        </nav>
      </div>

      {/* Center Section - Market Status */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-trading-light">Market Open</span>
        </div>
        <Badge variant="outline" className="text-green-400 border-green-400">
          NIFTY: 24,157.81 (+0.68%)
        </Badge>
        <Badge variant="outline" className="text-blue-400 border-blue-400">
          SENSEX: 79,243.18 (+0.45%)
        </Badge>
      </div>

      {/* Right Section - Controls & Status */}
      <div className="flex items-center space-x-4">
        <div className="text-sm text-trading-light">
          P&L: <span className="text-green-400 font-semibold">₹1,24,567</span>
        </div>
        <Button variant="ghost" size="sm">
          <Bell className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Activity className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Settings className="h-4 w-4" />
        </Button>
        <Button 
          variant="destructive" 
          size="sm"
          className="bg-red-600 hover:bg-red-700"
        >
          Emergency Stop
        </Button>
      </div>
    </header>
  );
};
