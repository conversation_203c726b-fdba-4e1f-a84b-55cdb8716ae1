
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { TrendingUp, Settings, BarChart3, Activity } from "lucide-react";
import { useState } from "react";

interface SwingPerformance {
  confidence: number;
  activePositions: number;
  profit: number;
  successRate: number;
}

interface SwingAgentProps {
  isActive?: boolean;
  onToggle?: (active: boolean) => void;
  performance?: SwingPerformance;
  onHoldingPeriodChange?: (period: number) => void;
  onPositionSizeChange?: (size: number) => void;
}

export const SwingAgent = ({
  isActive: propIsActive,
  onToggle,
  performance,
  onHoldingPeriodChange,
  onPositionSizeChange
}: SwingAgentProps) => {
  const [localIsActive, setLocalIsActive] = useState(true);
  const [holdingPeriod, setHoldingPeriod] = useState([7]);
  const [positionSize, setPositionSize] = useState([25]);

  const isActive = propIsActive !== undefined ? propIsActive : localIsActive;

  const handleToggle = (active: boolean) => {
    if (onToggle) {
      onToggle(active);
    } else {
      setLocalIsActive(active);
    }
  };

  const handleHoldingPeriodChange = (value: number[]) => {
    setHoldingPeriod(value);
    if (onHoldingPeriodChange) {
      onHoldingPeriodChange(value[0]);
    }
  };

  const handlePositionSizeChange = (value: number[]) => {
    setPositionSize(value);
    if (onPositionSizeChange) {
      onPositionSizeChange(value[0]);
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            Swing Trading Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Confidence</div>
            <div className="text-2xl font-bold text-blue-400">
              {performance ? `${performance.confidence}%` : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Active Positions</div>
            <div className="text-2xl font-bold text-trading-light">
              {performance ? performance.activePositions : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Profit</div>
            <div className="text-2xl font-bold text-green-400">
              {performance ? `₹${performance.profit.toLocaleString()}` : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Success Rate</div>
            <div className="text-2xl font-bold text-trading-light">
              {performance ? `${performance.successRate}%` : '--'}
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Agent Status</span>
            <Switch checked={isActive} onCheckedChange={handleToggle} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Avg Holding Period</span>
              <span className="text-sm text-trading-muted">{holdingPeriod[0]} days</span>
            </div>
            <Slider
              value={holdingPeriod}
              onValueChange={handleHoldingPeriodChange}
              max={30}
              min={1}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Position Size</span>
              <span className="text-sm text-trading-muted">{positionSize[0]}%</span>
            </div>
            <Slider
              value={positionSize}
              onValueChange={handlePositionSizeChange}
              max={50}
              min={5}
              step={5}
              className="w-full"
            />
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analysis
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Activity className="h-4 w-4 mr-2" />
            History
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
