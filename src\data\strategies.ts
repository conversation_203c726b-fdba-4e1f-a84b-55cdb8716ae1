
import { TradingStrategy } from '@/types/strategies';

export const ALL_STRATEGIES: TradingStrategy[] = [
  // Price Action Strategies (5)
  {
    id: 'pa-1',
    name: 'Wait and Trade the Pullback',
    category: 'Price Action',
    timeframe: '15m',
    indicators: ['Support/Resistance', 'Price Structure'],
    criteria: ['Price at Support/Resistance', 'Pullback Formation', 'Volume Confirmation'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'pa-2',
    name: 'Pin Bar Candlestick Pattern',
    category: 'Price Action',
    timeframe: '1h',
    indicators: ['Candlestick Patterns', 'Key Levels'],
    criteria: ['Pin Bar Formation', 'At Key Level', 'Rejection Candle'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'pa-3',
    name: 'Pure Pullback Strategy',
    category: 'Price Action',
    timeframe: '30m',
    indicators: ['Price Action', 'Market Structure'],
    criteria: ['Trend Confirmation', 'Pullback to Structure', 'Reversal Signal'],
    riskLevel: 'Low',
    isActive: true
  },
  {
    id: 'pa-4',
    name: 'Two-Legged Pullback',
    category: 'Price Action',
    timeframe: '15m',
    indicators: ['Price Structure', 'Market Flow'],
    criteria: ['First Leg Down', 'Second Leg Formation', 'Entry on Bounce'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'pa-5',
    name: 'Volatility Contraction Pattern',
    category: 'Price Action',
    timeframe: 'Daily',
    indicators: ['Price Compression', 'Volume Analysis'],
    criteria: ['Tight Range Formation', 'Volume Decrease', 'Breakout Setup'],
    riskLevel: 'High',
    isActive: true
  },

  // Options Strategies (13)
  {
    id: 'opt-1',
    name: 'Weekly Hedged Strategy',
    category: 'Options',
    timeframe: 'Daily',
    indicators: ['Greeks', 'Volatility', 'Time Decay'],
    criteria: ['Weekly Expiry', 'Hedge Position', 'Risk Management'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'opt-2',
    name: 'Multi-Time Option Strategy',
    category: 'Options',
    timeframe: '1h',
    indicators: ['Multi-TF Analysis', 'Option Chain', 'Volume'],
    criteria: ['Multi-Timeframe Alignment', 'Option Flow', 'Entry Timing'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-3',
    name: 'Option Buying Using Open Interest',
    category: 'Options',
    timeframe: '30m',
    indicators: ['Open Interest', 'Volume', 'Price Action'],
    criteria: ['OI Analysis', 'Volume Spike', 'Direction Confirmation'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'opt-4',
    name: 'Supertrend Selling Options',
    category: 'Options',
    timeframe: '15m',
    indicators: ['Supertrend', 'Option Greeks', 'ATR'],
    criteria: ['Supertrend Signal', 'Sell Premium', 'Risk Control'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-5',
    name: 'Combined Option + VWAP',
    category: 'Options',
    timeframe: '5m',
    indicators: ['VWAP', 'Option Flow', 'Volume'],
    criteria: ['VWAP Interaction', 'Option Premium', 'Quick Entry/Exit'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'opt-6',
    name: 'Momentum Buying Option',
    category: 'Options',
    timeframe: '5m',
    indicators: ['Momentum', 'Option Chain', 'Volume'],
    criteria: ['Strong Momentum', 'ATM/OTM Options', 'Volume Confirmation'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-7',
    name: 'Expiry Decay Strategy',
    category: 'Options',
    timeframe: '1h',
    indicators: ['Time Decay', 'Theta', 'Volatility'],
    criteria: ['Expiry Week', 'Theta Decay', 'Sell Premium'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'opt-8',
    name: 'Combined Stoploss Options',
    category: 'Options',
    timeframe: '15m',
    indicators: ['Stop Loss', 'Option Pricing', 'Risk Management'],
    criteria: ['Dynamic Stop Loss', 'Option Value', 'Risk Reward'],
    riskLevel: 'Low',
    isActive: true
  },
  {
    id: 'opt-9',
    name: 'Theta Decay Options',
    category: 'Options',
    timeframe: 'Daily',
    indicators: ['Theta', 'Time Value', 'Implied Volatility'],
    criteria: ['High Theta', 'Time Decay', 'Sell Strategy'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'opt-10',
    name: 'BTST Option Buying',
    category: 'Options',
    timeframe: 'Daily',
    indicators: ['Overnight Gap', 'Option Pricing', 'Market Sentiment'],
    criteria: ['Buy Today Sell Tomorrow', 'Gap Analysis', 'Overnight Premium'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-11',
    name: '3:00 PM Nifty Intraday',
    category: 'Options',
    timeframe: '5m',
    indicators: ['Time Analysis', 'Option Decay', 'Market Close'],
    criteria: ['3 PM Entry', 'Quick Scalp', 'Time Decay Advantage'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-12',
    name: 'Momentum Selling Options',
    category: 'Options',
    timeframe: '15m',
    indicators: ['Momentum', 'Option Greeks', 'Volume'],
    criteria: ['Strong Momentum', 'Sell Premium', 'Quick Profits'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'opt-13',
    name: 'Swing Buying Options',
    category: 'Options',
    timeframe: 'Daily',
    indicators: ['Swing Analysis', 'Option Chain', 'Time Value'],
    criteria: ['Multi-Day Hold', 'Swing Setup', 'Option Selection'],
    riskLevel: 'Medium',
    isActive: true
  },

  // Swing Trading Strategies (7)
  {
    id: 'swing-1',
    name: 'Williams %R and MACD Swing',
    category: 'Swing',
    timeframe: 'Daily',
    indicators: ['Williams %R', 'MACD', 'Volume'],
    criteria: ['Williams %R Signal', 'MACD Confirmation', 'Multi-Day Hold'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'swing-2',
    name: 'MACD + Fibonacci Retracement',
    category: 'Swing',
    timeframe: '1h',
    indicators: ['MACD', 'Fibonacci', 'Support/Resistance'],
    criteria: ['MACD Signal', 'Fib Level', 'Swing Structure'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'swing-3',
    name: 'Institutional Moves Swing',
    category: 'Swing',
    timeframe: 'Daily',
    indicators: ['Institutional Flow', 'Volume Analysis', 'Price Structure'],
    criteria: ['Smart Money Flow', 'Large Volume', 'Position Building'],
    riskLevel: 'Low',
    isActive: true
  },
  {
    id: 'swing-4',
    name: 'Double RSI Swing',
    category: 'Swing',
    timeframe: '1h',
    indicators: ['RSI 14', 'RSI 21', 'Trend Analysis'],
    criteria: ['Double RSI Signal', 'Trend Alignment', 'Swing Entry'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'swing-5',
    name: 'Sectoral Analysis Swing',
    category: 'Swing',
    timeframe: 'Daily',
    indicators: ['Sector Rotation', 'Relative Strength', 'Market Analysis'],
    criteria: ['Sector Leadership', 'Relative Performance', 'Swing Setup'],
    riskLevel: 'Low',
    isActive: true
  },
  {
    id: 'swing-6',
    name: 'Swing Buying Options Strategy',
    category: 'Swing',
    timeframe: 'Daily',
    indicators: ['Swing Analysis', 'Option Pricing', 'Time Decay'],
    criteria: ['Swing Setup', 'Option Selection', 'Multi-Day Strategy'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'swing-7',
    name: 'Positional Trading Strategy',
    category: 'Swing',
    timeframe: 'Daily',
    indicators: ['Long Term Trend', 'Support/Resistance', 'Volume'],
    criteria: ['Position Building', 'Long Term View', 'Trend Following'],
    riskLevel: 'Low',
    isActive: true
  },

  // Intraday Strategies (13)
  {
    id: 'intraday-1',
    name: 'RSI + Volume Oscillator',
    category: 'Intraday',
    timeframe: '15m',
    indicators: ['RSI', 'Volume Oscillator', 'Price Action'],
    criteria: ['RSI Signal', 'Volume Confirmation', 'Same Day Exit'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-2',
    name: 'Donchian Channel Pullback',
    category: 'Intraday',
    timeframe: '15m',
    indicators: ['Donchian Channel', 'Pullback Analysis', 'Volume'],
    criteria: ['Channel Break', 'Pullback Entry', 'Intraday Target'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-3',
    name: 'Macro Trends Intraday',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['Market Sentiment', 'Macro Analysis', 'Flow'],
    criteria: ['Macro Direction', 'Intraday Setup', 'Quick Execution'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'intraday-4',
    name: 'Supertrend with RSI',
    category: 'Intraday',
    timeframe: '15m',
    indicators: ['Supertrend', 'RSI', 'ATR'],
    criteria: ['Supertrend Signal', 'RSI Confirmation', 'Trend Following'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-5',
    name: '3-Min Parabolic SAR Scalping',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['Parabolic SAR', 'Heiken Ashi', 'Volume'],
    criteria: ['SAR Signal', 'Heiken Ashi Confirmation', 'Quick Scalp'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'intraday-6',
    name: 'RSI Divergence + BB Scalping',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['RSI Divergence', 'Bollinger Bands', 'Volume'],
    criteria: ['RSI Divergence', 'BB Touch', 'Scalping Entry'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'intraday-7',
    name: 'RSI + VWAP Scalping',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['RSI', 'VWAP', 'Volume'],
    criteria: ['RSI Signal', 'VWAP Interaction', 'Volume Spike'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-8',
    name: '1-Min Consolidation Breakouts',
    category: 'Intraday',
    timeframe: '1m',
    indicators: ['Consolidation Pattern', 'Breakout', 'Volume'],
    criteria: ['Tight Range', 'Volume Breakout', 'Quick Entry'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'intraday-9',
    name: 'Moving Average Scalping',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['EMA 9', 'EMA 21', 'Volume'],
    criteria: ['EMA Cross', 'Trend Direction', 'Scalping Setup'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-10',
    name: 'Martingale System',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['Position Sizing', 'Risk Management', 'Recovery'],
    criteria: ['Loss Recovery', 'Double Position', 'Risk Control'],
    riskLevel: 'High',
    isActive: false
  },
  {
    id: 'intraday-11',
    name: 'Option + VWAP Intraday',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['VWAP', 'Option Flow', 'Volume'],
    criteria: ['VWAP Signal', 'Option Premium', 'Intraday Exit'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'intraday-12',
    name: '3:00 PM Nifty Option',
    category: 'Intraday',
    timeframe: '5m',
    indicators: ['Time Analysis', 'Option Greeks', 'Market Close'],
    criteria: ['Late Entry', 'Time Decay', 'Quick Exit'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'intraday-13',
    name: '9 and 21 EMA Support/Resistance',
    category: 'Intraday',
    timeframe: '15m',
    indicators: ['EMA 9', 'EMA 21', 'Support/Resistance'],
    criteria: ['EMA as Support', 'Price Rejection', 'Intraday Setup'],
    riskLevel: 'Low',
    isActive: true
  },

  // Scalping Strategies (13)
  {
    id: 'scalp-1',
    name: 'Level 2 Momentum Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Level 2', 'Volume', 'Price Action'],
    criteria: ['Large Orders on L2', 'Volume Spike', 'Price Momentum'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-2',
    name: 'VWAP Touch Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['VWAP', 'Volume', 'Price Action'],
    criteria: ['Price touches VWAP', 'Quick Rejection', 'Volume Confirmation'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-3',
    name: 'Tape Reading Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Time & Sales', 'Level 2', 'Volume'],
    criteria: ['Large Print Analysis', 'Order Flow Direction', 'Quick Entry/Exit'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-4',
    name: 'Bid-Ask Spread Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Bid-Ask Spread', 'Volume', 'Price Action'],
    criteria: ['Tight Spread', 'High Volume', 'Quick Price Movement'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-5',
    name: 'Market Maker Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['MM Lines', 'Order Flow', 'Volume'],
    criteria: ['MM Support/Resistance', 'Order Imbalance', 'Quick Reversal'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-6',
    name: 'Momentum Burst Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Volume', 'Price Velocity', 'ATR'],
    criteria: ['Sudden Volume Increase', 'Price Acceleration', 'Quick Follow-through'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-7',
    name: 'Support/Resistance Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Key Levels', 'Volume', 'Price Action'],
    criteria: ['Price at Key Level', 'Volume Spike', 'Quick Bounce/Break'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-8',
    name: 'News Event Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Volume', 'Price Volatility', 'News Feed'],
    criteria: ['Breaking News', 'Volume Explosion', 'Price Momentum'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-9',
    name: 'Triangle Breakout Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Triangle Pattern', 'Volume', 'Breakout Direction'],
    criteria: ['Micro Triangle Formation', 'Volume Decrease', 'Breakout with Volume'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-10',
    name: '3-Minute Parabolic SAR',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Parabolic SAR', 'Heiken Ashi', 'Volume'],
    criteria: ['SAR Flip', 'Heiken Ashi Color', 'Volume Spike'],
    riskLevel: 'High',
    isActive: true
  },
  {
    id: 'scalp-11',
    name: 'RSI Divergence Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['RSI', 'Price Divergence', 'Volume'],
    criteria: ['RSI Divergence', 'Price Action', 'Quick Reversal'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-12',
    name: 'Moving Average Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['EMA 9', 'EMA 21', 'Volume'],
    criteria: ['EMA Cross', 'Quick Entry', 'Fast Exit'],
    riskLevel: 'Medium',
    isActive: true
  },
  {
    id: 'scalp-13',
    name: 'Consolidation Breakout Scalp',
    category: 'Scalping',
    timeframe: '1m',
    indicators: ['Range Analysis', 'Breakout', 'Volume'],
    criteria: ['Tight Range', 'Volume Breakout', 'Quick Momentum'],
    riskLevel: 'High',
    isActive: true
  }
];

export const getStrategiesByCategory = (category: string) => 
  ALL_STRATEGIES.filter(strategy => strategy.category === category);

export const getStrategiesByTimeframe = (timeframe: string) => 
  ALL_STRATEGIES.filter(strategy => strategy.timeframe === timeframe);

export const getActiveStrategies = () => 
  ALL_STRATEGIES.filter(strategy => strategy.isActive);
