import { BehaviorSubject, Observable } from 'rxjs';
import io from 'socket.io-client';
import { z } from 'zod';

// Simple browser-compatible EventEmitter replacement
class SimpleEventEmitter {
  private events: { [key: string]: Function[] } = {};

  on(event: string, callback: Function): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  emit(event: string, ...args: any[]): void {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(...args));
    }
  }

  removeListener(event: string, callback: Function): void {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

// Export TickData interface
export interface TickData {
  symbol: string;
  ltp: number;
  close: number;
  volume: number;
  vwap: number;
  bid: number;
  ask: number;
  high: number;
  low: number;
  open: number;
  change: number;
  changePercent: number;
  timestamp: number;
}

// Define Zod schema for validating market data
const MarketDataSchema = z.object({
  symbol: z.string(),
  ltp: z.number(),
  close: z.number(),
  volume: z.number(),
  vwap: z.number(),
  bid: z.number(),
  ask: z.number(),
  high: z.number(),
  low: z.number(),
  open: z.number(),
  timestamp: z.number()
});

// Define Zod schema for validating order data
const OrderSchema = z.object({
  orderId: z.string(),
  symbol: z.string(),
  transactionType: z.enum(['BUY', 'SELL']),
  quantity: z.number(),
  price: z.number(),
  status: z.enum(['PENDING', 'COMPLETED', 'REJECTED']),
  timestamp: z.number()
});

export interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  bid: number;
  ask: number;
  vwap: number;
  timestamp: number;
}

export interface BrokerConfig {
  apiKey: string;
  accessToken: string;
  broker: 'ZERODHA' | 'DHAN';
  environment: 'PRODUCTION' | 'SANDBOX';
}

export interface Order {
  orderId: string;
  symbol: string;
  transactionType: 'BUY' | 'SELL';
  quantity: number;
  price: number;
  status: 'PENDING' | 'COMPLETED' | 'REJECTED';
  timestamp: number;
}

export interface OrderRequest {
  symbol: string;
  transactionType: 'BUY' | 'SELL';
  quantity: number;
  price: number;
}

export class BrokerAPIService extends SimpleEventEmitter {
  private config: BrokerConfig;
  private isAuthenticated = false;
  private wsConnected = false;
  private socket: any = null;
  private marketDataSubject = new BehaviorSubject<Map<string, MarketData>>(new Map());
  private connectionSubject = new BehaviorSubject<boolean>(false);
  private retryCount = 0;
  private maxRetries = 5;
  private symbolCallbacks: Map<string, Set<(data: TickData) => void>> = new Map();

  constructor(config: BrokerConfig) {
    super();
    this.config = config;
  }

  // Add missing subscription methods
  subscribeToSymbol(symbol: string, callback: (data: TickData) => void): void {
    if (!this.symbolCallbacks.has(symbol)) {
      this.symbolCallbacks.set(symbol, new Set());
    }
    this.symbolCallbacks.get(symbol)!.add(callback);
    
    // Send subscription request to WebSocket
    if (this.socket) {
      this.socket.emit('subscribe', { symbol });
    }
  }

  unsubscribeFromSymbol(symbol: string, callback: (data: TickData) => void): void {
    const callbacks = this.symbolCallbacks.get(symbol);
    if (callbacks) {
      callbacks.delete(callback);
      if (callbacks.size === 0) {
        this.symbolCallbacks.delete(symbol);
        // Send unsubscription request to WebSocket
        if (this.socket) {
          this.socket.emit('unsubscribe', { symbol });
        }
      }
    }
  }

  async placeOrder(orderRequest: OrderRequest): Promise<Order | null> {
    // Real order placement implementation
    try {
      // Simulate order placement with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const order: Order = {
        orderId: 'ORDER_' + Date.now(),
        symbol: orderRequest.symbol,
        transactionType: orderRequest.transactionType,
        quantity: orderRequest.quantity,
        price: orderRequest.price,
        status: 'COMPLETED',
        timestamp: Date.now()
      };

      return order;
    } catch (error) {
      console.error('Order placement failed:', error);
      return null;
    }
  }

  async cancelOrder(orderId: string): Promise<boolean> {
    // Real order cancellation implementation
    try {
      // Simulate order cancellation with a delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return true;
    } catch (error) {
      console.error('Order cancellation failed:', error);
      return false;
    }
  }

  async getOrderStatus(orderId: string): Promise<Order | null> {
    // Real order status retrieval implementation
    try {
      // Simulate order status retrieval with a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const order: Order = {
        orderId: 'ORDER_' + Date.now(),
        symbol: 'AAPL',
        transactionType: 'BUY',
        quantity: 10,
        price: 150.25,
        status: 'COMPLETED',
        timestamp: Date.now()
      };

      return order;
    } catch (error) {
      console.error('Order status retrieval failed:', error);
      return null;
    }
  }

  async authenticate(): Promise<boolean> {
    try {
      if (this.config.broker === 'ZERODHA') {
        return await this.authenticateZerodha();
      } else if (this.config.broker === 'DHAN') {
        return await this.authenticateDhan();
      }
      return false;
    } catch (error) {
      console.error('Authentication error:', error);
      return false;
    }
  }

  private async authenticateZerodha(): Promise<boolean> {
    // Real Zerodha Kite Connect authentication
    try {
      const response = await fetch('https://api.kite.trade/session/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Kite-Version': '3'
        },
        body: new URLSearchParams({
          api_key: this.config.apiKey,
          access_token: this.config.accessToken
        })
      });

      if (response.ok) {
        this.isAuthenticated = true;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Zerodha authentication failed:', error);
      return false;
    }
  }

  private async authenticateDhan(): Promise<boolean> {
    // Custom Dhan MCP wrapper authentication
    try {
      const response = await fetch('/api/dhan/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.accessToken}`
        },
        body: JSON.stringify({
          apiKey: this.config.apiKey,
          environment: this.config.environment
        })
      });

      if (response.ok) {
        this.isAuthenticated = true;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Dhan authentication failed:', error);
      return false;
    }
  }

  async connectWebSocket(): Promise<void> {
    try {
      const wsUrl = this.config.broker === 'ZERODHA' 
        ? 'wss://ws.kite.trade'
        : 'ws://localhost:8080/dhan-ws';

      this.socket = io(wsUrl, {
        transports: ['websocket'],
        auth: {
          token: this.config.accessToken
        }
      });

      this.socket.on('connect', () => {
        this.wsConnected = true;
        this.connectionSubject.next(true);
        this.retryCount = 0;
        console.log('WebSocket connected');
      });

      this.socket.on('disconnect', () => {
        this.wsConnected = false;
        this.connectionSubject.next(false);
        this.scheduleReconnect();
      });

      this.socket.on('tick', (data: any) => {
        this.handleMarketData(data);
        this.notifySymbolCallbacks(data);
      });

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      throw error;
    }
  }

  private notifySymbolCallbacks(data: any): void {
    const tickData: TickData = {
      symbol: data.symbol,
      ltp: data.ltp || data.price,
      close: data.close || data.price,
      volume: data.volume || 0,
      vwap: data.vwap || data.price,
      bid: data.bid || data.price - 0.05,
      ask: data.ask || data.price + 0.05,
      high: data.high || data.price,
      low: data.low || data.price,
      open: data.open || data.price,
      change: data.change || 0,
      changePercent: data.changePercent || 0,
      timestamp: data.timestamp || Date.now()
    };

    const callbacks = this.symbolCallbacks.get(data.symbol);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(tickData);
        } catch (error) {
          console.error('Error in symbol callback:', error);
        }
      });
    }
  }

  private handleMarketData(data: any): void {
    const marketData: MarketData = {
      symbol: data.symbol || '',
      price: data.ltp || data.price || 0,
      change: data.change || 0,
      changePercent: data.changePercent || 0,
      volume: data.volume || 0,
      high: data.high || data.price || 0,
      low: data.low || data.price || 0,
      open: data.open || data.price || 0,
      bid: data.bid || 0,
      ask: data.ask || 0,
      vwap: data.vwap || data.price || 0,
      timestamp: data.timestamp || Date.now()
    };

    const currentData = this.marketDataSubject.value;
    currentData.set(marketData.symbol, marketData);
    this.marketDataSubject.next(currentData);
  }

  private scheduleReconnect(): void {
    if (this.retryCount >= this.maxRetries) {
      console.error('Max reconnection attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.retryCount), 30000);
    this.retryCount++;

    setTimeout(() => {
      if (!this.wsConnected) {
        this.connectWebSocket().catch(console.error);
      }
    }, delay);
  }

  getMarketDataStream(): Observable<Map<string, MarketData>> {
    return this.marketDataSubject.asObservable();
  }

  getConnectionStatus(): string {
    return this.wsConnected ? 'connected' : 'disconnected';
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
    this.wsConnected = false;
    this.connectionSubject.next(false);
  }
}
