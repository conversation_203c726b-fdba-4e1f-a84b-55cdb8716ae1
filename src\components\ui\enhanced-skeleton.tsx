
import React from 'react';
import { cn } from "@/lib/utils";

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'text' | 'title' | 'avatar' | 'card' | 'chart';
  lines?: number;
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = 'default', lines = 1, ...props }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case 'text':
          return 'h-4 w-full';
        case 'title':
          return 'h-6 w-3/4';
        case 'avatar':
          return 'h-10 w-10 rounded-full';
        case 'card':
          return 'h-32 w-full';
        case 'chart':
          return 'h-64 w-full';
        default:
          return 'h-4 w-full';
      }
    };

    if (variant === 'text' && lines > 1) {
      return (
        <div className="space-y-2">
          {Array.from({ length: lines }).map((_, index) => (
            <div
              key={index}
              ref={index === 0 ? ref : undefined}
              className={cn(
                "skeleton animate-shimmer rounded-md bg-muted/20",
                index === lines - 1 ? "w-3/4" : "w-full",
                "h-4",
                className
              )}
              {...(index === 0 ? props : {})}
            />
          ))}
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          "skeleton animate-shimmer rounded-md bg-muted/20",
          getVariantClasses(),
          className
        )}
        {...props}
      />
    );
  }
);

Skeleton.displayName = "Skeleton";

// Specialized skeleton components
export const SkeletonCard = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("glass-card p-6 space-y-4", className)} {...props}>
    <Skeleton variant="title" />
    <Skeleton variant="text" lines={3} />
    <div className="flex space-x-2">
      <Skeleton className="h-8 w-20" />
      <Skeleton className="h-8 w-16" />
    </div>
  </div>
);

export const SkeletonChart = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("glass-card p-6", className)} {...props}>
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton variant="title" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
      <Skeleton variant="chart" />
    </div>
  </div>
);

export const SkeletonTable = ({ 
  rows = 5, 
  columns = 4, 
  className, 
  ...props 
}: { 
  rows?: number; 
  columns?: number; 
} & React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("glass-card", className)} {...props}>
    <div className="p-6 space-y-4">
      {/* Table Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={`header-${index}`} className="h-5 w-20" />
        ))}
      </div>
      {/* Table Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={`row-${rowIndex}`} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={`cell-${rowIndex}-${colIndex}`} className="h-4" />
          ))}
        </div>
      ))}
    </div>
  </div>
);

export { Skeleton };
