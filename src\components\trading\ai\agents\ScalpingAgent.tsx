
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Zap, Settings, TrendingUp, Activity } from "lucide-react";
import { useState } from "react";

interface ScalpingPerformance {
  confidence: number;
  tradesCount: number;
  profit: number;
  winRate: number;
}

interface ScalpingAgentProps {
  isActive?: boolean;
  onToggle?: (active: boolean) => void;
  performance?: ScalpingPerformance;
  onRiskLevelChange?: (level: number) => void;
  onMaxTradesChange?: (maxTrades: number) => void;
}

export const ScalpingAgent = ({
  isActive: propIsActive,
  onToggle,
  performance,
  onRiskLevelChange,
  onMaxTradesChange
}: ScalpingAgentProps) => {
  const [localIsActive, setLocalIsActive] = useState(true);
  const [riskLevel, setRiskLevel] = useState([3]);
  const [maxTrades, setMaxTrades] = useState([50]);

  const isActive = propIsActive !== undefined ? propIsActive : localIsActive;

  const handleToggle = (active: boolean) => {
    if (onToggle) {
      onToggle(active);
    } else {
      setLocalIsActive(active);
    }
  };

  const handleRiskLevelChange = (value: number[]) => {
    setRiskLevel(value);
    if (onRiskLevelChange) {
      onRiskLevelChange(value[0]);
    }
  };

  const handleMaxTradesChange = (value: number[]) => {
    setMaxTrades(value);
    if (onMaxTradesChange) {
      onMaxTradesChange(value[0]);
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Zap className="h-5 w-5 mr-2 text-yellow-400" />
            Scalping Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
              {isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Confidence</div>
            <div className="text-2xl font-bold text-blue-400">
              {performance ? `${performance.confidence}%` : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Trades Today</div>
            <div className="text-2xl font-bold text-trading-light">
              {performance ? performance.tradesCount : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Profit</div>
            <div className="text-2xl font-bold text-green-400">
              {performance ? `₹${performance.profit.toLocaleString()}` : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Win Rate</div>
            <div className="text-2xl font-bold text-trading-light">
              {performance ? `${performance.winRate}%` : '--'}
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Agent Status</span>
            <Switch checked={isActive} onCheckedChange={handleToggle} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Risk Level</span>
              <span className="text-sm text-trading-muted">{riskLevel[0]}/10</span>
            </div>
            <Slider
              value={riskLevel}
              onValueChange={handleRiskLevelChange}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Max Trades/Day</span>
              <span className="text-sm text-trading-muted">{maxTrades[0]}</span>
            </div>
            <Slider
              value={maxTrades}
              onValueChange={handleMaxTradesChange}
              max={100}
              min={10}
              step={5}
              className="w-full"
            />
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <TrendingUp className="h-4 w-4 mr-2" />
            Performance
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Activity className="h-4 w-4 mr-2" />
            Logs
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
