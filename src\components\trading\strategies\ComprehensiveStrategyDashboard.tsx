
import { useState, useEffect, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Search, Filter, Brain, DollarSign, TrendingUp, Activity, Zap, Target, Play, Pause, Settings } from "lucide-react";
import { ALL_STRATEGIES } from "@/data/strategies";
import { MathematicalVerificationService } from "@/services/MathematicalVerificationService";
import { useToast } from "@/hooks/use-toast";

interface StrategyPerformance {
  id: string;
  winRate: number;
  totalTrades: number;
  pnl: number;
  sharpeRatio: number;
  maxDrawdown: number;
  isActive: boolean;
  lastSignal?: {
    action: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    timestamp: number;
    symbol: string;
  };
}

export const ComprehensiveStrategyDashboard = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>("");
  const [masterActive, setMasterActive] = useState(false);
  const [strategies, setStrategies] = useState<StrategyPerformance[]>([]);
  const [mathVerification, setMathVerification] = useState<any>(null);
  const [isRunningVerification, setIsRunningVerification] = useState(false);
  const { toast } = useToast();

  // Initialize strategies with mock performance data
  useEffect(() => {
    const initialStrategies = ALL_STRATEGIES.map(strategy => ({
      id: strategy.id,
      winRate: 45 + Math.random() * 40, // 45-85%
      totalTrades: Math.floor(10 + Math.random() * 200),
      pnl: (Math.random() - 0.3) * 50000, // -15k to +35k
      sharpeRatio: 0.5 + Math.random() * 2, // 0.5 to 2.5
      maxDrawdown: Math.random() * 25, // 0-25%
      isActive: false
    }));
    setStrategies(initialStrategies);
  }, []);

  // Run mathematical verification
  const runMathematicalVerification = async () => {
    setIsRunningVerification(true);
    try {
      const auditResult = await MathematicalVerificationService.runFullAudit();
      setMathVerification(auditResult);
      
      toast({
        title: "Mathematical Verification Complete",
        description: `${auditResult.passed}/${auditResult.totalTests} tests passed`,
        variant: auditResult.failed === 0 ? "default" : "destructive"
      });
    } catch (error) {
      toast({
        title: "Verification Failed",
        description: "Error running mathematical verification",
        variant: "destructive"
      });
    } finally {
      setIsRunningVerification(false);
    }
  };

  // Filter strategies based on search and filters
  const filteredStrategies = useMemo(() => {
    return ALL_STRATEGIES.filter(strategy => {
      const matchesSearch = strategy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           strategy.indicators.some(ind => ind.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = !selectedCategory || strategy.category === selectedCategory;
      const matchesTimeframe = !selectedTimeframe || strategy.timeframe === selectedTimeframe;
      
      return matchesSearch && matchesCategory && matchesTimeframe;
    });
  }, [searchTerm, selectedCategory, selectedTimeframe]);

  // Toggle strategy activation
  const toggleStrategy = (strategyId: string) => {
    setStrategies(prev => prev.map(s => 
      s.id === strategyId ? { ...s, isActive: !s.isActive } : s
    ));
  };

  // Master toggle
  const handleMasterToggle = (active: boolean) => {
    setMasterActive(active);
    setStrategies(prev => prev.map(s => ({ ...s, isActive: active })));
  };

  // Get strategy performance
  const getStrategyPerformance = (strategyId: string) => {
    return strategies.find(s => s.id === strategyId);
  };

  // Calculate aggregate stats
  const activeStrategies = strategies.filter(s => s.isActive).length;
  const totalPnL = strategies.reduce((sum, s) => sum + s.pnl, 0);
  const avgWinRate = strategies.length > 0 
    ? strategies.reduce((sum, s) => sum + s.winRate, 0) / strategies.length 
    : 0;
  const totalTrades = strategies.reduce((sum, s) => sum + s.totalTrades, 0);

  const categories = ['Price Action', 'Options', 'Swing', 'Intraday', 'Scalping'];
  const timeframes = ['1m', '5m', '15m', '30m', '1h', 'Daily'];

  return (
    <div className="space-y-6">
      {/* Header with Master Controls */}
      <Card className="bg-trading-darker border-trading-border border-2 border-blue-500">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-trading-light flex items-center">
              <Brain className="h-5 w-5 mr-2 text-blue-400" />
              Strategy Command Center - All {ALL_STRATEGIES.length} Strategies
            </CardTitle>
            <div className="flex items-center space-x-4">
              <Button 
                onClick={runMathematicalVerification}
                disabled={isRunningVerification}
                variant="outline"
                size="sm"
              >
                {isRunningVerification ? "Verifying..." : "Verify Math"}
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-trading-light">Master Control</span>
                <Switch checked={masterActive} onCheckedChange={handleMasterToggle} />
              </div>
              <Badge variant="outline" className={masterActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                {masterActive ? "All Active" : "All Inactive"}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{activeStrategies}</div>
              <div className="text-sm text-trading-muted">Active Strategies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">₹{totalPnL.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">Total P&L</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{totalTrades.toLocaleString()}</div>
              <div className="text-sm text-trading-muted">Total Trades</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{avgWinRate.toFixed(1)}%</div>
              <div className="text-sm text-trading-muted">Avg Win Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mathematical Verification Results */}
      {mathVerification && (
        <Card className="bg-trading-darker border-trading-border">
          <CardHeader>
            <CardTitle className="text-trading-light">Mathematical Verification Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">{mathVerification.passed}</div>
                <div className="text-sm text-trading-muted">Tests Passed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">{mathVerification.failed}</div>
                <div className="text-sm text-trading-muted">Tests Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{mathVerification.warnings}</div>
                <div className="text-sm text-trading-muted">Warnings</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{mathVerification.totalTests}</div>
                <div className="text-sm text-trading-muted">Total Tests</div>
              </div>
            </div>
            <div className="space-y-2">
              {mathVerification.results.map((result: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                  <span className="text-sm text-trading-light">{result.function}</span>
                  <Badge variant="outline" className={
                    result.status === 'PASS' ? "text-green-400 border-green-400" :
                    result.status === 'FAIL' ? "text-red-400 border-red-400" :
                    "text-yellow-400 border-yellow-400"
                  }>
                    {result.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-trading-muted" />
                <Input
                  placeholder="Search strategies or indicators..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Categories</SelectItem>
                  {categories.map(cat => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="All Timeframes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Timeframes</SelectItem>
                  {timeframes.map(tf => (
                    <SelectItem key={tf} value={tf}>{tf}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {filteredStrategies.map((strategy) => {
          const performance = getStrategyPerformance(strategy.id);
          const isActive = performance?.isActive || false;
          
          return (
            <Card key={strategy.id} className="bg-trading-darker border-trading-border hover:border-blue-500 transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm text-trading-light">{strategy.name}</CardTitle>
                  <div className="flex space-x-1">
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-6 w-6 p-0"
                      onClick={() => toggleStrategy(strategy.id)}
                    >
                      {isActive ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                    </Button>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div className="flex space-x-2 flex-wrap">
                  <Badge variant="outline" className="text-xs text-blue-400 border-blue-400">
                    {strategy.category}
                  </Badge>
                  <Badge variant="outline" className="text-xs text-purple-400 border-purple-400">
                    {strategy.timeframe}
                  </Badge>
                  <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                    {isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                {performance && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-xs text-trading-muted">Win Rate</span>
                      <span className="text-xs text-green-400">{performance.winRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-trading-muted">P&L</span>
                      <span className={`text-xs ${performance.pnl >= 0 ? "text-green-400" : "text-red-400"}`}>
                        ₹{performance.pnl.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-trading-muted">Trades</span>
                      <span className="text-xs text-trading-light">{performance.totalTrades}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-xs text-trading-muted">Sharpe</span>
                      <span className="text-xs text-blue-400">{performance.sharpeRatio.toFixed(2)}</span>
                    </div>
                  </>
                )}
                <div className="text-xs text-trading-muted">
                  <div className="font-medium mb-1">Indicators:</div>
                  <div className="flex flex-wrap gap-1">
                    {strategy.indicators.map((indicator, idx) => (
                      <span key={idx} className="bg-trading-accent px-1 rounded">
                        {indicator}
                      </span>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredStrategies.length === 0 && (
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="py-12">
            <div className="text-center">
              <Search className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
              <div className="text-lg text-trading-muted mb-2">No strategies found</div>
              <div className="text-sm text-trading-muted">Try adjusting your search or filters</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
