
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, BarChart, Activity, Target } from "lucide-react";
import { TradingChart } from "./TradingChart";

export const ChartingDashboard = () => {
  const indicators = [
    { name: "VWAP", enabled: true, value: "24,156.80" },
    { name: "Anchored VWAP", enabled: true, value: "24,148.20" },
    { name: "RSI (14)", enabled: true, value: "67.3" },
    { name: "MACD", enabled: false, value: "12.45" },
    { name: "Bollinger Bands", enabled: true, value: "±1.2%" },
    { name: "Volume Profile", enabled: true, value: "POC: 24,150" },
  ];

  return (
    <div className="space-y-4">
      {/* Main Trading Chart */}
      <TradingChart />

      {/* Order Flow Analysis */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Order Flow Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Smart Money Flow</div>
              <div className="text-xl font-bold text-green-400">+₹45.2Cr</div>
              <div className="text-xs text-green-400">Institutional Buying</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Retail Sentiment</div>
              <div className="text-xl font-bold text-red-400">-₹12.8Cr</div>
              <div className="text-xs text-red-400">Profit Booking</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Liquidity Voids</div>
              <div className="text-xl font-bold text-yellow-400">3 Zones</div>
              <div className="text-xs text-yellow-400">24,180 - 24,220</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Volume Profile */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Volume Profile & Key Levels
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { level: "24,180", type: "Resistance", volume: "High", color: "text-red-400" },
              { level: "24,150", type: "POC", volume: "Highest", color: "text-blue-400" },
              { level: "24,120", type: "Support", volume: "Medium", color: "text-green-400" },
              { level: "24,090", type: "Support", volume: "Low", color: "text-green-400" },
            ].map((level, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-trading-dark rounded">
                <div className="flex items-center space-x-3">
                  <span className={`font-medium ${level.color}`}>{level.level}</span>
                  <Badge variant="outline" className="text-xs">{level.type}</Badge>
                </div>
                <span className="text-xs text-trading-muted">Vol: {level.volume}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
