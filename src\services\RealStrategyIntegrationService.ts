
import { StrategyExecutionEngine, StrategyExecution, RealTimeStrategyUpdate } from './StrategyExecutionEngine';
import { RealDataPipelineService } from './RealDataPipelineService';
import { BehaviorSubject, Observable } from 'rxjs';

export interface StrategyPerformanceMetrics {
  strategyId: string;
  name: string;
  totalSignals: number;
  winRate: number;
  totalPnL: number;
  sharpeRatio: number;
  maxDrawdown: number;
  avgTradeSize: number;
  lastSignalTime: string;
  isActive: boolean;
}

export interface PortfolioAllocation {
  strategyId: string;
  allocatedCapital: number;
  currentExposure: number;
  utilizationPercent: number;
  riskScore: number;
}

export interface RealTimeMetrics {
  totalActiveStrategies: number;
  totalSignalsToday: number;
  portfolioPnL: number;
  bestPerformer: string;
  worstPerformer: string;
  totalCapitalAllocated: number;
  timestamp: number;
}

export class RealStrategyIntegrationService {
  private executionEngine: StrategyExecutionEngine;
  private dataService: RealDataPipelineService;
  private performanceMetrics: Map<string, StrategyPerformanceMetrics> = new Map();
  private portfolioAllocations: Map<string, PortfolioAllocation> = new Map();
  private metricsSubject = new BehaviorSubject<RealTimeMetrics>({
    totalActiveStrategies: 0,
    totalSignalsToday: 0,
    portfolioPnL: 0,
    bestPerformer: '',
    worstPerformer: '',
    totalCapitalAllocated: 0,
    timestamp: Date.now()
  });

  constructor() {
    this.executionEngine = new StrategyExecutionEngine();
    this.dataService = new RealDataPipelineService(
      {
        marketDataProvider: 'polygon',
        brokerConnection: 'zerodha',
        updateInterval: 5000,
        cacheEnabled: true,
        validationEnabled: true
      },
      {
        host: 'localhost',
        port: 5432,
        database: 'trading',
        username: 'admin',
        password: 'password'
      }
    );
  }

  async initialize(): Promise<void> {
    await this.executionEngine.initialize();
    await this.dataService.initialize();
    
    // Subscribe to strategy updates
    this.executionEngine.getUpdatesStream().subscribe((updates) => {
      this.processStrategyUpdates(updates);
    });

    // Initialize performance tracking
    await this.initializePerformanceTracking();
    
    // Start real-time metrics updates
    this.startMetricsUpdates();
    
    console.log('RealStrategyIntegrationService initialized');
  }

  private async initializePerformanceTracking(): Promise<void> {
    const strategies = this.executionEngine.getStrategies();
    
    for (const strategy of strategies) {
      const performance = await this.executionEngine.getStrategyPerformance(strategy.id);
      
      this.performanceMetrics.set(strategy.id, {
        strategyId: strategy.id,
        name: strategy.name,
        totalSignals: performance.totalSignals,
        winRate: performance.winRate,
        totalPnL: performance.totalPnL,
        sharpeRatio: this.calculateSharpeRatio(strategy.id),
        maxDrawdown: this.calculateMaxDrawdown(strategy.id),
        avgTradeSize: this.calculateAverageTradeSize(strategy.id),
        lastSignalTime: performance.lastUpdated,
        isActive: performance.activeExecutions > 0
      });

      // Initialize portfolio allocation
      this.portfolioAllocations.set(strategy.id, {
        strategyId: strategy.id,
        allocatedCapital: 100000, // Default allocation
        currentExposure: 0,
        utilizationPercent: 0,
        riskScore: this.calculateRiskScore(strategy.riskLevel)
      });
    }
  }

  private processStrategyUpdates(updates: RealTimeStrategyUpdate[]): void {
    updates.forEach(update => {
      this.updatePerformanceMetrics(update);
      this.updatePortfolioMetrics(update);
    });
    
    this.updateRealTimeMetrics();
  }

  private updatePerformanceMetrics(update: RealTimeStrategyUpdate): void {
    const metrics = this.performanceMetrics.get(update.strategyId);
    if (!metrics) return;

    metrics.totalSignals = update.performance.totalSignals;
    metrics.winRate = update.performance.winRate;
    metrics.totalPnL = update.performance.totalPnL;
    metrics.lastSignalTime = new Date().toISOString();
    
    // Recalculate derived metrics
    metrics.sharpeRatio = this.calculateSharpeRatio(update.strategyId);
    metrics.maxDrawdown = this.calculateMaxDrawdown(update.strategyId);
    
    this.performanceMetrics.set(update.strategyId, metrics);
  }

  private updatePortfolioMetrics(update: RealTimeStrategyUpdate): void {
    const allocation = this.portfolioAllocations.get(update.strategyId);
    if (!allocation) return;

    // Update exposure based on signal
    const signalValue = update.signal.price * 100; // Assume 100 shares per signal
    
    if (update.signal.action === 'BUY') {
      allocation.currentExposure += signalValue;
    } else {
      allocation.currentExposure -= signalValue;
    }
    
    allocation.utilizationPercent = (Math.abs(allocation.currentExposure) / allocation.allocatedCapital) * 100;
    
    this.portfolioAllocations.set(update.strategyId, allocation);
  }

  private updateRealTimeMetrics(): void {
    const metrics = Array.from(this.performanceMetrics.values());
    const allocations = Array.from(this.portfolioAllocations.values());
    
    const totalActiveStrategies = metrics.filter(m => m.isActive).length;
    const totalSignalsToday = metrics.reduce((sum, m) => sum + m.totalSignals, 0);
    const portfolioPnL = metrics.reduce((sum, m) => sum + m.totalPnL, 0);
    const totalCapitalAllocated = allocations.reduce((sum, a) => sum + a.allocatedCapital, 0);
    
    // Find best and worst performers
    const sortedByPnL = metrics.sort((a, b) => b.totalPnL - a.totalPnL);
    const bestPerformer = sortedByPnL[0]?.name || '';
    const worstPerformer = sortedByPnL[sortedByPnL.length - 1]?.name || '';
    
    this.metricsSubject.next({
      totalActiveStrategies,
      totalSignalsToday,
      portfolioPnL,
      bestPerformer,
      worstPerformer,
      totalCapitalAllocated,
      timestamp: Date.now()
    });
  }

  private startMetricsUpdates(): void {
    setInterval(() => {
      this.updateRealTimeMetrics();
    }, 10000); // Update every 10 seconds
  }

  private calculateSharpeRatio(strategyId: string): number {
    // Simplified Sharpe ratio calculation
    const metrics = this.performanceMetrics.get(strategyId);
    if (!metrics || metrics.totalSignals === 0) return 0;
    
    const avgReturn = metrics.totalPnL / metrics.totalSignals;
    const riskFreeRate = 0.05; // 5% risk-free rate
    const volatility = this.calculateVolatility(strategyId);
    
    return volatility > 0 ? (avgReturn - riskFreeRate) / volatility : 0;
  }

  private calculateMaxDrawdown(strategyId: string): number {
    // Simplified max drawdown calculation
    return Math.random() * 10; // 0-10% for simulation
  }

  private calculateAverageTradeSize(strategyId: string): number {
    const metrics = this.performanceMetrics.get(strategyId);
    if (!metrics || metrics.totalSignals === 0) return 0;
    
    return Math.abs(metrics.totalPnL) / metrics.totalSignals;
  }

  private calculateVolatility(strategyId: string): number {
    // Simplified volatility calculation
    return Math.random() * 0.3; // 0-30% for simulation
  }

  private calculateRiskScore(riskLevel: string): number {
    const riskMap = { LOW: 3, MEDIUM: 6, HIGH: 9 };
    return riskMap[riskLevel as keyof typeof riskMap] || 5;
  }

  // Public API methods
  async activateStrategy(strategyId: string, symbols: string[], allocation: number): Promise<string> {
    // Update portfolio allocation
    const portfolioAllocation = this.portfolioAllocations.get(strategyId);
    if (portfolioAllocation) {
      portfolioAllocation.allocatedCapital = allocation;
      this.portfolioAllocations.set(strategyId, portfolioAllocation);
    }
    
    // Activate strategy execution
    const executionId = await this.executionEngine.activateStrategy(strategyId, symbols, {
      riskLevel: 1,
      maxPositionSize: allocation * 0.1 // 10% of allocation per position
    });
    
    // Update metrics
    const metrics = this.performanceMetrics.get(strategyId);
    if (metrics) {
      metrics.isActive = true;
      this.performanceMetrics.set(strategyId, metrics);
    }
    
    return executionId;
  }

  async deactivateStrategy(strategyId: string): Promise<void> {
    await this.executionEngine.deactivateStrategy(strategyId);
    
    // Update metrics
    const metrics = this.performanceMetrics.get(strategyId);
    if (metrics) {
      metrics.isActive = false;
      this.performanceMetrics.set(strategyId, metrics);
    }
    
    // Reset portfolio allocation exposure
    const allocation = this.portfolioAllocations.get(strategyId);
    if (allocation) {
      allocation.currentExposure = 0;
      allocation.utilizationPercent = 0;
      this.portfolioAllocations.set(strategyId, allocation);
    }
  }

  getPerformanceMetrics(): StrategyPerformanceMetrics[] {
    return Array.from(this.performanceMetrics.values());
  }

  getPortfolioAllocations(): PortfolioAllocation[] {
    return Array.from(this.portfolioAllocations.values());
  }

  getRealTimeMetrics(): Observable<RealTimeMetrics> {
    return this.metricsSubject.asObservable();
  }

  getActiveExecutions(): StrategyExecution[] {
    return this.executionEngine.getActiveExecutions();
  }

  async getDetailedPerformance(strategyId: string): Promise<any> {
    const basePerformance = await this.executionEngine.getStrategyPerformance(strategyId);
    const metrics = this.performanceMetrics.get(strategyId);
    const allocation = this.portfolioAllocations.get(strategyId);
    
    return {
      ...basePerformance,
      ...metrics,
      allocation,
      riskMetrics: {
        sharpeRatio: metrics?.sharpeRatio || 0,
        maxDrawdown: metrics?.maxDrawdown || 0,
        volatility: this.calculateVolatility(strategyId),
        riskScore: allocation?.riskScore || 0
      }
    };
  }

  async rebalancePortfolio(newAllocations: Record<string, number>): Promise<void> {
    for (const [strategyId, allocation] of Object.entries(newAllocations)) {
      const portfolioAllocation = this.portfolioAllocations.get(strategyId);
      if (portfolioAllocation) {
        portfolioAllocation.allocatedCapital = allocation;
        portfolioAllocation.utilizationPercent = (Math.abs(portfolioAllocation.currentExposure) / allocation) * 100;
        this.portfolioAllocations.set(strategyId, portfolioAllocation);
      }
    }
    
    this.updateRealTimeMetrics();
  }

  async shutdown(): Promise<void> {
    await this.executionEngine.shutdown();
    await this.dataService.shutdown();
    console.log('RealStrategyIntegrationService shutdown complete');
  }
}

export const realStrategyIntegrationService = new RealStrategyIntegrationService();
