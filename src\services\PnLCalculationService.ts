export interface TradeResult {
  id: string;
  symbol: string;
  side: 'BUY' | 'SELL';
  quantity: number;
  entryPrice: number;
  exitPrice?: number;
  entryTime: Date;
  exitTime?: Date;
  realizedPnL?: number;
  unrealizedPnL?: number;
  fees: number;
  status: 'OPEN' | 'CLOSED' | 'PARTIALLY_FILLED';
}

export interface PnLBreakdown {
  totalPnL: number;
  realizedPnL: number;
  unrealizedPnL: number;
  tradingFees: number;
  netPnL: number;
  dayPnL: number;
  weekPnL: number;
  monthPnL: number;
  yearPnL: number;
  winRate: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  largestWin: number;
  largestLoss: number;
  maxConsecutiveWins: number;
  maxConsecutiveLosses: number;
}

export interface PositionPnL {
  symbol: string;
  quantity: number;
  averagePrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  realizedPnL: number;
  totalFees: number;
  dayChange: number;
  dayChangePercent: number;
}

export class PnLCalculationService {
  
  // Calculate unrealized P&L for a position
  static calculateUnrealizedPnL(
    quantity: number,
    averagePrice: number,
    currentPrice: number,
    side: 'LONG' | 'SHORT' = 'LONG'
  ): number {
    if (side === 'LONG') {
      return quantity * (currentPrice - averagePrice);
    } else {
      return quantity * (averagePrice - currentPrice);
    }
  }

  // Calculate realized P&L for a completed trade
  static calculateRealizedPnL(
    quantity: number,
    entryPrice: number,
    exitPrice: number,
    side: 'LONG' | 'SHORT' = 'LONG',
    fees: number = 0
  ): number {
    let pnl: number;
    
    if (side === 'LONG') {
      pnl = quantity * (exitPrice - entryPrice);
    } else {
      pnl = quantity * (entryPrice - exitPrice);
    }
    
    return pnl - fees;
  }

  // Calculate position-level P&L
  static calculatePositionPnL(
    symbol: string,
    trades: TradeResult[],
    currentPrice: number,
    previousClosePrice: number
  ): PositionPnL {
    const positionTrades = trades.filter(trade => trade.symbol === symbol);
    
    let totalQuantity = 0;
    let totalCost = 0;
    let realizedPnL = 0;
    let totalFees = 0;
    
    positionTrades.forEach(trade => {
      if (trade.status === 'CLOSED' && trade.realizedPnL !== undefined) {
        realizedPnL += trade.realizedPnL;
      } else if (trade.status === 'OPEN') {
        if (trade.side === 'BUY') {
          totalQuantity += trade.quantity;
          totalCost += trade.quantity * trade.entryPrice;
        } else {
          totalQuantity -= trade.quantity;
          totalCost -= trade.quantity * trade.entryPrice;
        }
      }
      totalFees += trade.fees;
    });
    
    const averagePrice = totalQuantity !== 0 ? Math.abs(totalCost / totalQuantity) : 0;
    const marketValue = totalQuantity * currentPrice;
    const unrealizedPnL = this.calculateUnrealizedPnL(
      totalQuantity,
      averagePrice,
      currentPrice,
      totalQuantity >= 0 ? 'LONG' : 'SHORT'
    );
    
    const unrealizedPnLPercent = averagePrice !== 0 ? (unrealizedPnL / (totalQuantity * averagePrice)) * 100 : 0;
    const dayChange = totalQuantity * (currentPrice - previousClosePrice);
    const dayChangePercent = previousClosePrice !== 0 ? ((currentPrice - previousClosePrice) / previousClosePrice) * 100 : 0;
    
    return {
      symbol,
      quantity: totalQuantity,
      averagePrice,
      currentPrice,
      marketValue: Math.abs(marketValue),
      unrealizedPnL,
      unrealizedPnLPercent,
      realizedPnL,
      totalFees,
      dayChange,
      dayChangePercent
    };
  }

  // Calculate portfolio-level P&L breakdown
  static calculatePortfolioPnL(
    trades: TradeResult[],
    positions: PositionPnL[],
    timeframe: 'day' | 'week' | 'month' | 'year' = 'day'
  ): PnLBreakdown {
    const now = new Date();
    const cutoffDate = this.getCutoffDate(now, timeframe);
    
    const filteredTrades = trades.filter(trade => {
      return trade.entryTime >= cutoffDate;
    });
    
    const closedTrades = filteredTrades.filter(trade => trade.status === 'CLOSED');
    
    const realizedPnL = closedTrades.reduce((sum, trade) => sum + (trade.realizedPnL || 0), 0);
    const unrealizedPnL = positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0);
    const tradingFees = filteredTrades.reduce((sum, trade) => sum + trade.fees, 0);
    const totalPnL = realizedPnL + unrealizedPnL;
    const netPnL = totalPnL - tradingFees;
    
    const dayPnL = this.calculateTimeframePnL(trades, positions, 'day');
    const weekPnL = this.calculateTimeframePnL(trades, positions, 'week');
    const monthPnL = this.calculateTimeframePnL(trades, positions, 'month');
    const yearPnL = this.calculateTimeframePnL(trades, positions, 'year');
    
    const winningTrades = closedTrades.filter(trade => (trade.realizedPnL || 0) > 0);
    const losingTrades = closedTrades.filter(trade => (trade.realizedPnL || 0) < 0);
    
    const winRate = closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) * 100 : 0;
    const averageWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, trade) => sum + (trade.realizedPnL || 0), 0) / winningTrades.length 
      : 0;
    const averageLoss = losingTrades.length > 0 
      ? Math.abs(losingTrades.reduce((sum, trade) => sum + (trade.realizedPnL || 0), 0) / losingTrades.length)
      : 0;
    
    const profitFactor = averageLoss !== 0 ? averageWin / averageLoss : 0;
    
    const largestWin = winningTrades.length > 0 
      ? Math.max(...winningTrades.map(trade => trade.realizedPnL || 0))
      : 0;
    const largestLoss = losingTrades.length > 0 
      ? Math.min(...losingTrades.map(trade => trade.realizedPnL || 0))
      : 0;
    
    const { maxConsecutiveWins, maxConsecutiveLosses } = this.calculateConsecutiveWinsLosses(closedTrades);
    
    return {
      totalPnL,
      realizedPnL,
      unrealizedPnL,
      tradingFees,
      netPnL,
      dayPnL,
      weekPnL,
      monthPnL,
      yearPnL,
      winRate,
      averageWin,
      averageLoss,
      profitFactor,
      largestWin,
      largestLoss,
      maxConsecutiveWins,
      maxConsecutiveLosses
    };
  }

  // Calculate timeframe-specific P&L
  private static calculateTimeframePnL(
    trades: TradeResult[],
    positions: PositionPnL[],
    timeframe: 'day' | 'week' | 'month' | 'year'
  ): number {
    const now = new Date();
    const cutoffDate = this.getCutoffDate(now, timeframe);
    
    const timeframeTrades = trades.filter(trade => 
      trade.status === 'CLOSED' && 
      trade.exitTime && 
      trade.exitTime >= cutoffDate
    );
    
    const realizedPnL = timeframeTrades.reduce((sum, trade) => sum + (trade.realizedPnL || 0), 0);
    
    if (timeframe === 'day') {
      const dayUnrealizedPnL = positions.reduce((sum, pos) => sum + pos.dayChange, 0);
      return realizedPnL + dayUnrealizedPnL;
    }
    
    return realizedPnL;
  }

  // Get cutoff date for timeframe
  private static getCutoffDate(now: Date, timeframe: 'day' | 'week' | 'month' | 'year'): Date {
    const cutoff = new Date(now);
    
    switch (timeframe) {
      case 'day':
        cutoff.setHours(0, 0, 0, 0);
        break;
      case 'week':
        cutoff.setDate(cutoff.getDate() - cutoff.getDay());
        cutoff.setHours(0, 0, 0, 0);
        break;
      case 'month':
        cutoff.setDate(1);
        cutoff.setHours(0, 0, 0, 0);
        break;
      case 'year':
        cutoff.setMonth(0, 1);
        cutoff.setHours(0, 0, 0, 0);
        break;
    }
    
    return cutoff;
  }

  // Calculate consecutive wins and losses
  private static calculateConsecutiveWinsLosses(trades: TradeResult[]): {
    maxConsecutiveWins: number,
    maxConsecutiveLosses: number
  } {
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentWins = 0;
    let currentLosses = 0;
    
    const sortedTrades = trades
      .filter(trade => trade.exitTime)
      .sort((a, b) => (a.exitTime!.getTime() - b.exitTime!.getTime()));
    
    sortedTrades.forEach(trade => {
      const pnl = trade.realizedPnL || 0;
      
      if (pnl > 0) {
        currentWins++;
        currentLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWins);
      } else if (pnl < 0) {
        currentLosses++;
        currentWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      }
    });
    
    return { maxConsecutiveWins, maxConsecutiveLosses };
  }

  // Calculate return on investment
  static calculateROI(initialCapital: number, currentValue: number): number {
    return initialCapital !== 0 ? ((currentValue - initialCapital) / initialCapital) * 100 : 0;
  }

  // Calculate compound annual growth rate (CAGR)
  static calculateCAGR(initialValue: number, finalValue: number, years: number): number {
    if (initialValue <= 0 || years <= 0) return 0;
    return (Math.pow(finalValue / initialValue, 1 / years) - 1) * 100;
  }

  // Calculate maximum adverse excursion (MAE) and maximum favorable excursion (MFE)
  static calculateMAEMFE(trades: TradeResult[]): {
    averageMAE: number,
    averageMFE: number,
    maxMAE: number,
    maxMFE: number
  } {
    return {
      averageMAE: 0,
      averageMFE: 0,
      maxMAE: 0,
      maxMFE: 0
    };
  }
}
