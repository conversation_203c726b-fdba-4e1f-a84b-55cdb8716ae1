
import { TechnicalIndicatorService } from './TechnicalIndicatorService';
import { RiskCalculationService, PortfolioRiskMetrics, PositionRisk } from './RiskCalculationService';
import { PnLCalculationService, PnLBreakdown, PositionPnL, TradeResult } from './PnLCalculationService';

export interface CalculationJob {
  id: string;
  type: 'technical' | 'risk' | 'pnl' | 'portfolio';
  priority: 'low' | 'medium' | 'high' | 'critical';
  data: any;
  callback: (result: any) => void;
  timestamp: number;
}

export interface CalculationResult {
  jobId: string;
  type: string;
  result: any;
  executionTime: number;
  timestamp: number;
}

export class CalculationEngineService {
  private static instance: CalculationEngineService;
  private jobQueue: CalculationJob[] = [];
  private isProcessing: boolean = false;
  private workers: number = 4;
  private resultCache: Map<string, CalculationResult> = new Map();
  private cacheTTL: number = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    this.startProcessing();
  }

  static getInstance(): CalculationEngineService {
    if (!CalculationEngineService.instance) {
      CalculationEngineService.instance = new CalculationEngineService();
    }
    return CalculationEngineService.instance;
  }

  // Add calculation job to queue
  addJob(job: Omit<CalculationJob, 'id' | 'timestamp'>): string {
    const jobId = `calc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const calculationJob: CalculationJob = {
      ...job,
      id: jobId,
      timestamp: Date.now()
    };

    // Check cache first
    const cacheKey = this.generateCacheKey(calculationJob);
    const cachedResult = this.resultCache.get(cacheKey);
    
    if (cachedResult && (Date.now() - cachedResult.timestamp) < this.cacheTTL) {
      // Return cached result immediately
      setTimeout(() => job.callback(cachedResult.result), 0);
      return jobId;
    }

    // Add to queue with priority sorting
    this.jobQueue.push(calculationJob);
    this.jobQueue.sort((a, b) => this.getPriorityValue(b.priority) - this.getPriorityValue(a.priority));

    return jobId;
  }

  // Process calculation queue
  private async startProcessing(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (true) {
      if (this.jobQueue.length === 0) {
        await this.sleep(100);
        continue;
      }

      const job = this.jobQueue.shift();
      if (!job) continue;

      try {
        const startTime = performance.now();
        const result = await this.executeJob(job);
        const executionTime = performance.now() - startTime;

        const calculationResult: CalculationResult = {
          jobId: job.id,
          type: job.type,
          result,
          executionTime,
          timestamp: Date.now()
        };

        // Cache result
        const cacheKey = this.generateCacheKey(job);
        this.resultCache.set(cacheKey, calculationResult);

        // Execute callback
        job.callback(result);

        console.log(`Calculation job ${job.id} completed in ${executionTime.toFixed(2)}ms`);

      } catch (error) {
        console.error(`Calculation job ${job.id} failed:`, error);
        job.callback({ error: error.message });
      }
    }
  }

  // Execute individual calculation job
  private async executeJob(job: CalculationJob): Promise<any> {
    switch (job.type) {
      case 'technical':
        return this.executeTechnicalCalculation(job.data);
      
      case 'risk':
        return this.executeRiskCalculation(job.data);
      
      case 'pnl':
        return this.executePnLCalculation(job.data);
      
      case 'portfolio':
        return this.executePortfolioCalculation(job.data);
      
      default:
        throw new Error(`Unknown calculation type: ${job.type}`);
    }
  }

  // Execute technical indicator calculations
  private async executeTechnicalCalculation(data: any): Promise<any> {
    const { indicator, prices, volumes, periods, ...params } = data;

    switch (indicator) {
      case 'sma':
        return TechnicalIndicatorService.calculateSMA(prices, periods.sma || 20);
      
      case 'ema':
        return TechnicalIndicatorService.calculateEMA(prices, periods.ema || 20);
      
      case 'rsi':
        return TechnicalIndicatorService.calculateRSI(prices, periods.rsi || 14);
      
      case 'macd':
        return TechnicalIndicatorService.calculateMACD(
          prices, 
          periods.fast || 12, 
          periods.slow || 26, 
          periods.signal || 9
        );
      
      case 'vwap':
        return TechnicalIndicatorService.calculateVWAP(prices, volumes, params.timestamps);
      
      case 'bollinger':
        return TechnicalIndicatorService.calculateBollingerBands(
          prices, 
          periods.bollinger || 20, 
          params.stdDev || 2
        );
      
      case 'volumeProfile':
        return TechnicalIndicatorService.calculateVolumeProfile(prices, volumes, params.bins || 50);
      
      default:
        throw new Error(`Unknown technical indicator: ${indicator}`);
    }
  }

  // Execute risk calculations
  private async executeRiskCalculation(data: any): Promise<any> {
    const { calculationType, ...params } = data;

    switch (calculationType) {
      case 'var':
        return RiskCalculationService.calculateVaR(params.returns, params.confidenceLevel);
      
      case 'expectedShortfall':
        return RiskCalculationService.calculateExpectedShortfall(params.returns, params.confidenceLevel);
      
      case 'maxDrawdown':
        return RiskCalculationService.calculateMaxDrawdown(params.portfolioValues);
      
      case 'sharpe':
        return RiskCalculationService.calculateSharpeRatio(params.returns, params.riskFreeRate);
      
      case 'beta':
        return RiskCalculationService.calculateBeta(params.assetReturns, params.marketReturns);
      
      case 'correlation':
        return RiskCalculationService.calculateCorrelationMatrix(params.returnsMatrix);
      
      case 'positionRisk':
        return RiskCalculationService.calculatePositionRisk(
          params.symbol,
          params.quantity,
          params.price,
          params.returns,
          params.marketReturns
        );
      
      case 'portfolioRisk':
        return RiskCalculationService.calculatePortfolioRisk(
          params.positions,
          params.portfolioReturns,
          params.marketReturns
        );
      
      case 'monteCarlo':
        return RiskCalculationService.monteCarloPortfolioSimulation(
          params.positions,
          params.simulations,
          params.timeHorizon
        );
      
      default:
        throw new Error(`Unknown risk calculation: ${calculationType}`);
    }
  }

  // Execute P&L calculations
  private async executePnLCalculation(data: any): Promise<any> {
    const { calculationType, ...params } = data;

    switch (calculationType) {
      case 'unrealized':
        return PnLCalculationService.calculateUnrealizedPnL(
          params.quantity,
          params.averagePrice,
          params.currentPrice,
          params.side
        );
      
      case 'realized':
        return PnLCalculationService.calculateRealizedPnL(
          params.quantity,
          params.entryPrice,
          params.exitPrice,
          params.side,
          params.fees
        );
      
      case 'position':
        return PnLCalculationService.calculatePositionPnL(
          params.symbol,
          params.trades,
          params.currentPrice,
          params.previousClosePrice
        );
      
      case 'portfolio':
        return PnLCalculationService.calculatePortfolioPnL(
          params.trades,
          params.positions,
          params.timeframe
        );
      
      case 'roi':
        return PnLCalculationService.calculateROI(params.initialCapital, params.currentValue);
      
      case 'cagr':
        return PnLCalculationService.calculateCAGR(
          params.initialValue,
          params.finalValue,
          params.years
        );
      
      default:
        throw new Error(`Unknown P&L calculation: ${calculationType}`);
    }
  }

  // Execute portfolio-level calculations
  private async executePortfolioCalculation(data: any): Promise<any> {
    const { positions, trades, marketData, ...params } = data;

    // Calculate position P&L for all positions
    const positionPnLs: PositionPnL[] = [];
    for (const position of positions) {
      const positionPnL = PnLCalculationService.calculatePositionPnL(
        position.symbol,
        trades.filter((t: TradeResult) => t.symbol === position.symbol),
        marketData[position.symbol]?.currentPrice || 0,
        marketData[position.symbol]?.previousClose || 0
      );
      positionPnLs.push(positionPnL);
    }

    // Calculate portfolio P&L
    const portfolioPnL = PnLCalculationService.calculatePortfolioPnL(trades, positionPnLs, params.timeframe);

    // Calculate portfolio risk if returns data is available
    let portfolioRisk: PortfolioRiskMetrics | null = null;
    if (params.portfolioReturns && params.marketReturns) {
      const positionRisks: PositionRisk[] = positions.map((pos: any) => 
        RiskCalculationService.calculatePositionRisk(
          pos.symbol,
          pos.quantity,
          pos.currentPrice,
          pos.returns || [],
          params.marketReturns
        )
      );

      portfolioRisk = RiskCalculationService.calculatePortfolioRisk(
        positionRisks,
        params.portfolioReturns,
        params.marketReturns
      );
    }

    return {
      positionPnLs,
      portfolioPnL,
      portfolioRisk
    };
  }

  // Generate cache key for calculation
  private generateCacheKey(job: CalculationJob): string {
    const dataHash = JSON.stringify(job.data);
    return `${job.type}_${dataHash}`;
  }

  // Get priority value for sorting
  private getPriorityValue(priority: string): number {
    switch (priority) {
      case 'critical': return 4;
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  }

  // Utility sleep function
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Clear cache
  clearCache(): void {
    this.resultCache.clear();
  }

  // Get queue status
  getQueueStatus(): {
    queueLength: number,
    isProcessing: boolean,
    cacheSize: number
  } {
    return {
      queueLength: this.jobQueue.length,
      isProcessing: this.isProcessing,
      cacheSize: this.resultCache.size
    };
  }

  // Clear old cache entries
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, result] of this.resultCache.entries()) {
      if (now - result.timestamp > this.cacheTTL) {
        this.resultCache.delete(key);
      }
    }
  }
}

// Export singleton instance
export const calculationEngine = CalculationEngineService.getInstance();
