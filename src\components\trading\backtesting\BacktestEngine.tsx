
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  PlayCircle, 
  StopCircle, 
  BarChart3, 
  Target,
  AlertCircle
} from "lucide-react";

interface BacktestResult {
  totalReturn?: number;
  totalReturnPercent?: number;
  maxDrawdown?: number;
  sharpeRatio?: number;
  totalTrades?: number;
  winRate?: number;
  avgWin?: number;
  avgLoss?: number;
  profitFactor?: number;
  startDate?: string;
  endDate?: string;
  initialCapital?: number;
  finalCapital?: number;
}

interface BacktestConfig {
  strategy: string;
  symbol: string;
  timeframe: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
}

interface BacktestEngineProps {
  onRunBacktest?: (config: BacktestConfig) => Promise<BacktestResult>;
  availableStrategies?: string[];
  availableSymbols?: string[];
  availableTimeframes?: string[];
  isRunning?: boolean;
  progress?: number;
  results?: BacktestResult | null;
  isLoading?: boolean;
}

export const BacktestEngine = ({
  onRunBacktest,
  availableStrategies = [],
  availableSymbols = [],
  availableTimeframes = [],
  isRunning = false,
  progress = 0,
  results = null,
  isLoading = false
}: BacktestEngineProps) => {
  const [strategy, setStrategy] = useState('');
  const [symbol, setSymbol] = useState('');
  const [timeframe, setTimeframe] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [initialCapital, setInitialCapital] = useState(100000);

  const runBacktest = async () => {
    if (!onRunBacktest) {
      console.log('No backtest handler provided');
      return;
    }

    const config: BacktestConfig = {
      strategy,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital
    };

    try {
      await onRunBacktest(config);
    } catch (error) {
      console.error('Backtest failed:', error);
    }
  };

  const stopBacktest = () => {
    console.log('Stop backtest requested');
  };

  const isConfigValid = strategy && symbol && timeframe && startDate && endDate;

  return (
    <ScrollArea className="h-[calc(100vh-200px)]">
      <div className="space-y-6 p-4">
        {/* Backtest Configuration */}
        <Card className="glassmorphism-card">
          <CardHeader>
            <CardTitle className="text-trading-light flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Backtest Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Show warning when no data available */}
            {(availableStrategies.length === 0 || availableSymbols.length === 0) && (
              <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mb-4">
                <div className="flex items-center text-amber-400">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  <span className="text-sm">
                    {availableStrategies.length === 0 && "No strategies loaded. "}
                    {availableSymbols.length === 0 && "No symbols available. "}
                    Please configure your data sources first.
                  </span>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="text-sm text-trading-muted mb-2 block">Strategy</label>
                <Select value={strategy} onValueChange={setStrategy} disabled={isLoading || availableStrategies.length === 0}>
                  <SelectTrigger className="bg-trading-dark border-trading-border">
                    <SelectValue placeholder={availableStrategies.length === 0 ? "No strategies available" : "Select strategy"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableStrategies.map((strat) => (
                      <SelectItem key={strat} value={strat}>
                        {strat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm text-trading-muted mb-2 block">Symbol</label>
                <Select value={symbol} onValueChange={setSymbol} disabled={isLoading || availableSymbols.length === 0}>
                  <SelectTrigger className="bg-trading-dark border-trading-border">
                    <SelectValue placeholder={availableSymbols.length === 0 ? "No symbols available" : "Select symbol"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSymbols.map((sym) => (
                      <SelectItem key={sym} value={sym}>
                        {sym}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm text-trading-muted mb-2 block">Timeframe</label>
                <Select value={timeframe} onValueChange={setTimeframe} disabled={isLoading || availableTimeframes.length === 0}>
                  <SelectTrigger className="bg-trading-dark border-trading-border">
                    <SelectValue placeholder={availableTimeframes.length === 0 ? "No timeframes available" : "Select timeframe"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTimeframes.map((tf) => (
                      <SelectItem key={tf} value={tf}>
                        {tf}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm text-trading-muted mb-2 block">Start Date</label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="bg-trading-dark border-trading-border"
                  disabled={isLoading}
                />
              </div>
              
              <div>
                <label className="text-sm text-trading-muted mb-2 block">End Date</label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="bg-trading-dark border-trading-border"
                  disabled={isLoading}
                />
              </div>
              
              <div>
                <label className="text-sm text-trading-muted mb-2 block">Initial Capital</label>
                <Input
                  type="number"
                  value={initialCapital}
                  onChange={(e) => setInitialCapital(Number(e.target.value))}
                  className="bg-trading-dark border-trading-border"
                  disabled={isLoading}
                />
              </div>
            </div>
            
            <div className="flex space-x-4">
              <Button 
                onClick={runBacktest} 
                disabled={isRunning || !isConfigValid || !onRunBacktest || isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                <PlayCircle className="h-4 w-4 mr-2" />
                Run Backtest
              </Button>
              
              {isRunning && (
                <Button onClick={stopBacktest} variant="destructive" disabled={isLoading}>
                  <StopCircle className="h-4 w-4 mr-2" />
                  Stop Backtest
                </Button>
              )}
            </div>
            
            {isRunning && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-trading-muted">Progress</span>
                  <span className="text-trading-light">{progress.toFixed(1)}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Backtest Results */}
        {results && (
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center justify-between">
                <span className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Backtest Results
                </span>
                <Badge 
                  variant="outline" 
                  className={
                    (results.totalReturnPercent ?? 0) >= 0 
                      ? 'text-green-400 border-green-400' 
                      : 'text-red-400 border-red-400'
                  }
                >
                  {(results.totalReturnPercent ?? 0) >= 0 ? '+' : ''}{(results.totalReturnPercent ?? 0).toFixed(2)}%
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Performance Metrics */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-trading-light">Performance</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Total Return</span>
                      <span className={`font-medium ${(results.totalReturn ?? 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ₹{(results.totalReturn ?? 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Return %</span>
                      <span className={`font-medium ${(results.totalReturnPercent ?? 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {(results.totalReturnPercent ?? 0) >= 0 ? '+' : ''}{(results.totalReturnPercent ?? 0).toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Max Drawdown</span>
                      <span className="text-red-400 font-medium">{(results.maxDrawdown ?? 0).toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Sharpe Ratio</span>
                      <span className="text-blue-400 font-medium">{(results.sharpeRatio ?? 0).toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Trade Statistics */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-trading-light">Trade Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Total Trades</span>
                      <span className="text-trading-light font-medium">{results.totalTrades ?? 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Win Rate</span>
                      <span className="text-green-400 font-medium">{(results.winRate ?? 0).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Avg Win</span>
                      <span className="text-green-400 font-medium">{(results.avgWin ?? 0).toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Avg Loss</span>
                      <span className="text-red-400 font-medium">{(results.avgLoss ?? 0).toFixed(2)}%</span>
                    </div>
                  </div>
                </div>

                {/* Risk Metrics */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-trading-light">Risk Metrics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Profit Factor</span>
                      <span className="text-purple-400 font-medium">{(results.profitFactor ?? 0).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Initial Capital</span>
                      <span className="text-trading-light">₹{(results.initialCapital ?? 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Final Capital</span>
                      <span className="text-trading-light">₹{(results.finalCapital ?? 0).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Period Info */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-trading-light">Period</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Start Date</span>
                      <span className="text-trading-light">{results.startDate ?? '--'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">End Date</span>
                      <span className="text-trading-light">{results.endDate ?? '--'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Strategy</span>
                      <span className="text-blue-400">{strategy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-trading-muted">Symbol</span>
                      <span className="text-purple-400">{symbol}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {!results && !isRunning && (
          <Card className="glassmorphism-card">
            <CardContent className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50 text-trading-muted" />
              <p className="text-trading-muted">
                {isLoading ? 'Loading backtest engine...' : 'No backtest results available'}
              </p>
              <p className="text-sm text-trading-muted mt-1">
                {isLoading ? 'Preparing backtest environment...' : 'Configure and run a backtest to see results'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </ScrollArea>
  );
};
