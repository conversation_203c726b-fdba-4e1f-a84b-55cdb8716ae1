
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Bell, AlertTriangle, TrendingUp, TrendingDown, Settings } from "lucide-react";
import { useState } from "react";

interface Alert {
  id: string;
  type: 'price' | 'volume' | 'technical' | 'news';
  symbol: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  timestamp: Date;
  isRead: boolean;
}

interface TradingAlertsProps {
  alerts?: Alert[];
  onMarkAsRead?: (id: string) => void;
  onToggleFilter?: (filter: 'all' | 'unread') => void;
}

export const TradingAlerts = ({ 
  alerts = [], 
  onMarkAsRead,
  onToggleFilter 
}: TradingAlertsProps) => {
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'price':
        return <TrendingUp className="h-4 w-4" />;
      case 'technical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'volume':
        return <TrendingDown className="h-4 w-4" />;
      case 'news':
        return <Bell className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: Alert['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-red-500 text-red-400';
      case 'medium':
        return 'border-yellow-500 text-yellow-400';
      case 'low':
        return 'border-blue-500 text-blue-400';
      default:
        return 'border-gray-500 text-gray-400';
    }
  };

  const handleMarkAsRead = (id: string) => {
    if (onMarkAsRead) {
      onMarkAsRead(id);
    }
  };

  const handleFilterChange = (newFilter: 'all' | 'unread') => {
    setFilter(newFilter);
    if (onToggleFilter) {
      onToggleFilter(newFilter);
    }
  };

  const filteredAlerts = filter === 'unread' 
    ? alerts.filter(alert => !alert.isRead)
    : alerts;

  const unreadCount = alerts.filter(alert => !alert.isRead).length;

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            Trading Alerts
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant={filter === 'all' ? 'secondary' : 'ghost'}
              onClick={() => handleFilterChange('all')}
              className="text-xs"
            >
              All
            </Button>
            <Button
              size="sm"
              variant={filter === 'unread' ? 'secondary' : 'ghost'}
              onClick={() => handleFilterChange('unread')}
              className="text-xs"
            >
              Unread
            </Button>
            <Button size="sm" variant="ghost" className="text-xs">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {filteredAlerts.length > 0 ? (
            filteredAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer hover:bg-trading-accent/10 ${
                  alert.isRead ? 'bg-trading-dark/50 opacity-70' : 'bg-trading-dark'
                } ${getPriorityColor(alert.priority)}`}
                onClick={() => handleMarkAsRead(alert.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-semibold text-trading-light">
                          {alert.symbol}
                        </span>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPriorityColor(alert.priority)}`}
                        >
                          {alert.type.toUpperCase()}
                        </Badge>
                      </div>
                      <p className="text-sm text-trading-muted">{alert.message}</p>
                      <div className="text-xs text-trading-muted mt-1">
                        {alert.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  {!alert.isRead && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-trading-muted">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No {filter === 'unread' ? 'unread ' : ''}alerts</p>
              <p className="text-sm mt-1">Connect to alert service to receive notifications</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
