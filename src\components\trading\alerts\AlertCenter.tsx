
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Bell, Mail, Phone, Globe, Settings, AlertTriangle } from "lucide-react";
import { useState } from "react";

interface AlertData {
  id: number;
  type: string;
  priority: string;
  title: string;
  message: string;
  time: string;
  symbol?: string;
}

interface AlertStats {
  todayAlerts: number;
  highPriority: number;
  signalsSent: number;
  responseTime: string;
}

interface AlertCenterProps {
  recentAlerts?: AlertData[];
  alertStats?: AlertStats;
  onSaveSettings?: (settings: any) => void;
}

export const AlertCenter = ({
  recentAlerts = [],
  alertStats,
  onSaveSettings
}: AlertCenterProps) => {
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [smsEnabled, setSmsEnabled] = useState(false);
  const [pushEnabled, setPushEnabled] = useState(true);
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [webhook, setWebhook] = useState('');

  const defaultStats = {
    todayAlerts: 0,
    highPriority: 0,
    signalsSent: 0,
    responseTime: '--'
  };

  const stats = alertStats || defaultStats;

  const displayStats = [
    { label: "Today's Alerts", value: stats.todayAlerts.toString(), color: "text-blue-400" },
    { label: "High Priority", value: stats.highPriority.toString(), color: "text-red-400" },
    { label: "Signals Sent", value: stats.signalsSent.toString(), color: "text-green-400" },
    { label: "Response Time", value: stats.responseTime, color: "text-purple-400" }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'text-red-400 border-red-400';
      case 'HIGH': return 'text-orange-400 border-orange-400';
      case 'MEDIUM': return 'text-yellow-400 border-yellow-400';
      case 'LOW': return 'text-blue-400 border-blue-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SIGNAL': return '📈';
      case 'RISK': return '⚠️';
      case 'EXECUTION': return '✅';
      case 'SYSTEM': return '🔧';
      default: return '📢';
    }
  };

  const handleSaveSettings = () => {
    const settings = {
      email: { enabled: emailEnabled, address: email },
      sms: { enabled: smsEnabled, phone },
      push: { enabled: pushEnabled },
      webhook
    };
    
    if (onSaveSettings) {
      onSaveSettings(settings);
    }
  };

  return (
    <div className="space-y-4">
      {/* Alert Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {displayStats.map((stat, index) => (
          <Card key={index} className="bg-trading-darker border-trading-border">
            <CardContent className="p-4 text-center">
              <div className={`text-2xl font-bold ${stat.color}`}>{stat.value}</div>
              <div className="text-xs text-trading-muted">{stat.label}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 bg-trading-darker">
          <TabsTrigger value="alerts" className="data-[state=active]:bg-trading-accent">
            <Bell className="h-4 w-4 mr-2" />
            Live Alerts
          </TabsTrigger>
          <TabsTrigger value="settings" className="data-[state=active]:bg-trading-accent">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="history" className="data-[state=active]:bg-trading-accent">
            <AlertTriangle className="h-4 w-4 mr-2" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="alerts">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Recent Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              {recentAlerts.length > 0 ? (
                <div className="space-y-3">
                  {recentAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-3">
                        <div className="text-lg">{getTypeIcon(alert.type)}</div>
                        <div>
                          <div className="font-medium text-trading-light">{alert.title}</div>
                          <div className="text-sm text-trading-muted">{alert.message}</div>
                          <div className="text-xs text-trading-muted">{alert.time}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getPriorityColor(alert.priority)}>
                          {alert.priority}
                        </Badge>
                        {alert.symbol && (
                          <Badge variant="outline" className="text-blue-400 border-blue-400">
                            {alert.symbol}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No recent alerts</p>
                  <p className="text-sm text-trading-muted mt-1">Alerts will appear when trading conditions are met</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Alert Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Notification Channels */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-trading-light">Notification Channels</h3>
                
                <div className="flex items-center justify-between p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-blue-400" />
                    <div>
                      <div className="font-medium text-trading-light">Email Alerts</div>
                      <div className="text-sm text-trading-muted">Receive alerts via email</div>
                    </div>
                  </div>
                  <Switch checked={emailEnabled} onCheckedChange={setEmailEnabled} />
                </div>

                <div className="flex items-center justify-between p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-green-400" />
                    <div>
                      <div className="font-medium text-trading-light">SMS Alerts</div>
                      <div className="text-sm text-trading-muted">Receive critical alerts via SMS</div>
                    </div>
                  </div>
                  <Switch checked={smsEnabled} onCheckedChange={setSmsEnabled} />
                </div>

                <div className="flex items-center justify-between p-4 bg-trading-dark rounded border border-trading-border">
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-purple-400" />
                    <div>
                      <div className="font-medium text-trading-light">Push Notifications</div>
                      <div className="text-sm text-trading-muted">Browser push notifications</div>
                    </div>
                  </div>
                  <Switch checked={pushEnabled} onCheckedChange={setPushEnabled} />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-trading-light">Contact Information</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-sm text-trading-muted">Email Address</label>
                    <Input 
                      placeholder="<EMAIL>" 
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-trading-dark border-trading-border text-trading-light"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm text-trading-muted">Phone Number</label>
                    <Input 
                      placeholder="+91 9876543210" 
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="bg-trading-dark border-trading-border text-trading-light"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm text-trading-muted">Webhook URL (Optional)</label>
                    <Input 
                      placeholder="https://your-webhook-url.com" 
                      value={webhook}
                      onChange={(e) => setWebhook(e.target.value)}
                      className="bg-trading-dark border-trading-border text-trading-light"
                    />
                  </div>
                </div>
              </div>

              {/* Alert Priorities */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-trading-light">Alert Priorities</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 bg-trading-dark rounded border border-trading-border">
                    <div className="text-sm font-medium text-trading-light">Trading Signals</div>
                    <div className="text-xs text-trading-muted">HIGH priority by default</div>
                  </div>
                  
                  <div className="p-3 bg-trading-dark rounded border border-trading-border">
                    <div className="text-sm font-medium text-trading-light">Risk Alerts</div>
                    <div className="text-xs text-trading-muted">CRITICAL priority</div>
                  </div>
                  
                  <div className="p-3 bg-trading-dark rounded border border-trading-border">
                    <div className="text-sm font-medium text-trading-light">Order Updates</div>
                    <div className="text-xs text-trading-muted">MEDIUM priority</div>
                  </div>
                  
                  <div className="p-3 bg-trading-dark rounded border border-trading-border">
                    <div className="text-sm font-medium text-trading-light">System Status</div>
                    <div className="text-xs text-trading-muted">LOW priority</div>
                  </div>
                </div>
              </div>

              <Button className="w-full" onClick={handleSaveSettings}>Save Alert Settings</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Alert History (Last 24h)</CardTitle>
            </CardHeader>
            <CardContent>
              {recentAlerts.length > 0 ? (
                <div className="space-y-2">
                  {recentAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-2 bg-trading-dark rounded text-sm">
                      <div className="flex items-center space-x-2">
                        <div>{getTypeIcon(alert.type)}</div>
                        <div className="text-trading-light">{alert.title}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getPriorityColor(alert.priority)}>
                          {alert.priority}
                        </Badge>
                        <div className="text-trading-muted text-xs">{alert.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No alert history available</p>
                  <p className="text-sm text-trading-muted mt-1">History will appear as alerts are generated</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
