
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Server, 
  Database, 
  Wifi, 
  Activity,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from "lucide-react";

export const CompactSystemHealth = () => {
  const systemMetrics = [
    { name: "Database", status: "online", latency: "12ms", icon: Database },
    { name: "Broker API", status: "connected", latency: "18ms", icon: Wifi },
    { name: "ETL Pipeline", status: "running", rate: "1.2K/s", icon: Activity },
    { name: "Trading Engine", status: "active", positions: "23", icon: Server }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'connected':
      case 'running':
      case 'active':
        return <CheckCircle className="h-3 w-3 text-green-400" />;
      default:
        return <AlertCircle className="h-3 w-3 text-red-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'connected':
      case 'running':
      case 'active':
        return 'text-green-400 border-green-400';
      default:
        return 'text-red-400 border-red-400';
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-trading-light flex items-center">
          <Server className="h-4 w-4 mr-2" />
          System Health
          <Badge variant="outline" className="ml-2 text-xs text-green-400 border-green-400">
            All Systems Operational
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {systemMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <div key={index} className="flex items-center space-x-2 p-2 bg-trading-dark rounded">
                <Icon className="h-4 w-4 text-blue-400" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(metric.status)}
                    <span className="text-xs text-trading-light truncate">{metric.name}</span>
                  </div>
                  <div className="text-xs text-trading-muted">
                    {metric.latency || metric.rate || `${metric.positions} pos`}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
