
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Bar<PERSON><PERSON>, TrendingUp, Play, Import, Download } from "lucide-react";

export const BacktestDashboard = () => {
  return (
    <div className="space-y-4">
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <BarChart className="h-5 w-5 mr-2" />
            Strategy Backtesting Suite
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-sm text-trading-muted">Active Backtests</div>
                <div className="text-2xl font-bold text-blue-400">0</div>
                <div className="text-xs text-trading-muted">None Running</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">Strategies</div>
                <div className="text-2xl font-bold text-purple-400">0</div>
                <div className="text-xs text-trading-muted">Ready to test</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">Completed Tests</div>
                <div className="text-2xl font-bold text-green-400">0</div>
                <div className="text-xs text-trading-muted">No results yet</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-trading-muted">Total Results</div>
                <div className="text-2xl font-bold text-trading-light">0</div>
                <div className="text-xs text-trading-muted">No data available</div>
              </div>
            </div>
          </div>

          <div className="flex space-x-2 mb-4">
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Play className="h-4 w-4 mr-2" />
              New Backtest
            </Button>
            <Button variant="outline" size="sm">
              <Import className="h-4 w-4 mr-2" />
              Import Strategy
            </Button>
            <Button variant="outline" size="sm" disabled>
              <Download className="h-4 w-4 mr-2" />
              Export Results
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light">Backtest Results</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px] w-full">
            <div className="text-center py-8">
              <BarChart className="h-12 w-12 mx-auto mb-4 opacity-50 text-trading-muted" />
              <p className="text-trading-muted text-lg">No backtest results available</p>
              <p className="text-sm text-trading-muted mt-2">
                Run your first backtest to see performance metrics and analysis
              </p>
              <Button className="mt-4 bg-blue-600 hover:bg-blue-700">
                <Play className="h-4 w-4 mr-2" />
                Start Backtesting
              </Button>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Performance Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[300px] w-full">
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50 text-trading-muted" />
              <div className="text-lg text-trading-muted">Performance Charts</div>
              <div className="text-sm text-trading-muted mt-1">
                Strategy comparison and analysis will appear here
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
