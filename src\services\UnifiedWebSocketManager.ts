import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface WebSocketConnectionConfig {
  url: string;
  protocols?: string[];
  heartbeatInterval?: number;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  enableCompression?: boolean;
  enableAuth?: boolean;
  authToken?: string;
}

export interface WebSocketMessage {
  type: string;
  symbol?: string;
  data?: any;
  timestamp?: number;
  sequence?: number;
  channel?: string;
}

export interface ConnectionState {
  isConnected: boolean;
  reconnectAttempts: number;
  lastError?: string;
  latency: number;
  connectionId: string;
}

export class UnifiedWebSocketManager {
  private connections: Map<string, WebSocket> = new Map();
  private connectionStates: Map<string, BehaviorSubject<ConnectionState>> = new Map();
  private messageSubjects: Map<string, Subject<WebSocketMessage>> = new Map();
  private heartbeatIntervals: Map<string, any> = new Map();
  private reconnectTimeouts: Map<string, any> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map(); // connectionId -> channels
  private channelSubscriptions: Map<string, Set<string>> = new Map(); // channel -> connectionIds

  private static instance: UnifiedWebSocketManager;

  private constructor() {}

  static getInstance(): UnifiedWebSocketManager {
    if (!UnifiedWebSocketManager.instance) {
      UnifiedWebSocketManager.instance = new UnifiedWebSocketManager();
    }
    return UnifiedWebSocketManager.instance;
  }

  async createConnection(connectionId: string, config: WebSocketConnectionConfig): Promise<void> {
    if (this.connections.has(connectionId)) {
      console.warn(`Connection ${connectionId} already exists`);
      return;
    }

    try {
      const ws = new WebSocket(config.url, config.protocols);
      
      // Initialize state
      const stateSubject = new BehaviorSubject<ConnectionState>({
        isConnected: false,
        reconnectAttempts: 0,
        latency: 0,
        connectionId
      });

      const messageSubject = new Subject<WebSocketMessage>();

      this.connections.set(connectionId, ws);
      this.connectionStates.set(connectionId, stateSubject);
      this.messageSubjects.set(connectionId, messageSubject);
      this.subscriptions.set(connectionId, new Set());

      // Setup event handlers
      ws.onopen = () => this.handleOpen(connectionId, config);
      ws.onmessage = (event) => this.handleMessage(connectionId, event);
      ws.onclose = (event) => this.handleClose(connectionId, event, config);
      ws.onerror = (error) => this.handleError(connectionId, error, config);

      console.log(`WebSocket connection ${connectionId} created`);
    } catch (error) {
      console.error(`Failed to create WebSocket connection ${connectionId}:`, error);
      throw error;
    }
  }

  private handleOpen(connectionId: string, config: WebSocketConnectionConfig): void {
    console.log(`WebSocket ${connectionId} connected`);
    
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject) {
      stateSubject.next({
        ...stateSubject.value,
        isConnected: true,
        reconnectAttempts: 0,
        lastError: undefined
      });
    }

    // Send authentication if required
    if (config.enableAuth && config.authToken) {
      this.sendMessage(connectionId, {
        type: 'auth',
        data: { token: config.authToken }
      });
    }

    // Start heartbeat
    if (config.heartbeatInterval) {
      this.startHeartbeat(connectionId, config.heartbeatInterval);
    }

    // Resubscribe to channels
    this.resubscribeChannels(connectionId);
  }

  private handleMessage(connectionId: string, event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      message.timestamp = Date.now();

      // Update latency if it's a pong message
      if (message.type === 'pong') {
        this.updateLatency(connectionId, message.timestamp);
        return;
      }

      // Emit message to subscribers
      const messageSubject = this.messageSubjects.get(connectionId);
      if (messageSubject) {
        messageSubject.next(message);
      }

    } catch (error) {
      console.error(`Error parsing WebSocket message for ${connectionId}:`, error);
    }
  }

  private handleClose(connectionId: string, event: CloseEvent, config: WebSocketConnectionConfig): void {
    console.log(`WebSocket ${connectionId} disconnected:`, event.reason);
    
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject) {
      stateSubject.next({
        ...stateSubject.value,
        isConnected: false,
        lastError: event.reason || 'Connection closed'
      });
    }

    this.stopHeartbeat(connectionId);
    this.scheduleReconnect(connectionId, config);
  }

  private handleError(connectionId: string, error: Event, config: WebSocketConnectionConfig): void {
    console.error(`WebSocket ${connectionId} error:`, error);
    
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject) {
      stateSubject.next({
        ...stateSubject.value,
        isConnected: false,
        lastError: 'WebSocket error'
      });
    }

    this.scheduleReconnect(connectionId, config);
  }

  private scheduleReconnect(connectionId: string, config: WebSocketConnectionConfig): void {
    const stateSubject = this.connectionStates.get(connectionId);
    if (!stateSubject) return;

    const currentState = stateSubject.value;
    if (currentState.reconnectAttempts >= (config.maxReconnectAttempts || 10)) {
      console.error(`Max reconnect attempts reached for ${connectionId}`);
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, currentState.reconnectAttempts), 30000);
    
    const timeoutId = setTimeout(() => {
      console.log(`Attempting to reconnect ${connectionId}...`);
      
      stateSubject.next({
        ...currentState,
        reconnectAttempts: currentState.reconnectAttempts + 1
      });

      this.reconnect(connectionId, config);
    }, delay);

    this.reconnectTimeouts.set(connectionId, timeoutId);
  }

  private async reconnect(connectionId: string, config: WebSocketConnectionConfig): Promise<void> {
    // Clean up existing connection
    this.closeConnection(connectionId, false);
    
    // Create new connection
    await this.createConnection(connectionId, config);
  }

  private startHeartbeat(connectionId: string, interval: number): void {
    const intervalId = setInterval(() => {
      this.sendMessage(connectionId, {
        type: 'ping',
        timestamp: Date.now()
      });
    }, interval);

    this.heartbeatIntervals.set(connectionId, intervalId);
  }

  private stopHeartbeat(connectionId: string): void {
    const intervalId = this.heartbeatIntervals.get(connectionId);
    if (intervalId) {
      clearInterval(intervalId);
      this.heartbeatIntervals.delete(connectionId);
    }
  }

  private updateLatency(connectionId: string, timestamp: number): void {
    const latency = Date.now() - timestamp;
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject) {
      stateSubject.next({
        ...stateSubject.value,
        latency
      });
    }
  }

  private resubscribeChannels(connectionId: string): void {
    const channels = this.subscriptions.get(connectionId);
    if (channels) {
      channels.forEach(channel => {
        this.sendMessage(connectionId, {
          type: 'subscribe',
          channel,
          data: { channel }
        });
      });
    }
  }

  // Public API methods
  sendMessage(connectionId: string, message: WebSocketMessage): boolean {
    const ws = this.connections.get(connectionId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
      return true;
    } else {
      console.warn(`Cannot send message to ${connectionId}: connection not open`);
      return false;
    }
  }

  subscribeToChannel(connectionId: string, channel: string): void {
    // Add to connection subscriptions
    const connectionChannels = this.subscriptions.get(connectionId) || new Set();
    connectionChannels.add(channel);
    this.subscriptions.set(connectionId, connectionChannels);

    // Add to channel subscriptions
    const channelConnections = this.channelSubscriptions.get(channel) || new Set();
    channelConnections.add(connectionId);
    this.channelSubscriptions.set(channel, channelConnections);

    // Send subscription message if connected
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject?.value.isConnected) {
      this.sendMessage(connectionId, {
        type: 'subscribe',
        channel,
        data: { channel }
      });
    }
  }

  unsubscribeFromChannel(connectionId: string, channel: string): void {
    // Remove from connection subscriptions
    const connectionChannels = this.subscriptions.get(connectionId);
    if (connectionChannels) {
      connectionChannels.delete(channel);
    }

    // Remove from channel subscriptions
    const channelConnections = this.channelSubscriptions.get(channel);
    if (channelConnections) {
      channelConnections.delete(connectionId);
      if (channelConnections.size === 0) {
        this.channelSubscriptions.delete(channel);
      }
    }

    // Send unsubscription message if connected
    const stateSubject = this.connectionStates.get(connectionId);
    if (stateSubject?.value.isConnected) {
      this.sendMessage(connectionId, {
        type: 'unsubscribe',
        channel,
        data: { channel }
      });
    }
  }

  getMessageStream(connectionId: string): Observable<WebSocketMessage> {
    const messageSubject = this.messageSubjects.get(connectionId);
    return messageSubject ? messageSubject.asObservable() : new Observable();
  }

  getChannelStream(connectionId: string, channel: string): Observable<WebSocketMessage> {
    return this.getMessageStream(connectionId).pipe(
      filter(message => message.channel === channel || message.type === channel)
    );
  }

  getConnectionState(connectionId: string): Observable<ConnectionState> {
    const stateSubject = this.connectionStates.get(connectionId);
    return stateSubject ? stateSubject.asObservable() : new Observable();
  }

  isConnected(connectionId: string): boolean {
    const stateSubject = this.connectionStates.get(connectionId);
    return stateSubject?.value.isConnected || false;
  }

  closeConnection(connectionId: string, removeFromMaps: boolean = true): void {
    // Clear timeouts
    const reconnectTimeout = this.reconnectTimeouts.get(connectionId);
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      this.reconnectTimeouts.delete(connectionId);
    }

    // Stop heartbeat
    this.stopHeartbeat(connectionId);

    // Close WebSocket
    const ws = this.connections.get(connectionId);
    if (ws) {
      ws.close();
    }

    if (removeFromMaps) {
      // Clean up maps
      this.connections.delete(connectionId);
      this.connectionStates.delete(connectionId);
      this.messageSubjects.delete(connectionId);
      this.subscriptions.delete(connectionId);

      // Remove from channel subscriptions
      this.channelSubscriptions.forEach((connections, channel) => {
        connections.delete(connectionId);
        if (connections.size === 0) {
          this.channelSubscriptions.delete(channel);
        }
      });
    }

    console.log(`WebSocket connection ${connectionId} closed`);
  }

  closeAllConnections(): void {
    const connectionIds = Array.from(this.connections.keys());
    connectionIds.forEach(connectionId => {
      this.closeConnection(connectionId);
    });
  }

  getActiveConnections(): string[] {
    return Array.from(this.connections.keys()).filter(connectionId => 
      this.isConnected(connectionId)
    );
  }

  getConnectionStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    this.connectionStates.forEach((stateSubject, connectionId) => {
      const state = stateSubject.value;
      stats[connectionId] = {
        isConnected: state.isConnected,
        latency: state.latency,
        reconnectAttempts: state.reconnectAttempts,
        subscribedChannels: Array.from(this.subscriptions.get(connectionId) || [])
      };
    });

    return stats;
  }
}

// Export singleton instance
export const unifiedWebSocketManager = UnifiedWebSocketManager.getInstance();
