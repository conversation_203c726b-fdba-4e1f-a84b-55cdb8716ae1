
import { <PERSON>, <PERSON>, Monitor, <PERSON>pt<PERSON>, SunMoon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTheme } from './enhanced-theme-provider';

interface ThemeToggleProps {
  variant?: 'default' | 'minimal' | 'text';
  showLabel?: boolean;
  align?: 'start' | 'center' | 'end';
}

export function EnhancedThemeToggle({ 
  variant = 'default', 
  showLabel = false,
  align = 'end' 
}: ThemeToggleProps) {
  const { theme, resolvedTheme, setTheme, toggleTheme, isSystemTheme } = useTheme();

  if (variant === 'minimal') {
    return (
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleTheme}
        className="interactive-hover glass-card h-9 w-9"
        aria-label="Toggle theme"
      >
        <Sun className="h-4 w-4 rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0" />
        <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size={showLabel ? "default" : "icon"} 
          className="interactive-hover glass-card"
        >
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100" />
          {showLabel && (
            <span className="ml-2 capitalize">
              {isSystemTheme ? 'auto' : resolvedTheme}
            </span>
          )}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align={align} 
        className="glass-card border-trading-border/50 animate-scale-in"
      >
        <DropdownMenuItem 
          onClick={() => setTheme('light')}
          className={`cursor-pointer ${theme === 'light' ? 'bg-trading-accent' : ''}`}
        >
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
          {theme === 'light' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-trading-primary animate-pulse-glow" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => setTheme('dark')}
          className={`cursor-pointer ${theme === 'dark' ? 'bg-trading-accent' : ''}`}
        >
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
          {theme === 'dark' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-trading-primary animate-pulse-glow" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuSeparator className="bg-trading-border/50" />
        
        <DropdownMenuItem 
          onClick={() => setTheme('system')}
          className={`cursor-pointer ${theme === 'system' ? 'bg-trading-accent' : ''}`}
        >
          <Monitor className="mr-2 h-4 w-4" />
          <span>System</span>
          {theme === 'system' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-trading-primary animate-pulse-glow" />
          )}
        </DropdownMenuItem>
        
        {/* Theme Preview */}
        <DropdownMenuSeparator className="bg-trading-border/50" />
        
        <div className="px-2 py-1">
          <div className="text-xs text-trading-muted mb-2">Preview</div>
          <div className="flex space-x-2">
            <div className="flex-1 h-8 rounded border border-gray-200 bg-white" />
            <div className="flex-1 h-8 rounded border border-gray-800 bg-gray-900" />
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact theme indicator for status bars
export function ThemeIndicator() {
  const { resolvedTheme, isSystemTheme } = useTheme();
  
  return (
    <div className="flex items-center space-x-1 text-xs text-trading-muted">
      {resolvedTheme === 'dark' ? (
        <Moon className="h-3 w-3" />
      ) : (
        <Sun className="h-3 w-3" />
      )}
      <span>{isSystemTheme ? 'Auto' : resolvedTheme}</span>
    </div>
  );
}

// Theme-aware component wrapper
export function ThemeAware({ 
  children, 
  lightComponent, 
  darkComponent 
}: {
  children?: React.ReactNode;
  lightComponent?: React.ReactNode;
  darkComponent?: React.ReactNode;
}) {
  const { resolvedTheme } = useTheme();
  
  if (lightComponent && darkComponent) {
    return resolvedTheme === 'light' ? lightComponent : darkComponent;
  }
  
  return children;
}
