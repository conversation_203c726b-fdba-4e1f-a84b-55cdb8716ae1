
import { ServiceManager } from './ServiceManager';

export class AppInitializer {
  private static instance: AppInitializer;
  private serviceManager: ServiceManager;
  private isInitialized = false;

  private constructor() {
    this.serviceManager = ServiceManager.getInstance();
  }

  static getInstance(): AppInitializer {
    if (!AppInitializer.instance) {
      AppInitializer.instance = new AppInitializer();
    }
    return AppInitializer.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('App already initialized');
      return;
    }

    console.log('Initializing Trading Platform...');
    
    try {
      // Initialize all services
      await this.serviceManager.initialize();
      
      // Set up global error handlers
      this.setupErrorHandlers();
      
      // Set up cleanup on page unload
      this.setupCleanup();
      
      this.isInitialized = true;
      console.log('Trading Platform initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Trading Platform:', error);
      throw error;
    }
  }

  private setupErrorHandlers(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      const alertService = this.serviceManager.getAlertService();
      if (alertService) {
        alertService.sendSystemAlert({
          component: 'Application',
          status: 'ERROR',
          message: `Unhandled error: ${event.error?.message || 'Unknown error'}`
        });
      }
    });

    // Unhandled promise rejection
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      const alertService = this.serviceManager.getAlertService();
      if (alertService) {
        alertService.sendSystemAlert({
          component: 'Application',
          status: 'ERROR',
          message: `Unhandled promise rejection: ${event.reason}`
        });
      }
    });
  }

  private setupCleanup(): void {
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      console.log('Shutting down Trading Platform...');
      this.serviceManager.shutdown();
    });

    // Cleanup on page visibility change (mobile/tab switching)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('Page hidden - maintaining minimal services');
      } else {
        console.log('Page visible - resuming full services');
      }
    });
  }

  getServiceManager(): ServiceManager {
    return this.serviceManager;
  }

  isAppInitialized(): boolean {
    return this.isInitialized;
  }
}

// Auto-initialize when imported
export const initializeApp = async (): Promise<void> => {
  const initializer = AppInitializer.getInstance();
  await initializer.initialize();
};
