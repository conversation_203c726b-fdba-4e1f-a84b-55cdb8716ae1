
import { useState, useEffect } from 'react';
import { realStrategyIntegrationService, StrategyPerformanceMetrics, PortfolioAllocation, RealTimeMetrics } from '../services/RealStrategyIntegrationService';
import { StrategyExecution } from '../services/StrategyExecutionEngine';

export const useStrategyIntegration = () => {
  const [performanceMetrics, setPerformanceMetrics] = useState<StrategyPerformanceMetrics[]>([]);
  const [portfolioAllocations, setPortfolioAllocations] = useState<PortfolioAllocation[]>([]);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [activeExecutions, setActiveExecutions] = useState<StrategyExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeService = async () => {
      try {
        await realStrategyIntegrationService.initialize();
        
        // Load initial data
        setPerformanceMetrics(realStrategyIntegrationService.getPerformanceMetrics());
        setPortfolioAllocations(realStrategyIntegrationService.getPortfolioAllocations());
        setActiveExecutions(realStrategyIntegrationService.getActiveExecutions());
        
        // Subscribe to real-time updates
        const subscription = realStrategyIntegrationService.getRealTimeMetrics().subscribe({
          next: (metrics) => setRealTimeMetrics(metrics),
          error: (err) => setError(err.message)
        });
        
        // Set up periodic updates for other data
        const interval = setInterval(() => {
          setPerformanceMetrics(realStrategyIntegrationService.getPerformanceMetrics());
          setPortfolioAllocations(realStrategyIntegrationService.getPortfolioAllocations());
          setActiveExecutions(realStrategyIntegrationService.getActiveExecutions());
        }, 5000);
        
        setIsLoading(false);
        
        return () => {
          subscription.unsubscribe();
          clearInterval(interval);
        };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize strategy integration');
        setIsLoading(false);
      }
    };

    initializeService();
  }, []);

  const activateStrategy = async (strategyId: string, symbols: string[], allocation: number) => {
    try {
      const executionId = await realStrategyIntegrationService.activateStrategy(strategyId, symbols, allocation);
      setPerformanceMetrics(realStrategyIntegrationService.getPerformanceMetrics());
      setActiveExecutions(realStrategyIntegrationService.getActiveExecutions());
      return executionId;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to activate strategy');
      throw err;
    }
  };

  const deactivateStrategy = async (strategyId: string) => {
    try {
      await realStrategyIntegrationService.deactivateStrategy(strategyId);
      setPerformanceMetrics(realStrategyIntegrationService.getPerformanceMetrics());
      setActiveExecutions(realStrategyIntegrationService.getActiveExecutions());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to deactivate strategy');
      throw err;
    }
  };

  const getDetailedPerformance = async (strategyId: string) => {
    try {
      return await realStrategyIntegrationService.getDetailedPerformance(strategyId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get detailed performance');
      throw err;
    }
  };

  const rebalancePortfolio = async (newAllocations: Record<string, number>) => {
    try {
      await realStrategyIntegrationService.rebalancePortfolio(newAllocations);
      setPortfolioAllocations(realStrategyIntegrationService.getPortfolioAllocations());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to rebalance portfolio');
      throw err;
    }
  };

  return {
    performanceMetrics,
    portfolioAllocations,
    realTimeMetrics,
    activeExecutions,
    isLoading,
    error,
    activateStrategy,
    deactivateStrategy,
    getDetailedPerformance,
    rebalancePortfolio
  };
};
