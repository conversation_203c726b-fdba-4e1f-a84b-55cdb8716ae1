
import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { BarChart3, TrendingUp, Target, Activity, Zap, Brain } from "lucide-react";

export const StrategyPerformanceDashboard = () => {
  const strategyPerformance = [
    {
      name: "EMA Crossover",
      totalReturn: 23.4,
      winRate: 68.5,
      sharpeRatio: 1.82,
      maxDrawdown: -8.3,
      totalTrades: 147,
      profitFactor: 2.34,
      avgWin: 4.2,
      avgLoss: -2.1,
      status: "active",
      lastSignal: "BUY - 2 hours ago"
    },
    {
      name: "RSI Mean Reversion",
      totalReturn: 31.7,
      winRate: 72.1,
      sharpeRatio: 2.15,
      maxDrawdown: -5.7,
      totalTrades: 203,
      profitFactor: 2.89,
      avgWin: 3.8,
      avgLoss: -1.9,
      status: "active",
      lastSignal: "SELL - 45 min ago"
    },
    {
      name: "Bollinger Squeeze",
      totalReturn: 18.9,
      winRate: 65.3,
      sharpeRatio: 1.67,
      maxDrawdown: -12.1,
      totalTrades: 89,
      profitFactor: 1.98,
      avgWin: 5.4,
      avgLoss: -3.2,
      status: "paused",
      lastSignal: "HOLD - 1 day ago"
    },
    {
      name: "MACD Momentum",
      totalReturn: 27.2,
      winRate: 70.4,
      sharpeRatio: 1.94,
      maxDrawdown: -9.8,
      totalTrades: 156,
      profitFactor: 2.56,
      avgWin: 4.7,
      avgLoss: -2.3,
      status: "active",
      lastSignal: "BUY - 3 hours ago"
    },
    {
      name: "Volume Breakout",
      totalReturn: 35.6,
      winRate: 58.9,
      sharpeRatio: 2.31,
      maxDrawdown: -15.4,
      totalTrades: 78,
      profitFactor: 3.12,
      avgWin: 8.1,
      avgLoss: -4.2,
      status: "active",
      lastSignal: "HOLD - 6 hours ago"
    }
  ];

  const portfolioMetrics = {
    totalReturn: 156.8,
    totalReturnPercent: 28.5,
    sharpeRatio: 2.18,
    maxDrawdown: -11.2,
    winRate: 67.8,
    profitFactor: 2.47,
    avgMonthlyReturn: 2.4,
    volatility: 12.6,
    bestMonth: 8.9,
    worstMonth: -4.2
  };

  const riskMetrics = {
    var95: -2.8,
    expectedShortfall: -4.1,
    beta: 0.87,
    alpha: 0.024,
    correlation: 0.78,
    informationRatio: 1.45,
    sortino: 3.21,
    calmar: 2.54
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-600";
      case "paused": return "bg-yellow-600";
      case "stopped": return "bg-red-600";
      default: return "bg-gray-600";
    }
  };

  return (
    <div className="space-y-4">
      {/* Portfolio Overview */}
      <Card className="glassmorphism-card">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Strategy Portfolio Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-sm text-trading-muted">Total Return</div>
              <div className="text-2xl font-bold text-green-400">+{portfolioMetrics.totalReturnPercent}%</div>
              <div className="text-xs text-trading-muted">₹{portfolioMetrics.totalReturn}L</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Sharpe Ratio</div>
              <div className="text-2xl font-bold text-blue-400">{portfolioMetrics.sharpeRatio}</div>
              <div className="text-xs text-blue-400">Excellent</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Win Rate</div>
              <div className="text-2xl font-bold text-purple-400">{portfolioMetrics.winRate}%</div>
              <div className="text-xs text-trading-muted">Consistent</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Max Drawdown</div>
              <div className="text-2xl font-bold text-red-400">{portfolioMetrics.maxDrawdown}%</div>
              <div className="text-xs text-trading-muted">Controlled</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-trading-muted">Profit Factor</div>
              <div className="text-2xl font-bold text-green-400">{portfolioMetrics.profitFactor}</div>
              <div className="text-xs text-green-400">Strong</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="strategies" className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="strategies">Strategy Performance</TabsTrigger>
          <TabsTrigger value="risk">Risk Metrics</TabsTrigger>
          <TabsTrigger value="analytics">Advanced Analytics</TabsTrigger>
          <TabsTrigger value="optimization">AI Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="strategies" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Target className="h-5 w-5 mr-2" />
                Individual Strategy Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] w-full">
                <div className="space-y-4">
                  {strategyPerformance.map((strategy, index) => (
                    <div key={index} className="p-4 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg font-medium text-trading-light">{strategy.name}</span>
                          <Badge className={getStatusColor(strategy.status)}>
                            {strategy.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-trading-muted">Total Return</div>
                          <div className="text-lg font-bold text-green-400">+{strategy.totalReturn}%</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Win Rate</div>
                          <div className="text-sm font-bold text-blue-400">{strategy.winRate}%</div>
                          <Progress value={strategy.winRate} className="mt-1 h-1" />
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Sharpe</div>
                          <div className="text-sm font-bold text-purple-400">{strategy.sharpeRatio}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Max DD</div>
                          <div className="text-sm font-bold text-red-400">{strategy.maxDrawdown}%</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Trades</div>
                          <div className="text-sm font-bold text-trading-light">{strategy.totalTrades}</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-3">
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Profit Factor</div>
                          <div className="text-sm text-green-400">{strategy.profitFactor}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Avg Win</div>
                          <div className="text-sm text-green-400">+{strategy.avgWin}%</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-trading-muted">Avg Loss</div>
                          <div className="text-sm text-red-400">{strategy.avgLoss}%</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t border-trading-border">
                        <div className="text-sm text-trading-muted">
                          Last Signal: <span className="text-trading-light">{strategy.lastSignal}</span>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">View Details</Button>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">Optimize</Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  Risk Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">VaR (95%)</span>
                    <span className="text-red-400">{riskMetrics.var95}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Expected Shortfall</span>
                    <span className="text-red-400">{riskMetrics.expectedShortfall}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Beta</span>
                    <span className="text-blue-400">{riskMetrics.beta}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Alpha</span>
                    <span className="text-green-400">{riskMetrics.alpha}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Correlation</span>
                    <span className="text-purple-400">{riskMetrics.correlation}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glassmorphism-card">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Advanced Risk Ratios
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Information Ratio</span>
                    <span className="text-green-400">{riskMetrics.informationRatio}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Sortino Ratio</span>
                    <span className="text-blue-400">{riskMetrics.sortino}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Calmar Ratio</span>
                    <span className="text-purple-400">{riskMetrics.calmar}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Volatility</span>
                    <span className="text-yellow-400">{portfolioMetrics.volatility}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-trading-muted">Best Month</span>
                    <span className="text-green-400">+{portfolioMetrics.bestMonth}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                Advanced Performance Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-trading-light font-medium mb-3">Monthly Returns</h4>
                  <div className="space-y-2">
                    {[-4.2, 1.8, 3.4, 2.1, 8.9, -1.2, 2.7, 4.3, 1.9, 3.6, 2.8, 5.1].map((ret, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-trading-muted text-sm">
                          {new Date(2024, index).toLocaleDateString('en-US', { month: 'short' })}
                        </span>
                        <div className="flex items-center space-x-2">
                          <div className={`w-16 h-2 rounded ${ret > 0 ? 'bg-green-600' : 'bg-red-600'}`} 
                               style={{ width: `${Math.abs(ret) * 8}px` }} />
                          <span className={`text-sm ${ret > 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {ret > 0 ? '+' : ''}{ret}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-trading-light font-medium mb-3">Drawdown Analysis</h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-red-600/20 rounded border border-red-600/30">
                      <div className="text-sm text-red-400 font-medium">Worst Drawdown</div>
                      <div className="text-lg text-red-400">-11.2%</div>
                      <div className="text-xs text-trading-muted">Duration: 23 days</div>
                    </div>
                    <div className="p-3 bg-yellow-600/20 rounded border border-yellow-600/30">
                      <div className="text-sm text-yellow-400 font-medium">Avg Drawdown</div>
                      <div className="text-lg text-yellow-400">-3.4%</div>
                      <div className="text-xs text-trading-muted">Duration: 8 days</div>
                    </div>
                    <div className="p-3 bg-green-600/20 rounded border border-green-600/30">
                      <div className="text-sm text-green-400 font-medium">Recovery Time</div>
                      <div className="text-lg text-green-400">12 days</div>
                      <div className="text-xs text-trading-muted">Avg recovery</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <Card className="glassmorphism-card">
            <CardHeader>
              <CardTitle className="text-trading-light flex items-center">
                <Brain className="h-5 w-5 mr-2" />
                AI Strategy Optimization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-600/20 rounded border border-blue-600/30">
                  <div className="font-medium text-blue-400 mb-2">Optimization Recommendations</div>
                  <div className="space-y-2 text-sm text-trading-muted">
                    <div>• Increase position size for EMA Crossover by 15% to improve returns</div>
                    <div>• Reduce RSI threshold from 30/70 to 25/75 for better entry timing</div>
                    <div>• Add volume filter to Bollinger Squeeze to reduce false signals</div>
                    <div>• Implement dynamic stop-loss for Volume Breakout strategy</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-green-600/20 rounded border border-green-600/30">
                    <div className="font-medium text-green-400 mb-2">Expected Improvements</div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Return Enhancement</span>
                        <span className="text-green-400">+4.2%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Sharpe Improvement</span>
                        <span className="text-green-400">+0.34</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-trading-muted">Drawdown Reduction</span>
                        <span className="text-green-400">-2.1%</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 bg-purple-600/20 rounded border border-purple-600/30">
                    <div className="font-medium text-purple-400 mb-2">Machine Learning Insights</div>
                    <div className="space-y-2 text-sm text-trading-muted">
                      <div>• Market regime detection shows 87% accuracy</div>
                      <div>• Volatility clustering model improves position sizing</div>
                      <div>• Sentiment analysis adds 12% to win rate</div>
                      <div>• Portfolio rebalancing optimal at 5-day intervals</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <Button className="bg-green-600 hover:bg-green-700">
          <Brain className="h-4 w-4 mr-2" />
          Auto-Optimize All
        </Button>
        <Button variant="outline">
          <BarChart3 className="h-4 w-4 mr-2" />
          Export Report
        </Button>
        <Button variant="outline">
          <Target className="h-4 w-4 mr-2" />
          Custom Backtest
        </Button>
        <Button variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          Risk Analysis
        </Button>
      </div>
    </div>
  );
};
