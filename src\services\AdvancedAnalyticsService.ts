import { EnhancedDatabaseService } from './EnhancedDatabaseService';

export interface TechnicalIndicators {
  rsi: number;
  macd: { macd: number; signal: number; histogram: number };
  bollingerBands: { upper: number; middle: number; lower: number };
  ema: number;
  sma: number;
  vwap: number;
}

export interface TradingSignal {
  symbol: string;
  signal: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  strategy: string;
  timestamp: number;
}

export interface PredictionResult {
  symbol: string;
  direction: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  timeframe: string;
  strategy: string;
}

export interface VolumeAnalysis {
  averageVolume: number;
  volumeSpike: boolean;
  volumeTrend: 'INCREASING' | 'DECREASING' | 'STABLE';
  accumulation: boolean;
}

export class AdvancedAnalyticsService {
  private databaseService: EnhancedDatabaseService;

  constructor(databaseService: EnhancedDatabaseService) {
    this.databaseService = databaseService;
  }

  async calculateTechnicalIndicators(symbol: string, period: number = 14): Promise<TechnicalIndicators> {
    const data = await this.databaseService.query(
      'SELECT price, volume, timestamp FROM market_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT 50',
      [symbol]
    );
    
    const prices = data.map((row: any) => row.price);
    const volumes = data.map((row: any) => row.volume);
    
    const rsi = await this.calculateRSI(prices, period);
    const macd = await this.calculateMACD(prices);
    const bollingerBands = await this.calculateBollingerBands(prices);
    const ema = await this.calculateEMA(prices, 20);
    const sma = await this.calculateSMA(prices, 20);
    const vwap = await this.calculateVWAP(prices, volumes);
    
    return {
      rsi,
      macd,
      bollingerBands,
      ema,
      sma,
      vwap
    };
  }

  async calculateRSI(data: number[], period: number = 14): Promise<number> {
    if (data.length < period + 1) return 50;

    const changes = data.slice(1).map((price, i) => price - data[i]);
    const gains = changes.map(change => change > 0 ? change : 0);
    const losses = changes.map(change => change < 0 ? Math.abs(change) : 0);

    const avgGain = gains.slice(-period).reduce((sum, gain) => sum + gain, 0) / period;
    const avgLoss = losses.slice(-period).reduce((sum, loss) => sum + loss, 0) / period;

    if (avgLoss === 0) return 100;
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  async calculateMACD(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): Promise<{ macd: number; signal: number; histogram: number }> {
    const fastEMA = await this.calculateEMA(data, fastPeriod);
    const slowEMA = await this.calculateEMA(data, slowPeriod);
    
    const macdLine = fastEMA - slowEMA;
    
    // Calculate signal line (EMA of MACD line)
    const macdData = data.map((_, i) => {
      if (i < data.length - slowPeriod) {
        const slice = data.slice(i, i + slowPeriod);
        const fast = this.simpleEMA(slice, fastPeriod);
        const slow = this.simpleEMA(slice, slowPeriod);
        return fast - slow;
      }
      return 0;
    }).filter(val => val !== 0);
    
    const signalLine = this.simpleEMA(macdData, signalPeriod);
    const histogram = macdLine - signalLine;
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram
    };
  }

  private simpleEMA(data: number[], period: number): number {
    const k = 2 / (period + 1);
    let ema = data[0];
    
    for (let i = 1; i < data.length; i++) {
      ema = data[i] * k + ema * (1 - k);
    }
    
    return ema;
  }

  async calculateBollingerBands(data: number[], period: number = 20, multiplier: number = 2): Promise<{ upper: number; middle: number; lower: number }> {
    const sma = await this.calculateSMA(data, period);
    
    // Calculate standard deviation
    const squaredDifferences = data.slice(0, period).map(price => Math.pow(price - sma, 2));
    const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / period;
    const stdDev = Math.sqrt(variance);
    
    const upperBand = sma + (multiplier * stdDev);
    const lowerBand = sma - (multiplier * stdDev);
    
    return {
      upper: upperBand,
      middle: sma,
      lower: lowerBand
    };
  }

  async calculateEMA(data: number[], period: number): Promise<number> {
    if (data.length < period) return data[0];
    
    const k = 2 / (period + 1);
    let ema = data.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
    
    for (let i = period; i < data.length; i++) {
      ema = data[i] * k + ema * (1 - k);
    }
    
    return ema;
  }

  async calculateSMA(data: number[], period: number): Promise<number> {
    if (data.length < period) return data[0];
    return data.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
  }

  async calculateVWAP(prices: number[], volumes: number[]): Promise<number> {
    if (prices.length !== volumes.length || prices.length === 0) return 0;
    
    let volumeSum = 0;
    let priceVolumeSum = 0;
    
    for (let i = 0; i < prices.length; i++) {
      volumeSum += volumes[i];
      priceVolumeSum += prices[i] * volumes[i];
    }
    
    return volumeSum === 0 ? 0 : priceVolumeSum / volumeSum;
  }

  async getMarketData(symbol: string, period: string = '1d'): Promise<any[]> {
    try {
      const data = await this.databaseService.query(
        'SELECT * FROM market_data WHERE symbol = ? AND period = ? ORDER BY timestamp DESC LIMIT 100',
        [symbol, period]
      );
      return data;
    } catch (error) {
      console.error('Error fetching market data:', error);
      return [];
    }
  }

  async getHistoricalData(symbol: string, days: number = 30): Promise<any[]> {
    try {
      const data = await this.databaseService.query(
        'SELECT * FROM historical_data WHERE symbol = ? AND timestamp >= ? ORDER BY timestamp',
        [symbol, Date.now() - (days * 24 * 60 * 60 * 1000)]
      );
      return data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      return [];
    }
  }

  async getOrderFlowData(symbol: string): Promise<any[]> {
    try {
      const data = await this.databaseService.query(
        'SELECT * FROM order_flow WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1000',
        [symbol]
      );
      return data;
    } catch (error) {
      console.error('Error fetching order flow data:', error);
      return [];
    }
  }

  async generateTradingSignals(symbols: string[]): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];
    
    for (const symbol of symbols) {
      try {
        const marketData = await this.databaseService.query(
          'SELECT * FROM market_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT 50',
          [symbol]
        );
        
        if (marketData.length > 0) {
          const prediction = await this.generateMLPrediction(symbol);
          signals.push({
            symbol: prediction.symbol,
            signal: prediction.direction,
            confidence: prediction.confidence,
            strategy: prediction.strategy,
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.error(`Error generating signal for ${symbol}:`, error);
      }
    }
    
    return signals;
  }

  async generateMLPrediction(symbol: string): Promise<PredictionResult> {
    try {
      const historicalData = await this.databaseService.query(
        'SELECT * FROM historical_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT 100',
        [symbol]
      );
      
      // Simulate ML prediction
      return {
        symbol,
        direction: Math.random() > 0.5 ? 'BUY' : 'SELL',
        confidence: Math.random() * 100,
        timeframe: '1d',
        strategy: 'ML_LSTM'
      };
    } catch (error) {
      console.error('ML prediction failed:', error);
      return {
        symbol,
        direction: 'HOLD',
        confidence: 0,
        timeframe: '1d',
        strategy: 'ERROR'
      };
    }
  }

  async analyzeVolume(symbol: string, days: number = 30): Promise<VolumeAnalysis> {
    const data = await this.databaseService.query(
      'SELECT volume FROM market_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT ?',
      [symbol, days]
    );
    
    const volumes = data.map((row: any) => row.volume);
    const averageVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
    
    // Check for volume spike (today's volume > 2x average)
    const volumeSpike = volumes[0] > averageVolume * 2;
    
    // Determine volume trend
    const recentAvg = volumes.slice(0, 5).reduce((sum, vol) => sum + vol, 0) / 5;
    const olderAvg = volumes.slice(-5).reduce((sum, vol) => sum + vol, 0) / 5;
    
    let volumeTrend: 'INCREASING' | 'DECREASING' | 'STABLE';
    if (recentAvg > olderAvg * 1.2) {
      volumeTrend = 'INCREASING';
    } else if (recentAvg < olderAvg * 0.8) {
      volumeTrend = 'DECREASING';
    } else {
      volumeTrend = 'STABLE';
    }
    
    // Check for accumulation (increasing volume on up days)
    const priceData = await this.databaseService.query(
      'SELECT price FROM market_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT ?',
      [symbol, days]
    );
    
    const prices = priceData.map((row: any) => row.price);
    let upDayVolume = 0;
    let upDayCount = 0;
    
    for (let i = 1; i < prices.length; i++) {
      if (prices[i-1] > prices[i]) {
        upDayVolume += volumes[i-1];
        upDayCount++;
      }
    }
    
    const accumulation = upDayCount > 0 && (upDayVolume / upDayCount) > averageVolume;
    
    return {
      averageVolume,
      volumeSpike,
      volumeTrend,
      accumulation
    };
  }

  async predictPriceMovement(symbol: string): Promise<PredictionResult> {
    const indicators = await this.calculateTechnicalIndicators(symbol);
    const volumeAnalysis = await this.analyzeVolume(symbol);
    
    // Simple prediction logic based on indicators
    let buySignals = 0;
    let sellSignals = 0;
    
    // RSI signals
    if (indicators.rsi < 30) buySignals += 2;
    else if (indicators.rsi < 40) buySignals += 1;
    else if (indicators.rsi > 70) sellSignals += 2;
    else if (indicators.rsi > 60) sellSignals += 1;
    
    // MACD signals
    if (indicators.macd.histogram > 0 && indicators.macd.histogram > indicators.macd.signal) buySignals += 2;
    else if (indicators.macd.histogram < 0 && indicators.macd.histogram < indicators.macd.signal) sellSignals += 2;
    
    // Bollinger Bands signals
    const data = await this.databaseService.query(
      'SELECT price FROM market_data WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1',
      [symbol]
    );
    const currentPrice = data[0]?.price || 0;
    
    if (currentPrice < indicators.bollingerBands.lower) buySignals += 2;
    else if (currentPrice > indicators.bollingerBands.upper) sellSignals += 2;
    
    // Volume signals
    if (volumeAnalysis.volumeSpike && volumeAnalysis.volumeTrend === 'INCREASING') buySignals += 1;
    if (volumeAnalysis.accumulation) buySignals += 1;
    
    // Determine direction and confidence
    const totalSignals = buySignals + sellSignals;
    const direction = buySignals > sellSignals ? 'BUY' : 'SELL';
    const confidence = totalSignals === 0 ? 50 : (Math.max(buySignals, sellSignals) / totalSignals) * 100;
    
    return {
      symbol,
      direction,
      confidence,
      timeframe: '1-3 days',
      strategy: 'MULTI_INDICATOR'
    };
  }

  async backtest(symbol: string, strategy: string, startDate: string, endDate: string): Promise<{
    totalTrades: number;
    winRate: number;
    profitFactor: number;
    netProfit: number;
  }> {
    // Simplified backtesting implementation
    const data = await this.databaseService.query(
      'SELECT price, timestamp FROM market_data WHERE symbol = ? AND timestamp BETWEEN ? AND ? ORDER BY timestamp ASC',
      [symbol, new Date(startDate).getTime(), new Date(endDate).getTime()]
    );
    
    if (data.length < 30) {
      return {
        totalTrades: 0,
        winRate: 0,
        profitFactor: 0,
        netProfit: 0
      };
    }
    
    let position: 'NONE' | 'LONG' | 'SHORT' = 'NONE';
    let entryPrice = 0;
    let trades = [];
    
    for (let i = 20; i < data.length; i++) {
      const prices = data.slice(0, i).map((row: any) => row.price);
      const rsi = await this.calculateRSI(prices.reverse());
      
      // Simple RSI strategy
      if (strategy === 'RSI') {
        if (position === 'NONE' && rsi < 30) {
          position = 'LONG';
          entryPrice = data[i].price;
        } else if (position === 'LONG' && rsi > 70) {
          trades.push({
            profit: data[i].price - entryPrice,
            profitPercent: (data[i].price - entryPrice) / entryPrice * 100
          });
          position = 'NONE';
        }
      }
    }
    
    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => t.profit > 0).length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    
    const grossProfit = trades.filter(t => t.profit > 0).reduce((sum, t) => sum + t.profit, 0);
    const grossLoss = Math.abs(trades.filter(t => t.profit < 0).reduce((sum, t) => sum + t.profit, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;
    
    const netProfit = trades.reduce((sum, t) => sum + t.profit, 0);
    
    return {
      totalTrades,
      winRate,
      profitFactor,
      netProfit
    };
  }
}
