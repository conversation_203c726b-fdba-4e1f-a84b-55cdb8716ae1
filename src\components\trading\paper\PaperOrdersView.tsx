
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle, XCircle, Target } from "lucide-react";
import { PaperTradeOrder } from '@/services/PaperTradingService';

interface PaperOrdersViewProps {
  orders: PaperTradeOrder[];
}

export const PaperOrdersView: React.FC<PaperOrdersViewProps> = ({ orders }) => {
  if (orders.length === 0) {
    return (
      <Card className="bg-trading-darker border-trading-border">
        <CardContent className="text-center py-12">
          <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium text-trading-light mb-2">No Orders</h3>
          <p className="text-trading-muted">Your orders will appear here when placed</p>
        </CardContent>
      </Card>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'FILLED':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-400" />;
      case 'CANCELLED':
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-400" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      FILLED: 'text-green-400 border-green-400',
      PENDING: 'text-yellow-400 border-yellow-400',
      CANCELLED: 'text-red-400 border-red-400',
      REJECTED: 'text-red-400 border-red-400'
    } as const;
    
    return (
      <Badge variant="outline" className={variants[status as keyof typeof variants] || 'text-gray-400 border-gray-400'}>
        {status}
      </Badge>
    );
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <CardTitle className="text-trading-light">Orders History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {orders.map((order) => (
            <div key={order.id} className="p-4 bg-trading-dark rounded border border-trading-border">
              <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                {/* Order Details */}
                <div className="flex items-center space-x-3">
                  {getStatusIcon(order.status)}
                  <div>
                    <div className="font-medium text-trading-light">{order.symbol}</div>
                    <div className="text-xs text-trading-muted">
                      {order.instrumentType} • {order.strategy}
                    </div>
                  </div>
                </div>

                {/* Order Type & Side */}
                <div className="text-center">
                  <Badge variant="outline" className={order.type === "BUY" ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
                    {order.type}
                  </Badge>
                  <div className="text-xs text-trading-muted mt-1">
                    {order.orderType}
                  </div>
                </div>

                {/* Quantity & Price */}
                <div className="text-center">
                  <div className="text-sm text-trading-muted">Qty & Price</div>
                  <div className="text-trading-light font-medium">{order.quantity}</div>
                  <div className="text-xs text-trading-muted">@ ₹{order.price.toFixed(2)}</div>
                </div>

                {/* Fill Price */}
                <div className="text-center">
                  <div className="text-sm text-trading-muted">Fill Price</div>
                  <div className="text-trading-light font-medium">
                    {order.fillPrice ? `₹${order.fillPrice.toFixed(2)}` : '-'}
                  </div>
                </div>

                {/* Status */}
                <div className="text-center">
                  <div className="text-sm text-trading-muted">Status</div>
                  {getStatusBadge(order.status)}
                </div>

                {/* Timestamp */}
                <div className="text-center">
                  <div className="text-sm text-trading-muted">Time</div>
                  <div className="text-xs text-trading-light">
                    {order.timestamp.toLocaleTimeString()}
                  </div>
                  <div className="text-xs text-trading-muted">
                    {order.timestamp.toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* Option Details if applicable */}
              {order.optionDetails && (
                <div className="mt-3 pt-3 border-t border-trading-border">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                    <div>
                      <span className="text-trading-muted">Strike: </span>
                      <span className="text-trading-light">₹{order.optionDetails.strike}</span>
                    </div>
                    <div>
                      <span className="text-trading-muted">Type: </span>
                      <span className="text-trading-light">{order.optionDetails.optionType}</span>
                    </div>
                    <div>
                      <span className="text-trading-muted">Premium: </span>
                      <span className="text-trading-light">₹{order.optionDetails.premium.toFixed(2)}</span>
                    </div>
                    <div>
                      <span className="text-trading-muted">Expiry: </span>
                      <span className="text-trading-light">{order.optionDetails.expiry.toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
