
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from "recharts";
import { TrendingU<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as PieChartIcon, Calendar } from "lucide-react";

interface PerformanceData {
  name: string;
  swing?: number;
  intraday?: number;
  scalping?: number;
}

interface StrategyDistribution {
  name: string;
  value: number;
  color: string;
}

interface TopPerformer {
  strategy: string;
  category: 'Swing' | 'Intraday' | 'Scalping';
  winRate: number;
  pnl: number;
  trades: number;
}

interface PerformanceStats {
  totalPnL?: number;
  totalTrades?: number;
  avgWinRate?: number;
  sharpeRatio?: number;
  maxDrawdown?: number;
}

interface PerformanceAnalyticsProps {
  performanceData?: PerformanceData[];
  strategyDistribution?: StrategyDistribution[];
  topPerformers?: TopPerformer[];
  performanceStats?: PerformanceStats;
}

export const PerformanceAnalytics = ({
  performanceData = [],
  strategyDistribution = [],
  topPerformers = [],
  performanceStats = {}
}: PerformanceAnalyticsProps) => {
  const {
    totalPnL = 0,
    totalTrades = 0,
    avgWinRate = 0,
    sharpeRatio = 0,
    maxDrawdown = 0
  } = performanceStats;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Swing': return 'text-green-400 border-green-400';
      case 'Intraday': return 'text-blue-400 border-blue-400';
      case 'Scalping': return 'text-purple-400 border-purple-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <Card className="bg-trading-darker border-trading-border">
        <CardHeader>
          <CardTitle className="text-trading-light flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Strategy Performance Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {totalPnL ? `₹${totalPnL.toLocaleString()}` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Total P&L (6M)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {totalTrades ? totalTrades.toLocaleString() : '--'}
              </div>
              <div className="text-sm text-trading-muted">Total Trades</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {avgWinRate ? `${avgWinRate}%` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Avg Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {sharpeRatio ? sharpeRatio.toFixed(2) : '--'}
              </div>
              <div className="text-sm text-trading-muted">Sharpe Ratio</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-400">
                {maxDrawdown ? `${maxDrawdown}%` : '--'}
              </div>
              <div className="text-sm text-trading-muted">Max Drawdown</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="performance" className="data-[state=active]:bg-trading-accent">
            <TrendingUp className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="distribution" className="data-[state=active]:bg-trading-accent">
            <PieChartIcon className="h-4 w-4 mr-2" />
            Distribution
          </TabsTrigger>
          <TabsTrigger value="rankings" className="data-[state=active]:bg-trading-accent">
            <BarChart3 className="h-4 w-4 mr-2" />
            Rankings
          </TabsTrigger>
          <TabsTrigger value="calendar" className="data-[state=active]:bg-trading-accent">
            <Calendar className="h-4 w-4 mr-2" />
            Calendar
          </TabsTrigger>
        </TabsList>

        <TabsContent value="performance">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Monthly P&L by Category</CardTitle>
            </CardHeader>
            <CardContent>
              {performanceData.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="name" stroke="#9ca3af" />
                    <YAxis stroke="#9ca3af" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Bar dataKey="swing" fill="#10b981" name="Swing" />
                    <Bar dataKey="intraday" fill="#3b82f6" name="Intraday" />
                    <Bar dataKey="scalping" fill="#8b5cf6" name="Scalping" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="text-center text-trading-muted">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg">No performance data available</p>
                    <p className="text-sm mt-1">Connect to trading data feed to see monthly P&L trends</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Strategy Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                {strategyDistribution.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={strategyDistribution}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={120}
                        dataKey="value"
                      >
                        {strategyDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-trading-muted">
                      <PieChartIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No distribution data available</p>
                      <p className="text-sm mt-1">Connect to strategy data feed</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Category Performance</CardTitle>
              </CardHeader>
              <CardContent>
                {strategyDistribution.length > 0 ? (
                  <div className="space-y-4">
                    {strategyDistribution.map((category, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-4 h-4 rounded-full" 
                            style={{ backgroundColor: category.color }}
                          ></div>
                          <span className="text-trading-light">{category.name}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-trading-light font-medium">{category.value} strategies</div>
                          <div className="text-xs text-trading-muted">
                            {((category.value / strategyDistribution.reduce((sum, c) => sum + c.value, 0)) * 100).toFixed(1)}% of total
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="h-[300px] flex items-center justify-center">
                    <div className="text-center text-trading-muted">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No category data available</p>
                      <p className="text-sm mt-1">Connect to strategy data feed</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rankings">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Top Performing Strategies</CardTitle>
            </CardHeader>
            <CardContent>
              {topPerformers.length > 0 ? (
                <div className="space-y-3">
                  <div className="grid grid-cols-6 gap-4 text-xs text-trading-muted font-medium border-b border-trading-border pb-2">
                    <div>Rank</div>
                    <div>Strategy</div>
                    <div>Category</div>
                    <div>Win Rate</div>
                    <div>P&L</div>
                    <div>Trades</div>
                  </div>
                  {topPerformers.map((performer, index) => (
                    <div key={index} className="grid grid-cols-6 gap-4 text-xs items-center p-2 bg-trading-dark rounded border border-trading-border">
                      <div className="text-trading-light font-bold">#{index + 1}</div>
                      <div className="text-trading-light font-medium">{performer.strategy}</div>
                      <Badge variant="outline" className={`text-xs ${getCategoryColor(performer.category)}`}>
                        {performer.category}
                      </Badge>
                      <div className="text-green-400">{performer.winRate}%</div>
                      <div className="text-green-400">₹{performer.pnl.toLocaleString()}</div>
                      <div className="text-trading-light">{performer.trades}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-trading-muted">No ranking data available</p>
                  <p className="text-sm text-trading-muted mt-1">Connect to strategy performance data feed</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Trading Calendar & Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 mx-auto mb-4 text-trading-muted opacity-50" />
                <div className="text-lg text-trading-muted">Trading Calendar View</div>
                <div className="text-sm text-trading-muted mt-2">Strategy performance by date and time - connect to trading data feed</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
