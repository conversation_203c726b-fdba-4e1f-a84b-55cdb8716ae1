
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Brain, Settings, FileText, Activity } from "lucide-react";
import { useState } from "react";

interface AnalysisMetrics {
  analysisQuality: number;
  reportsGenerated: number;
  dataSources: number;
  processingSpeed: number;
}

interface AnalysisAgentProps {
  isActive?: boolean;
  onToggle?: (active: boolean) => void;
  metrics?: AnalysisMetrics;
  onAnalysisDepthChange?: (depth: number) => void;
  onUpdateFrequencyChange?: (frequency: number) => void;
}

export const AnalysisAgent = ({
  isActive: propIsActive,
  onToggle,
  metrics,
  onAnalysisDepthChange,
  onUpdateFrequencyChange
}: AnalysisAgentProps) => {
  const [localIsActive, setLocalIsActive] = useState(true);
  const [analysisDepth, setAnalysisDepth] = useState([7]);
  const [updateFreq, setUpdateFreq] = useState([5]);

  const isActive = propIsActive !== undefined ? propIsActive : localIsActive;

  const handleToggle = (active: boolean) => {
    if (onToggle) {
      onToggle(active);
    } else {
      setLocalIsActive(active);
    }
  };

  const handleAnalysisDepthChange = (value: number[]) => {
    setAnalysisDepth(value);
    if (onAnalysisDepthChange) {
      onAnalysisDepthChange(value[0]);
    }
  };

  const handleUpdateFreqChange = (value: number[]) => {
    setUpdateFreq(value);
    if (onUpdateFrequencyChange) {
      onUpdateFrequencyChange(value[0]);
    }
  };

  return (
    <Card className="bg-trading-darker border-trading-border">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <Brain className="h-5 w-5 mr-2 text-indigo-400" />
            Market Analysis Agent
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <Badge variant="outline" className={isActive ? "text-green-400 border-green-400" : "text-red-400 border-red-400"}>
              {isActive ? "Analyzing" : "Idle"}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Analysis Quality</div>
            <div className="text-2xl font-bold text-blue-400">
              {metrics ? `${metrics.analysisQuality}%` : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Reports Generated</div>
            <div className="text-2xl font-bold text-trading-light">
              {metrics ? metrics.reportsGenerated : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Data Sources</div>
            <div className="text-2xl font-bold text-trading-light">
              {metrics ? metrics.dataSources : '--'}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm text-trading-muted">Processing Speed</div>
            <div className="text-2xl font-bold text-green-400">
              {metrics ? `${metrics.processingSpeed}s` : '--'}
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-4 border-t border-trading-border">
          <div className="flex items-center justify-between">
            <span className="text-sm text-trading-light">Analysis Engine</span>
            <Switch checked={isActive} onCheckedChange={handleToggle} />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Analysis Depth</span>
              <span className="text-sm text-trading-muted">{analysisDepth[0]}/10</span>
            </div>
            <Slider
              value={analysisDepth}
              onValueChange={handleAnalysisDepthChange}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-trading-light">Update Frequency</span>
              <span className="text-sm text-trading-muted">{updateFreq[0]} min</span>
            </div>
            <Slider
              value={updateFreq}
              onValueChange={handleUpdateFreqChange}
              max={60}
              min={1}
              step={1}
              className="w-full"
            />
          </div>
        </div>

        <div className="flex space-x-2 pt-4">
          <Button size="sm" variant="outline" className="flex-1">
            <Settings className="h-4 w-4 mr-2" />
            Models
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <FileText className="h-4 w-4 mr-2" />
            Reports
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Activity className="h-4 w-4 mr-2" />
            Insights
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
