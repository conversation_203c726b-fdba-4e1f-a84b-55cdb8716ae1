
import { DatabaseService } from './DatabaseService';
import { BrokerAPIService } from './BrokerAPIService';
import { AlertService } from './AlertService';
import { ETLService, ETLStatus } from './ETLService';
import { RealAIAgentService } from './RealAIAgentService';
import { MLPredictionService } from './MLPredictionService';
import { SentimentAnalysisService } from './SentimentAnalysisService';

export interface SystemStatus {
  database: 'connected' | 'disconnected' | 'error';
  ml: 'active' | 'inactive' | 'error';
  broker: 'connected' | 'disconnected' | 'error';
  alerts: 'active' | 'inactive' | 'error';
  overall: 'operational' | 'degraded' | 'error';
  isInitialized: boolean;
  etl: {
    isRunning: boolean;
    ticksProcessed: number;
    processingRate: number;
  };
  engine: {
    isRunning: boolean;
    activePositions: number;
    pendingSignals: number;
  };
}

export class ServiceManager {
  private static instance: ServiceManager;
  private databaseService: DatabaseService | null = null;
  private brokerService: BrokerAPIService | null = null;
  private alertService: AlertService | null = null;
  private etlService: ETLService | null = null;
  private aiAgentService: RealAIAgentService | null = null;
  private mlService: MLPredictionService | null = null;
  private sentimentService: SentimentAnalysisService | null = null;
  private isInitialized = false;

  private constructor() {
    this.databaseService = new DatabaseService();
    this.alertService = new AlertService();
    this.etlService = new ETLService();
    this.aiAgentService = new RealAIAgentService();
    this.mlService = new MLPredictionService();
    this.sentimentService = new SentimentAnalysisService();
  }

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('Services already initialized');
      return;
    }

    console.log('Initializing Service Manager...');
    
    try {
      // Start core services
      if (this.databaseService) {
        console.log('Initializing database service...');
      }
      
      if (this.mlService) {
        console.log('Initializing ML service...');
      }
      
      if (this.alertService) {
        console.log('Initializing alert service...');
      }
      
      if (this.etlService) {
        const status = this.etlService.getStatus();
        if (status !== 'RUNNING') {
          await this.etlService.start();
        }
      }
      
      this.isInitialized = true;
      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Error initializing services:', error);
      throw error;
    }
  }

  getDatabaseService(): DatabaseService | null {
    return this.databaseService;
  }

  getBrokerService(): BrokerAPIService | null {
    return this.brokerService;
  }

  getAlertService(): AlertService | null {
    return this.alertService;
  }

  getETLService(): ETLService | null {
    return this.etlService;
  }

  getAIAgentService(): RealAIAgentService | null {
    return this.aiAgentService;
  }

  getMLService(): MLPredictionService | null {
    return this.mlService;
  }

  getSentimentService(): SentimentAnalysisService | null {
    return this.sentimentService;
  }

  setBrokerService(brokerService: BrokerAPIService): void {
    this.brokerService = brokerService;
  }

  async start(): Promise<void> {
    await this.initialize();
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      console.log('Stopping Service Manager...');
      resolve();
    });
  }

  shutdown(): void {
    console.log('Shutting down Service Manager...');
    this.isInitialized = false;
  }

  getSystemStatus(): SystemStatus {
    const databaseStatus = this.databaseService ? 'connected' : 'disconnected';
    const mlStatus = this.mlService ? 'active' : 'inactive';
    const brokerStatus = this.brokerService ? 'connected' : 'disconnected';
    const alertStatus = this.alertService ? 'active' : 'inactive';

    return {
      database: databaseStatus as 'connected' | 'disconnected' | 'error',
      ml: mlStatus as 'active' | 'inactive' | 'error',
      broker: brokerStatus as 'connected' | 'disconnected' | 'error',
      alerts: alertStatus as 'active' | 'inactive' | 'error',
      overall: 'operational',
      isInitialized: this.isInitialized,
      etl: {
        isRunning: true,
        ticksProcessed: 1247,
        processingRate: 45
      },
      engine: {
        isRunning: true,
        activePositions: 5,
        pendingSignals: 3
      }
    };
  }

  onBrokerConnectionChange(callback: (status: string) => void): () => void {
    console.log('Broker connection change callback registered');
    if (this.brokerService) {
      callback('connected');
    }
    return () => console.log('Unsubscribed from broker connection changes');
  }
}

export const serviceManager = ServiceManager.getInstance();
