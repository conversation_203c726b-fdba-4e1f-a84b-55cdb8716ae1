
export interface TickData {
  symbol: string;
  timestamp: string;
  ltp: number;
  volume: number;
  vwap: number;
  bid: number;
  ask: number;
  high: number;
  low: number;
  open: number;
  close: number; // Added missing property
}

export interface FinancialData {
  symbol: string;
  date: string;
  roce: number;
  roe: number;
  eps: number;
  salesGrowth: number;
  debtEquity: number;
  mfHolding: number;
  revenue: number;
  netProfit: number;
  bookValue: number;
}

export interface OrderFlowData {
  symbol: string;
  timestamp: string;
  buyVolume: number;
  sellVolume: number;
  deltaVolume: number;
  largeOrders: Array<{
    price: number;
    quantity: number;
    side: 'BUY' | 'SELL';
    timestamp: string;
  }>;
}

export interface TradeRecord {
  symbol: string;
  action: 'BUY' | 'SELL';
  price: number;
  quantity: number;
  strategy: string;
  confidence: number;
  reasoning: string;
  timestamp: string;
  pnl?: number;
}

export class DatabaseService {
  private cache: Map<string, any> = new Map();
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();

  // Store tick data (mock InfluxDB behavior)
  async storeTick(data: TickData): Promise<void> {
    const key = `tick_${data.symbol}_${data.timestamp}`;
    this.cache.set(key, data);
    
    // Notify subscribers
    this.notifySubscribers(`tick_${data.symbol}`, data);
  }

  // Store financial data (mock PostgreSQL behavior)
  async storeFinancials(data: FinancialData): Promise<void> {
    const key = `financials_${data.symbol}_${data.date}`;
    this.cache.set(key, data);
    
    // Notify subscribers
    this.notifySubscribers(`financials_${data.symbol}`, data);
  }

  // Store order flow data
  async storeOrderFlow(data: OrderFlowData): Promise<void> {
    const key = `orderflow_${data.symbol}_${data.timestamp}`;
    this.cache.set(key, data);
    
    // Notify subscribers
    this.notifySubscribers(`orderflow_${data.symbol}`, data);
  }

  // Log trade data
  async logTrade(data: TradeRecord): Promise<void> {
    const key = `trade_${data.symbol}_${data.timestamp}`;
    this.cache.set(key, data);
    
    // Store in trades collection
    const tradesKey = 'all_trades';
    const existingTrades = this.cache.get(tradesKey) || [];
    existingTrades.push(data);
    this.cache.set(tradesKey, existingTrades);
  }

  // Get recent trades
  async getRecentTrades(count: number = 100): Promise<TradeRecord[]> {
    const trades = this.cache.get('all_trades') || [];
    return trades.slice(-count);
  }

  // Get latest tick data
  async getLatestTick(symbol: string): Promise<TickData | null> {
    const keys = Array.from(this.cache.keys()).filter(k => k.startsWith(`tick_${symbol}`));
    if (keys.length === 0) return null;
    
    const latestKey = keys.sort().pop();
    return this.cache.get(latestKey!) || null;
  }

  // Get historical ticks
  async getHistoricalTicks(symbol: string, from: string, to: string): Promise<TickData[]> {
    const keys = Array.from(this.cache.keys())
      .filter(k => k.startsWith(`tick_${symbol}`))
      .filter(k => {
        const timestamp = k.split('_')[2];
        return timestamp >= from && timestamp <= to;
      });
    
    return keys.map(k => this.cache.get(k)).filter(Boolean);
  }

  // Get financial data
  async getFinancials(symbol: string, years: number = 5): Promise<FinancialData[]> {
    const keys = Array.from(this.cache.keys())
      .filter(k => k.startsWith(`financials_${symbol}`))
      .slice(-years);
    
    return keys.map(k => this.cache.get(k)).filter(Boolean);
  }

  // Get order flow data
  async getOrderFlow(symbol: string, timeframe: string = '1h'): Promise<OrderFlowData[]> {
    const keys = Array.from(this.cache.keys())
      .filter(k => k.startsWith(`orderflow_${symbol}`));
    
    return keys.map(k => this.cache.get(k)).filter(Boolean);
  }

  // Subscribe to data updates
  subscribe(channel: string, callback: (data: any) => void): void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set());
    }
    this.subscribers.get(channel)!.add(callback);
  }

  // Unsubscribe from data updates
  unsubscribe(channel: string, callback: (data: any) => void): void {
    if (this.subscribers.has(channel)) {
      this.subscribers.get(channel)!.delete(callback);
    }
  }

  // Notify subscribers
  private notifySubscribers(channel: string, data: any): void {
    if (this.subscribers.has(channel)) {
      this.subscribers.get(channel)!.forEach(callback => callback(data));
    }
  }

  // Calculate derived features
  async calculateVWAP(symbol: string, period: number = 20): Promise<number> {
    const ticks = await this.getHistoricalTicks(
      symbol, 
      new Date(Date.now() - period * 60000).toISOString(),
      new Date().toISOString()
    );
    
    if (ticks.length === 0) return 0;
    
    const totalVolumePrice = ticks.reduce((sum, tick) => sum + (tick.ltp * tick.volume), 0);
    const totalVolume = ticks.reduce((sum, tick) => sum + tick.volume, 0);
    
    return totalVolume > 0 ? totalVolumePrice / totalVolume : 0;
  }

  // Calculate RSI
  async calculateRSI(symbol: string, period: number = 14): Promise<number> {
    const ticks = await this.getHistoricalTicks(
      symbol,
      new Date(Date.now() - period * 2 * 60000).toISOString(),
      new Date().toISOString()
    );
    
    if (ticks.length < period + 1) return 50;
    
    const gains: number[] = [];
    const losses: number[] = [];
    
    for (let i = 1; i < ticks.length; i++) {
      const change = ticks[i].ltp - ticks[i - 1].ltp;
      if (change > 0) {
        gains.push(change);
        losses.push(0);
      } else {
        gains.push(0);
        losses.push(Math.abs(change));
      }
    }
    
    const avgGain = gains.slice(-period).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(-period).reduce((a, b) => a + b, 0) / period;
    
    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }
}
