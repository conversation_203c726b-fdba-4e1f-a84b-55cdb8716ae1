
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { PieChart, TrendingUp, Brain, Target, BarChart3, Zap } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

export const SectorAnalysisDashboard = () => {
  const [selectedSector, setSelectedSector] = useState("Banking");

  const sectorPerformance = [
    { sector: "IT", performance: 8.5, momentum: 92, aiScore: 88, nextCycle: "Bullish" },
    { sector: "Banking", performance: 6.2, momentum: 78, aiScore: 85, nextCycle: "Neutral" },
    { sector: "Pharma", performance: 12.3, momentum: 95, aiScore: 92, nextCycle: "Bullish" },
    { sector: "Auto", performance: -2.1, momentum: 45, aiScore: 62, nextCycle: "Bearish" },
    { sector: "FMCG", performance: 4.8, momentum: 68, aiScore: 74, nextCycle: "Neutral" },
    { sector: "Energy", performance: 15.7, momentum: 98, aiScore: 95, nextCycle: "Bullish" },
  ];

  const rotationData = [
    { month: 'Jan', IT: 85, Banking: 70, Pharma: 60, Auto: 45, Energy: 40 },
    { month: 'Feb', IT: 88, Banking: 72, Pharma: 65, Auto: 42, Energy: 45 },
    { month: 'Mar', IT: 82, Banking: 75, Pharma: 70, Auto: 38, Energy: 52 },
    { month: 'Apr', IT: 90, Banking: 78, Pharma: 75, Auto: 35, Energy: 60 },
    { month: 'May', IT: 92, Banking: 80, Pharma: 82, Auto: 40, Energy: 68 },
    { month: 'Jun', IT: 89, Banking: 82, Pharma: 88, Auto: 45, Energy: 75 },
  ];

  const thematicOpportunities = [
    { theme: "Digital Transformation", growth: "+45%", confidence: "High", stocks: ["TCS", "INFY", "HCLTECH"] },
    { theme: "Green Energy", growth: "+78%", confidence: "Very High", stocks: ["ADANIGREEN", "TATAPOWER", "SUZLON"] },
    { theme: "Healthcare Innovation", growth: "+32%", confidence: "High", stocks: ["DRREDDY", "SUNPHARMA", "BIOCON"] },
    { theme: "EV Revolution", growth: "+125%", confidence: "Medium", stocks: ["TATAMOTORS", "MAHINDRA", "BAJAJFINSV"] },
    { theme: "Fintech Adoption", growth: "+89%", confidence: "High", stocks: ["HDFCBANK", "KOTAKBANK", "BAJFINANCE"] },
  ];

  const aiInsights = {
    Banking: {
      nextCycle: "Credit cycle recovery expected in Q2 FY25. Interest rate normalization will boost NIMs.",
      stockPicks: ["HDFCBANK", "ICICIBANK", "KOTAKBANK"],
      risks: "Asset quality concerns in unsecured lending segment",
      timeline: "6-9 months",
      confidence: "85%"
    },
    IT: {
      nextCycle: "AI/Cloud transformation driving next growth phase. Margin expansion likely.",
      stockPicks: ["TCS", "INFY", "WIPRO"],
      risks: "Client discretionary spending still weak",
      timeline: "3-6 months", 
      confidence: "88%"
    },
    Pharma: {
      nextCycle: "US FDA approvals accelerating. India domestic growth strong.",
      stockPicks: ["DRREDDY", "SUNPHARMA", "LUPIN"],
      risks: "Pricing pressure in US generics",
      timeline: "12-18 months",
      confidence: "92%"
    }
  };

  const radarData = [
    { sector: 'Valuation', IT: 78, Banking: 85, Pharma: 72, Auto: 65, Energy: 88 },
    { sector: 'Growth', IT: 82, Banking: 75, Pharma: 88, Auto: 45, Energy: 92 },
    { sector: 'Quality', IT: 90, Banking: 80, Pharma: 85, Auto: 70, Energy: 75 },
    { sector: 'Momentum', IT: 85, Banking: 72, Pharma: 90, Auto: 55, Energy: 95 },
    { sector: 'Sentiment', IT: 88, Banking: 78, Pharma: 82, Auto: 62, Energy: 90 },
  ];

  return (
    <div className="space-y-6">
      {/* Sector Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <PieChart className="h-8 w-8 mx-auto mb-2 text-blue-400" />
            <div className="text-2xl font-bold text-blue-400">12</div>
            <div className="text-sm text-trading-muted">Sectors Tracked</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-400" />
            <div className="text-2xl font-bold text-green-400">7</div>
            <div className="text-sm text-trading-muted">Bullish Sectors</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <Brain className="h-8 w-8 mx-auto mb-2 text-purple-400" />
            <div className="text-2xl font-bold text-purple-400">89%</div>
            <div className="text-sm text-trading-muted">AI Accuracy</div>
          </CardContent>
        </Card>
        <Card className="bg-trading-darker border-trading-border">
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-400" />
            <div className="text-2xl font-bold text-yellow-400">5</div>
            <div className="text-sm text-trading-muted">Hot Themes</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="rotation" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-trading-darker">
          <TabsTrigger value="rotation">Sector Rotation</TabsTrigger>
          <TabsTrigger value="themes">Thematic Analysis</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
          <TabsTrigger value="radar">Sector Radar</TabsTrigger>
        </TabsList>

        <TabsContent value="rotation">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Sector Rotation Timeline</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={rotationData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="month" stroke="#9ca3af" />
                    <YAxis stroke="#9ca3af" />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1f2937', 
                        border: '1px solid #374151',
                        borderRadius: '8px'
                      }}
                    />
                    <Line type="monotone" dataKey="IT" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="Banking" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="Pharma" stroke="#8b5cf6" strokeWidth={2} />
                    <Line type="monotone" dataKey="Energy" stroke="#f59e0b" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light">Sector Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sectorPerformance.map((sector, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-trading-dark rounded border border-trading-border">
                      <div className="flex items-center space-x-4">
                        <div className="text-trading-light font-medium">{sector.sector}</div>
                        <Badge variant="outline" className={
                          sector.nextCycle === "Bullish" ? "text-green-400 border-green-400" : 
                          sector.nextCycle === "Bearish" ? "text-red-400 border-red-400" : 
                          "text-yellow-400 border-yellow-400"
                        }>
                          {sector.nextCycle}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${sector.performance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {sector.performance >= 0 ? '+' : ''}{sector.performance}%
                        </div>
                        <div className="text-xs text-trading-muted">AI Score: {sector.aiScore}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="themes">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Thematic Investment Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {thematicOpportunities.map((theme, index) => (
                  <Card key={index} className="bg-trading-dark border-trading-border">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-trading-light font-medium">{theme.theme}</h3>
                        <Badge variant="outline" className={
                          theme.confidence === "Very High" ? "text-green-400 border-green-400" :
                          theme.confidence === "High" ? "text-blue-400 border-blue-400" :
                          "text-yellow-400 border-yellow-400"
                        }>
                          {theme.confidence}
                        </Badge>
                      </div>
                      <div className="text-2xl font-bold text-green-400 mb-2">{theme.growth}</div>
                      <div className="text-sm text-trading-muted mb-3">Expected Growth</div>
                      <div className="flex flex-wrap gap-1">
                        {theme.stocks.map((stock, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {stock}
                          </Badge>
                        ))}
                      </div>
                      <Button size="sm" className="w-full mt-3" variant="outline">
                        <Brain className="h-3 w-3 mr-1" />
                        AI Analysis
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-insights">
          <div className="space-y-4">
            <div className="flex gap-2 mb-4">
              {Object.keys(aiInsights).map((sector) => (
                <Button
                  key={sector}
                  variant={selectedSector === sector ? "default" : "outline"}
                  onClick={() => setSelectedSector(sector)}
                  size="sm"
                >
                  {sector}
                </Button>
              ))}
            </div>
            
            <Card className="bg-trading-darker border-trading-border">
              <CardHeader>
                <CardTitle className="text-trading-light flex items-center">
                  <Brain className="h-5 w-5 mr-2" />
                  AI 360° Analysis: {selectedSector}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="p-4 bg-blue-900/20 border border-blue-500/50 rounded">
                    <h3 className="text-blue-400 font-medium mb-2">Next Cycle Prediction</h3>
                    <p className="text-trading-light">{aiInsights[selectedSector as keyof typeof aiInsights].nextCycle}</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 bg-green-900/20 border border-green-500/50 rounded">
                      <h3 className="text-green-400 font-medium mb-2">AI Stock Picks</h3>
                      <div className="space-y-1">
                        {aiInsights[selectedSector as keyof typeof aiInsights].stockPicks.map((stock, index) => (
                          <Badge key={index} variant="outline" className="mr-2">
                            {stock}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="p-4 bg-red-900/20 border border-red-500/50 rounded">
                      <h3 className="text-red-400 font-medium mb-2">Key Risks</h3>
                      <p className="text-trading-light text-sm">{aiInsights[selectedSector as keyof typeof aiInsights].risks}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-400">
                        {aiInsights[selectedSector as keyof typeof aiInsights].timeline}
                      </div>
                      <div className="text-sm text-trading-muted">Expected Timeline</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-400">
                        {aiInsights[selectedSector as keyof typeof aiInsights].confidence}
                      </div>
                      <div className="text-sm text-trading-muted">AI Confidence</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="radar">
          <Card className="bg-trading-darker border-trading-border">
            <CardHeader>
              <CardTitle className="text-trading-light">Multi-Factor Sector Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="sector" />
                  <PolarRadiusAxis angle={54} domain={[0, 100]} />
                  <Radar name="IT" dataKey="IT" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.1} />
                  <Radar name="Banking" dataKey="Banking" stroke="#10b981" fill="#10b981" fillOpacity={0.1} />
                  <Radar name="Pharma" dataKey="Pharma" stroke="#8b5cf6" fill="#8b5cf6" fillOpacity={0.1} />
                  <Radar name="Energy" dataKey="Energy" stroke="#f59e0b" fill="#f59e0b" fillOpacity={0.1} />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
