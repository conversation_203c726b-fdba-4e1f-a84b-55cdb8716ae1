
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { EnhancedCard, TradingCard } from "@/components/ui/enhanced-card";
import { 
  ComposedChart, 
  Line, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  ReferenceLine 
} from "recharts";
import { TrendingUp, Volume, Target, Activity } from "lucide-react";
import { cn } from "@/lib/utils";

interface CandlestickData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  vwap?: number;
}

interface TradingChartProps {
  symbol?: string;
  timeframe?: string;
  data?: CandlestickData[];
  currentPrice?: number;
  dayChange?: number;
  dayChangePercent?: number;
  volume?: number;
  onSymbolSelect?: (symbol: string) => void;
  onTimeframeChange?: (timeframe: string) => void;
}

export const TradingChart = ({
  symbol = "NIFTY",
  timeframe = "5m",
  data = [],
  currentPrice,
  dayChange,
  dayChangePercent,
  volume,
  onSymbolSelect,
  onTimeframeChange
}: TradingChartProps) => {
  const [indicators, setIndicators] = useState({
    vwap: true,
    rsi: false,
    macd: false,
    bollinger: true
  });

  const timeframes = ["1m", "5m", "15m", "1h", "4h", "1D"];

  const toggleIndicator = (indicator: keyof typeof indicators) => {
    setIndicators(prev => ({ ...prev, [indicator]: !prev[indicator] }));
  };

  const isPositive = dayChangePercent !== undefined && dayChangePercent >= 0;

  return (
    <EnhancedCard animation="slide-in" className="chart-enter">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-trading-light flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 pulse-glow" />
            <span className="text-xl font-bold">{symbol}</span>
            <span className="text-sm text-trading-muted ml-2">• {timeframe}</span>
          </CardTitle>
          <div className="flex space-x-2">
            {timeframes.map((tf) => (
              <Button
                key={tf}
                variant={tf === timeframe ? "default" : "outline"}
                size="sm"
                onClick={() => onTimeframeChange?.(tf)}
                className={cn(
                  "text-xs glass-button micro-scale",
                  tf === timeframe && "pulse-glow"
                )}
              >
                {tf}
              </Button>
            ))}
          </div>
        </div>
        
        {/* Enhanced price display */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-4">
            {currentPrice && (
              <div className="flex items-center space-x-2">
                <div className="text-3xl font-bold text-trading-light">
                  ₹{currentPrice.toLocaleString()}
                </div>
                {dayChangePercent !== undefined && (
                  <Badge 
                    variant="outline" 
                    className={cn(
                      "text-sm font-medium micro-scale",
                      isPositive 
                        ? "text-green-400 border-green-400 glass-status-positive price-flash-green" 
                        : "text-red-400 border-red-400 glass-status-negative price-flash-red"
                    )}
                  >
                    <Activity className="h-3 w-3 mr-1" />
                    {isPositive ? '+' : ''}{dayChangePercent.toFixed(2)}%
                  </Badge>
                )}
              </div>
            )}
          </div>
          
          {volume && (
            <TradingCard 
              title="Volume" 
              value={`${(volume / 1000000).toFixed(1)}M`}
              icon={<Volume className="h-4 w-4" />}
              className="w-fit"
            >
              <div className="text-xs text-trading-muted">
                Last 24h
              </div>
            </TradingCard>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Enhanced Indicator Controls */}
          <div className="flex flex-wrap gap-2 p-3 glass-card rounded-lg">
            <div className="text-xs text-trading-muted mr-2 flex items-center">
              <Target className="h-3 w-3 mr-1" />
              Indicators:
            </div>
            {Object.entries(indicators).map(([key, enabled]) => (
              <Button
                key={key}
                variant={enabled ? "default" : "outline"}
                size="sm"
                onClick={() => toggleIndicator(key as keyof typeof indicators)}
                className={cn(
                  "text-xs glass-button micro-scale",
                  enabled && "pulse-glow"
                )}
              >
                {key.toUpperCase()}
              </Button>
            ))}
          </div>

          {/* Enhanced Main Chart */}
          {data.length > 0 ? (
            <div className="glass-chart rounded-lg p-4 chart-update">
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={data}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" opacity={0.3} />
                  <XAxis 
                    dataKey="time" 
                    stroke="#9ca3af" 
                    fontSize={12}
                    tickLine={false}
                  />
                  <YAxis 
                    domain={['dataMin - 10', 'dataMax + 10']} 
                    stroke="#9ca3af" 
                    fontSize={12}
                    tickLine={false}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(26, 26, 26, 0.95)', 
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '8px',
                      backdropFilter: 'blur(10px)'
                    }}
                    formatter={(value: number, name: string) => [
                      `₹${value.toLocaleString()}`, 
                      name.toUpperCase()
                    ]}
                  />
                  
                  {/* VWAP Line with glow effect */}
                  {indicators.vwap && (
                    <Line 
                      type="monotone" 
                      dataKey="vwap" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={false}
                      name="VWAP"
                      filter="drop-shadow(0 0 6px #3b82f6)"
                    />
                  )}

                  {/* Enhanced Price line */}
                  <Line 
                    type="monotone" 
                    dataKey="close" 
                    stroke={isPositive ? "#10b981" : "#ef4444"} 
                    strokeWidth={3}
                    dot={false}
                    name="Price"
                    filter={`drop-shadow(0 0 8px ${isPositive ? "#10b981" : "#ef4444"})`}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-96 glass-chart rounded-lg flex items-center justify-center">
              <div className="text-center text-trading-muted bounce-in">
                <div className="shimmer-loading h-12 w-12 mx-auto mb-4 rounded-full"></div>
                <div className="text-lg">Connecting to market data...</div>
                <div className="text-sm mt-2">Real-time charts loading</div>
              </div>
            </div>
          )}

          {/* Enhanced Volume Chart */}
          {data.length > 0 && (
            <div className="glass-card rounded-lg p-4">
              <div className="text-sm text-trading-muted mb-2 flex items-center">
                <Volume className="h-4 w-4 mr-2" />
                Volume Profile
              </div>
              <ResponsiveContainer width="100%" height={100}>
                <ComposedChart data={data}>
                  <XAxis dataKey="time" stroke="#9ca3af" fontSize={10} />
                  <YAxis stroke="#9ca3af" fontSize={10} />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'rgba(26, 26, 26, 0.95)', 
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '8px'
                    }}
                  />
                  <Bar 
                    dataKey="volume" 
                    fill="url(#volumeGradient)" 
                    opacity={0.8}
                    radius={[2, 2, 0, 0]}
                  />
                  <defs>
                    <linearGradient id="volumeGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#6366f1" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#6366f1" stopOpacity={0.2}/>
                    </linearGradient>
                  </defs>
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </CardContent>
    </EnhancedCard>
  );
};
