
import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

export interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  bid: number;
  ask: number;
  vwap: number;
  timestamp: number;
}

export interface OrderBookLevel {
  price: number;
  size: number;
  orders: number;
}

export interface OrderBook {
  symbol: string;
  bids: OrderBookLevel[];
  asks: OrderBookLevel[];
  timestamp: number;
}

export interface MarketState {
  // Market Data
  marketData: Record<string, MarketData>;
  orderBooks: Record<string, OrderBook>;
  subscribedSymbols: Set<string>;
  
  // Connection Status
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  latency: number;
  
  // Actions
  updateMarketData: (symbol: string, data: MarketData) => void;
  updateOrderBook: (symbol: string, orderBook: OrderBook) => void;
  addSubscription: (symbol: string) => void;
  removeSubscription: (symbol: string) => void;
  setConnectionStatus: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void;
  setLatency: (latency: number) => void;
  clearData: () => void;
}

export const useMarketStore = create<MarketState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial State
      marketData: {},
      orderBooks: {},
      subscribedSymbols: new Set<string>(),
      isConnected: false,
      connectionStatus: 'disconnected',
      latency: 0,

      // Actions
      updateMarketData: (symbol: string, data: MarketData) => {
        set((state) => ({
          marketData: {
            ...state.marketData,
            [symbol]: data
          }
        }), false, 'updateMarketData');
      },

      updateOrderBook: (symbol: string, orderBook: OrderBook) => {
        set((state) => ({
          orderBooks: {
            ...state.orderBooks,
            [symbol]: orderBook
          }
        }), false, 'updateOrderBook');
      },

      addSubscription: (symbol: string) => {
        set((state) => ({
          subscribedSymbols: new Set([...state.subscribedSymbols, symbol])
        }), false, 'addSubscription');
      },

      removeSubscription: (symbol: string) => {
        const newSubscriptions = new Set(get().subscribedSymbols);
        newSubscriptions.delete(symbol);
        set({
          subscribedSymbols: newSubscriptions
        }, false, 'removeSubscription');
      },

      setConnectionStatus: (status) => {
        set({
          connectionStatus: status,
          isConnected: status === 'connected'
        }, false, 'setConnectionStatus');
      },

      setLatency: (latency: number) => {
        set({ latency }, false, 'setLatency');
      },

      clearData: () => {
        set({
          marketData: {},
          orderBooks: {},
          subscribedSymbols: new Set<string>(),
          isConnected: false,
          connectionStatus: 'disconnected',
          latency: 0
        }, false, 'clearData');
      }
    })),
    { name: 'market-store' }
  )
);
